apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-dynamic-config-disabled
  namespace: compliance-system
spec:
  id: "V-242399"
  title: "Kubernetes DynamicKubeletConfig must not be enabled."
  description: "Kubernetes allows a user to configure kubelets with dynamic configurations. When dynamic configuration is used, the kubelet will watch for changes to the configuration file. When changes are made, the kubelet will automatically restart. Allowing this capability bypasses access restrictions and authorizations. Using this capability, an attacker can lower the security posture of the kubelet, which includes allowing the ability to run arbitrary commands in any container running on that node."
  checkText: "This check is only applicable for Kubernetes versions 1.25 and older.  \n\nOn the Control Plane, change to the manifests' directory at /etc/kubernetes/manifests and run the command:\ngrep -i feature-gates *\n\nIn each manifest file, if the feature-gates does not exist, or does not contain the \"DynamicKubeletConfig\" flag, or sets the flag to \"true\", this is a finding.\n\nOn each Control Plane and Worker node, run the command:\nps -ef | grep kubelet\n\nVerify the \"feature-gates\" option is not present.\n\nNote the path to the config file (identified by --config).\n\nInspect the content of the config file:\nIf the \"featureGates\" setting is not present, or does not contain the \"DynamicKubeletConfig\", or sets the flag to \"true\", this is a finding."
  fixText: "This fix is only applicable to Kubernetes version 1.25 and older.\n\nOn the Control Plane, change to the manifests' directory at /etc/kubernetes/manifests and run the command:\ngrep -i feature-gates *\n\nEdit the manifest files so that every manifest has a \"--feature-gates\" setting with \"DynamicKubeletConfig=false\".\n\nOn each Control Plane and Worker Node, run the command:\nps -ef | grep kubelet\n\nRemove the \"feature-gates\" option if present.\n\nNote the path to the config file (identified by --config).\n\nEdit the config file: \nAdd a \"featureGates\" setting if one does not yet exist. Add the feature gate \"DynamicKubeletConfig=false\".\n\nRestart the kubelet service using the following command:\nsystemctl daemon-reload && systemctl restart kubelet"
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting Kubernetes DynamicKubeletConfig disabled check (V-242399)"
    echo "This check is only applicable for Kubernetes versions 1.25 and older"
    
    # Check Kubernetes version to determine applicability
    echo "Checking Kubernetes version..."
    
    # Try to get Kubernetes version from kubelet
    kubelet_version=$(chroot /host kubelet --version 2>/dev/null | grep -oP 'Kubernetes v\K[0-9]+\.[0-9]+' || echo "")
    
    if [[ -n "$kubelet_version" ]]; then
        echo "Found Kubernetes version: v$kubelet_version"
        
        # Parse version numbers
        major_version=$(echo "$kubelet_version" | cut -d. -f1)
        minor_version=$(echo "$kubelet_version" | cut -d. -f2)
        
        # Check if version is 1.26 or newer (DynamicKubeletConfig removed)
        if [[ $major_version -gt 1 ]] || [[ $major_version -eq 1 && $minor_version -ge 26 ]]; then
            echo "INFO: Kubernetes v$kubelet_version detected"
            echo "DynamicKubeletConfig feature was removed in Kubernetes 1.26+"
            echo "This check is not applicable for this version"
            echo "PASS: DynamicKubeletConfig is not available in this Kubernetes version"
            exit 0
        fi
        
        echo "Kubernetes v$kubelet_version requires manual verification"
    else
        echo "Unable to determine Kubernetes version automatically"
    fi
    
    echo ""
    echo "MANUAL VERIFICATION REQUIRED:"
    echo "This check requires manual verification for Kubernetes 1.25 and older"
    echo ""
    echo "Manual verification steps:"
    echo "1. Check Control Plane manifests:"
    echo "   cd /etc/kubernetes/manifests"
    echo "   grep -i feature-gates *"
    echo "   Verify each manifest has '--feature-gates' with 'DynamicKubeletConfig=false'"
    echo ""
    echo "2. Check kubelet process arguments:"
    echo "   chroot /host ps -ef | grep kubelet"
    echo "   Verify 'feature-gates' option is not present in process arguments"
    echo ""
    echo "3. Check kubelet configuration file:"
    echo "   Find config file path from kubelet process (--config parameter)"
    echo "   Verify 'featureGates' setting contains 'DynamicKubeletConfig=false'"
    echo ""
    echo "FINDING CRITERIA:"
    echo "- If feature-gates does not exist in manifests"
    echo "- If DynamicKubeletConfig flag is missing or set to 'true'"
    echo "- If featureGates setting in config is missing or sets flag to 'true'"
    exit 2