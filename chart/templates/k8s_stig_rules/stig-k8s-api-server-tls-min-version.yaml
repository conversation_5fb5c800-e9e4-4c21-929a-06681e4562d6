apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-tls-min-version
  namespace: compliance-system
spec:
  id: "V-242378"
  title: "The Kubernetes API Server must use TLS 1.2, at a minimum, to protect the confidentiality of sensitive data during electronic dissemination."
  description: "The Kubernetes API Server will prohibit the use of SSL and unauthorized versions of TLS protocols to properly secure communication.\n\nThe use of unsupported protocol exposes vulnerabilities to the Kubernetes by rogue traffic interceptions, man-in-the-middle attacks, and impersonation of users or services from the container platform runtime, registry, and keystore. To enable the minimum version of TLS to be used by the Kubernetes API Server, the setting \"tls-min-version\" must be set."
  checkText: "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i tls-min-version * \n\nIf the setting \"tls-min-version\" is not configured in the Kubernetes API Server manifest file or it is set to \"VersionTLS10\" or \"VersionTLS11\", this is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of \"--tls-min-version\" to \"VersionTLS12\" or higher."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting API Server TLS minimum version check (V-242378)"
    
    # Set manifest file path
    manifest_path="/host/etc/kubernetes/manifests/kube-apiserver.yaml"
    
    echo "Checking API Server manifest file: $manifest_path"
    
    # Check if manifest file exists
    if [[ ! -f "$manifest_path" ]]; then
        echo "FAIL: API Server manifest missing at $manifest_path"
        echo "Expected to find kube-apiserver.yaml manifest file"
        exit 1
    fi
    
    echo "Found API Server manifest file"
    
    # Check TLS minimum version (preserving original logic)
    echo "Searching for --tls-min-version configuration..."
    tls_min_version=$(grep -oP -- "--tls-min-version=\K[^ ]+" "$manifest_path" 2>/dev/null)
    
    if [[ -z "$tls_min_version" ]]; then
        echo "FAIL: TLS minimum version not configured"
        echo "The --tls-min-version parameter must be explicitly set"
        exit 1
    fi
    
    echo "Found TLS minimum version configuration: $tls_min_version"
    
    # Validate TLS version (preserving original logic)
    if [[ "$tls_min_version" == "VersionTLS12" || "$tls_min_version" == "VersionTLS13" ]]; then
        echo "PASS: TLS minimum version correctly set to $tls_min_version"
        echo "Using secure TLS version (1.2 or higher)"
        exit 0
    else
        echo "FAIL: Incorrect TLS version configured: $tls_min_version"
        echo "TLS version must be VersionTLS12 or VersionTLS13"
        echo "Current setting is insecure and must be updated"
        exit 1
    fi 