apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-hostname-override-disabled
  namespace: compliance-system
spec:
  id: "V-242404"
  title: "Kubernetes Kubelet must deny hostname override."
  description: "Kubernetes allows for the overriding of hostnames. Allowing this feature to be implemented within the kubelets may break the TLS setup between the kubelet service and the API server. This setting also can make it difficult to associate logs with nodes if security analytics needs to take place. The better practice is to setup nodes with resolvable FQDNs and avoid overriding the hostnames."
  checkText: "On the Control Plane and Worker nodes, run the command:\nps -ef | grep kubelet\n\nIf the option \"--hostname-override\" is present, this is a finding."
  fixText: "Run the command:  \nsystemctl status kubelet.  \nNote the path to the drop-in file.\n\nDetermine the path to the environment file(s) with the command: \ngrep -i EnvironmentFile <path_to_drop_in_file>.\n\nRemove the \"--hostname-override\" option from any environment file where it is present.  \n\nRestart the kubelet service using the following command:\nsystemctl daemon-reload && systemctl restart kubelet"
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting kubelet hostname override disabled check (V-242404)"
    
    # Check kubelet process arguments (preserving original logic)
    echo "Checking kubelet process arguments..."
    kubelet_cmd=$(chroot /host ps -ef | grep kubelet | grep -v grep)
    
    if [[ -z "$kubelet_cmd" ]]; then
        echo "FAIL: Kubelet process not found"
        echo "Unable to locate running kubelet process"
        exit 1
    fi
    
    echo "Found kubelet process"
    echo "Checking for --hostname-override option..."
    
    # Check for --hostname-override in process arguments (preserving original logic)
    if echo "$kubelet_cmd" | grep -q -- "--hostname-override"; then
        echo "FAIL: --hostname-override found in kubelet arguments"
        echo "Kubelet process contains --hostname-override option"
        echo "This option should not be present as it may break TLS setup"
        echo "and make log association with nodes difficult"
        exit 1
    else
        echo "PASS: Hostname override properly disabled"
        echo "No --hostname-override option found in kubelet process arguments"
        echo "Kubelet is using proper hostname resolution"
        exit 0
    fi 