apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: test-aggregated-scan
  namespace: compliance-system
  annotations:
    compliance-operator.alauda.io/force-scan: "true"
    compliance-operator.alauda.io/job-mode: "aggregated"
spec:
  profile: test-profile-aggregated
  scanType: node
  nodeSelector:
    node-role.kubernetes.io/control-plane: ""
  nodeScopeStrategy: manual
  targetNodeRoles:
    - control-plane
---
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: test-profile-aggregated
  namespace: compliance-system
spec:
  id: "TEST-AGGREGATED-V1"
  title: "Test Profile for Aggregated Jobs"
  description: "A test profile to validate aggregated job functionality"
  version: "1.0"
  rules:
    - name: test-rule-file-permissions
    - name: test-rule-process-check
    - name: test-rule-network-check
---
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: test-rule-file-permissions
  namespace: compliance-system
spec:
  id: "TEST-FP-001"
  title: "Test File Permissions Check"
  description: "Check file permissions on control plane nodes"
  checkText: "Verify file permissions on control plane nodes"
  fixText: "Ensure proper file permissions are set"
  severity: medium
  checkType: node
  nodeScope: control-plane
  checkScript: |
    #!/bin/bash
    echo "=== Test Rule: File Permissions Check ==="
    echo "Node: $(hostname)"
    echo "Checking /etc/kubernetes directory permissions..."
    
    if [ -d "/host/etc/kubernetes" ]; then
        ls -la /host/etc/kubernetes/ | head -5
        echo "File permissions check completed successfully"
        exit 0
    elif [ -d "/etc/kubernetes" ]; then
        ls -la /etc/kubernetes/ | head -5
        echo "File permissions check completed successfully"
        exit 0
    else
        echo "Kubernetes config directory not found"
        exit 1
    fi
---
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: test-rule-process-check
  namespace: compliance-system
spec:
  id: "TEST-PC-001"
  title: "Test Process Check"
  description: "Check running processes on control plane nodes"
  checkText: "Verify required processes are running"
  fixText: "Start required processes if not running"
  severity: high
  checkType: node
  nodeScope: control-plane
  checkScript: |
    #!/bin/bash
    echo "=== Test Rule: Process Check ==="
    echo "Node: $(hostname)"
    echo "Checking for kube-apiserver process..."
    
    if pgrep -f kube-apiserver >/dev/null; then
        echo "kube-apiserver process found"
        pgrep -f kube-apiserver | head -3
        exit 0
    else
        echo "kube-apiserver process not found"
        exit 1
    fi
---
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: test-rule-network-check
  namespace: compliance-system
spec:
  id: "TEST-NC-001"
  title: "Test Network Check"
  description: "Check network configuration on control plane nodes"
  checkText: "Verify network configuration is correct"
  fixText: "Configure network settings properly"
  severity: low
  checkType: node
  nodeScope: control-plane
  checkScript: |
    #!/bin/bash
    echo "=== Test Rule: Network Check ==="
    echo "Node: $(hostname)"
    echo "Checking network interfaces..."
    
    if command -v ip >/dev/null 2>&1; then
        echo "Available network interfaces:"
        ip link show | grep -E "^[0-9]+:" | head -3
        exit 0
    elif command -v ifconfig >/dev/null 2>&1; then
        echo "Available network interfaces:"
        ifconfig | grep -E "^[a-z]" | head -3
        exit 0
    else
        echo "No network tools available"
        exit 1
    fi
