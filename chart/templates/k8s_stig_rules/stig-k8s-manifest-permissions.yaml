apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-manifest-permissions
  namespace: compliance-system
spec:
  id: "V-242408"
  title: "The Kubernetes manifest files must have least privileges."
  description: "Kubernetes manifest files must have restrictive file permissions to prevent unauthorized access."
  fixText: "On both Control Plane and Worker Nodes, change to the /etc/kubernetes/manifest directory. Run the command:\nchmod 644 *\n\nTo verify the change took place, run the command:\nls -l *\n\nAll the manifest files should now have privileges of \"644\"."
  checkText: "On both Control Plane and Worker Nodes, change to the /etc/kubernetes/manifest directory. Run the command:\nls -l *\n\nEach manifest file must have permissions \"644\" or more restrictive.\n\nIf any manifest file is less restrictive than \"644\", this is a finding."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting V-242408: Kubernetes manifest files permissions check"
    
    manifests_dir="/host/etc/kubernetes/manifests"
    failed_files=""
    has_failures=false
    
    echo "Checking manifest files in directory: $manifests_dir"
    
    if [[ ! -d "$manifests_dir" ]]; then
        echo "FAIL: Manifest directory not found: $manifests_dir"
        exit 1
    fi
    
    echo "Scanning manifest files for permission compliance..."
    
    # Check manifest file permissions
    while IFS= read -r -d $'\0' file; do
        perms=$(stat -c %a "$file" 2>/dev/null)
        echo "Checking file: $file (permissions: $perms)"
        
        if [[ "$perms" -gt 644 ]]; then
            echo "FAIL: Invalid permissions $perms for $file (should be ≤644)"
            failed_files+="${failed_files:+, }$(basename "$file"):$perms"
            has_failures=true
        else
            echo "PASS: Valid permissions $perms for $file"
        fi
    done < <(find "$manifests_dir" -type f -print0 2>/dev/null)
    
    if [[ "$has_failures" == true ]]; then
        echo "FAIL: Some manifest files have excessive permissions: $failed_files"
        exit 1
    else
        echo "PASS: All manifest files have correct permissions (≤644)"
        exit 0
    fi