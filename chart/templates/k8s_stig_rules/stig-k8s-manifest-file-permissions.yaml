apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-manifest-file-permissions
  namespace: compliance-system
spec:
  id: "V-242455"
  title: "The Kubernetes kubeadm.conf must have file permissions set to 644 or more restrictive."
  description: "The Kubernetes kubeadm.conf contains sensitive information regarding the cluster nodes configuration. If this file can be modified, the Kubernetes Platform Plane would be degraded or compromised for malicious intent. Many of the security settings within the document are implemented through this file."
  checkText: "Review the kubeadm.conf file :\n\nGet the path for kubeadm.conf by running:\nsystemctl status kubelet\n\nNote the configuration file installed by the kubeadm is written to\n(Default Location: /etc/systemd/system/kubelet.service.d/10-kubeadm.conf)\nstat -c %a  <kubeadm.conf path>\n\nIf the file has permissions more permissive than \"644\", this is a finding."
  fixText: "Change the permissions of kubeadm.conf to \"644\" by executing the command:\n\nchmod 644 <kubeadm.conf path>"
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting V-242455: Kubernetes kubeadm.conf file permissions check"
    
    kubeadm_conf="/host/usr/lib/systemd/system/kubelet.service.d/10-kubeadm.conf"
    
    echo "Checking kubeadm configuration file: $kubeadm_conf"
    
    if [[ -f "$kubeadm_conf" ]]; then
        echo "Kubeadm configuration file found"
        
        perm=$(stat -c '%a' "$kubeadm_conf" 2>/dev/null)
        echo "Current file permissions: $perm"
        
        if [[ $perm -gt 644 ]]; then
            echo "FAILED: Kubeadm.conf has excessive permissions: $perm (should be ≤644)"
            exit 1
        else
            echo "PASSED: Kubeadm.conf has compliant permissions: $perm"
            exit 0
        fi
    else
        echo "FAILED: Kubeadm configuration file not found at $kubeadm_conf"
        exit 1
    fi 