apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-ssh-service-stopped
  namespace: compliance-system
spec:
  id: "V-242394"
  title: "Kubernetes Worker Nodes must not have the sshd service enabled."
  description: "Worker Nodes are maintained and monitored by the Control Plane. Direct access and manipulation of the nodes must not take place by administrators. Worker nodes must be treated as immutable and updated via replacement rather than in-place upgrades."
  checkText: "Log in to each worker node. Verify that the sshd service is not enabled. To validate the service is not enabled, run the command:\n\nsystemctl is-enabled sshd.service\n\nIf the service sshd is enabled, this is a finding.\n\nNote: If console access is not available, SSH access can be attempted. If the worker nodes cannot be reached, this requirement is \"not a finding\"."
  fixText: "To disable the sshd service, run the command:\n\nchkconfig sshd off\n\nNote: If access to the worker node is through an SSH session, it is important to realize there are two requirements for disabling and stopping the sshd service that must be done during the same SSH session. Disabling the service must be performed first and then the service stopped to guarantee both settings can be made if the session is interrupted."
  severity: "medium"
  checkType: "node"
  nodeScope: "all"
  checkScript: |
    #!/bin/bash
    
    echo "Checking if SSH service is stopped on worker node..."
    
    # Check SSH service active status
    echo "Checking systemctl active status for sshd service..."
    ssh_active=$(systemctl is-active sshd 2>/dev/null)
    
    if [[ "$ssh_active" == "active" ]]; then
        echo "FAILED: SSH service is currently active on this worker node"
        echo "SSH service status: $ssh_active"
        echo "Worker nodes must not have SSH service running for security"
        exit 1
    else
        echo "PASSED: SSH service is properly stopped"
        echo "SSH service status: ${ssh_active:-inactive/not-found}"
        echo "Worker node SSH service is not running - compliant"
        exit 0
    fi