# NodeScopeStrategy 测试指南

本目录包含用于测试 NodeScopeStrategy 功能的基本示例。

## 📁 文件说明

### 基础测试资源
- `test-profile-basic.yaml` - 基础测试 Profile
- `test-rules-basic.yaml` - 包含不同 nodeScope 的测试 Rules
- `test-content-basic.yaml` - 基础测试 ProfileBundle
- `test-scans-nodescope-strategy.yaml` - 不同策略的测试 Scans

## 🎯 测试场景

### 1. Auto Strategy (自动策略)
**文件**: `test-scan-auto-strategy`
**行为**: 根据 Rule 的 `nodeScope` 自动创建相应的 Jobs
**预期结果**:
- `control-plane` scope 的 rule 只在 control-plane 节点运行
- `worker` scope 的 rule 只在 worker 节点运行  
- `all` scope 的 rule 在所有节点运行
- `platform` scope 的 rule 创建 platform job

### 2. Manual Strategy (手动策略)
**文件**: `test-scan-manual-strategy`
**行为**: 使用传统的 `nodeSelector`，忽略 Rule 的 `nodeScope`
**预期结果**:
- 所有 Rules 在所有匹配 `nodeSelector` 的节点上运行
- 忽略 Rule 的 `nodeScope` 设置

### 3. Strict Strategy (严格策略)
**文件**: `test-scan-strict-strategy`
**行为**: 验证 Rule 的 `nodeScope` 与 Scan 的 `targetNodeRoles` 兼容性
**预期结果**:
- 只有兼容的 Rules 会创建 Jobs
- 不兼容的 Rules 会导致 Scan 失败

### 4. 特定节点角色测试
**文件**: `test-scan-control-plane-only`, `test-scan-worker-only`
**行为**: 测试只扫描特定角色节点的场景

## 🚀 部署和测试步骤

### 1. 部署测试资源
```bash
# 部署所有测试资源
kubectl apply -f examples/test-content-basic.yaml
kubectl apply -f examples/test-profile-basic.yaml
kubectl apply -f examples/test-rules-basic.yaml

# 验证资源创建
kubectl get profilebundle,profile,rule -n compliance-system
```

### 2. 测试 Auto Strategy
```bash
# 创建 auto strategy scan
kubectl apply -f examples/test-scans-nodescope-strategy.yaml

# 查看 scan 状态
kubectl get scan test-scan-auto-strategy -n compliance-system -o yaml

# 查看创建的 jobs
kubectl get jobs -n compliance-system -l compliance-operator.alauda.io/scan=test-scan-auto-strategy

# 查看 job 的节点分配
kubectl get jobs -n compliance-system -l compliance-operator.alauda.io/scan=test-scan-auto-strategy -o wide
```

### 3. 测试 Manual Strategy
```bash
# 查看 manual strategy scan
kubectl get scan test-scan-manual-strategy -n compliance-system -o yaml

# 查看创建的 jobs
kubectl get jobs -n compliance-system -l compliance-operator.alauda.io/scan=test-scan-manual-strategy
```

### 4. 测试 Strict Strategy
```bash
# 查看 strict strategy scan
kubectl get scan test-scan-strict-strategy -n compliance-system -o yaml

# 检查是否有兼容性错误
kubectl describe scan test-scan-strict-strategy -n compliance-system
```

### 5. 查看 Job 执行结果
```bash
# 查看 job pods
kubectl get pods -n compliance-system -l compliance-operator.alauda.io/scan=test-scan-auto-strategy

# 查看 job 日志
kubectl logs -n compliance-system -l compliance-operator.alauda.io/scan=test-scan-auto-strategy

# 查看 scan 结果
kubectl get scanresult -n compliance-system
```

## 🔍 验证要点

### Auto Strategy 验证
1. **节点分配正确性**:
   - Control-plane rules 只在 control-plane 节点运行
   - Worker rules 只在 worker 节点运行
   - All-scope rules 在所有节点运行

2. **Job 标签正确性**:
   ```bash
   kubectl get jobs -n compliance-system -o yaml | grep -A5 -B5 "nodeScope\|nodeRole"
   ```

3. **容忍度设置**:
   ```bash
   kubectl get jobs -n compliance-system -o yaml | grep -A10 tolerations
   ```

### Manual Strategy 验证
1. **忽略 nodeScope**: 所有 rules 在所有节点运行
2. **使用 nodeSelector**: 只在匹配的节点运行

### Strict Strategy 验证
1. **兼容性检查**: 不兼容的 rules 不会创建 jobs
2. **错误报告**: Scan 状态中包含兼容性错误信息

## 🧹 清理测试资源
```bash
# 删除所有测试 scans
kubectl delete scan -n compliance-system -l compliance-operator.alauda.io/scan-type=test

# 删除测试 jobs
kubectl delete jobs -n compliance-system -l compliance-operator.alauda.io/scan-type=test

# 删除测试资源
kubectl delete -f examples/test-rules-basic.yaml
kubectl delete -f examples/test-profile-basic.yaml
kubectl delete -f examples/test-content-basic.yaml
```

## 📊 预期测试结果

### Auto Strategy
- ✅ Control-plane rule 只在 control-plane 节点创建 job
- ✅ Worker rule 只在 worker 节点创建 job  
- ✅ All-scope rule 在所有节点创建 job
- ✅ Platform rule 创建单个 platform job

### Manual Strategy
- ✅ 所有 rules 在所有匹配 nodeSelector 的节点创建 job
- ✅ 忽略 rule 的 nodeScope 设置

### Strict Strategy
- ✅ 兼容的 rules 正常创建 job
- ✅ 不兼容的 rules 导致 scan 失败并报告错误

这些测试示例将帮助你验证 NodeScopeStrategy 功能是否按预期工作！
