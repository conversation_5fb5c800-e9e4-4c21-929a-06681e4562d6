package scan

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

// TestAutoStrategyNodeSelection 测试auto策略的节点选择修复
func TestAutoStrategyNodeSelection(t *testing.T) {
	// 创建测试节点
	controlPlaneNode := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "control-plane-node",
			Labels: map[string]string{
				"node-role.kubernetes.io/control-plane": "",
				"kubernetes.io/os":                      "linux",
			},
		},
	}

	workerNode1 := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "worker-node-1",
			Labels: map[string]string{
				"node-role.kubernetes.io/worker": "",
				"kubernetes.io/os":               "linux",
			},
		},
	}

	workerNode2 := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "worker-node-2",
			Labels: map[string]string{
				"node-role.kubernetes.io/worker": "",
				"kubernetes.io/os":               "linux",
			},
		},
	}

	// 创建测试规则
	controlPlaneRule := complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "control-plane-rule",
			Namespace: "default",
		},
		Spec: complianceapi.RuleSpec{
			Title:       "Control Plane Rule",
			CheckType:   "node",
			NodeScope:   "control-plane",
			CheckScript: "echo 'control plane check'",
		},
	}

	workerRule := complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "worker-rule",
			Namespace: "default",
		},
		Spec: complianceapi.RuleSpec{
			Title:       "Worker Rule",
			CheckType:   "node",
			NodeScope:   "worker",
			CheckScript: "echo 'worker check'",
		},
	}

	allScopeRule := complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "all-scope-rule",
			Namespace: "default",
		},
		Spec: complianceapi.RuleSpec{
			Title:       "All Scope Rule",
			CheckType:   "node",
			NodeScope:   "all",
			CheckScript: "echo 'all nodes check'",
		},
	}

	testCases := []struct {
		name                    string
		scan                    *complianceapi.Scan
		rules                   []complianceapi.Rule
		expectedJobsByNodeScope map[string][]string // nodeScope -> node names
		description             string
	}{
		{
			name: "Auto strategy ignores targetNodeRoles - control-plane rules",
			scan: &complianceapi.Scan{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "test-scan",
					Namespace: "default",
				},
				Spec: complianceapi.ScanSpec{
					NodeScopeStrategy: "auto",
					TargetNodeRoles:   []string{"control-plane", "worker"}, // 这应该被忽略
				},
			},
			rules: []complianceapi.Rule{controlPlaneRule},
			expectedJobsByNodeScope: map[string][]string{
				"control-plane": {"control-plane-node"}, // 只在control-plane节点创建Job
			},
			description: "Auto策略应该忽略targetNodeRoles，只根据规则的nodeScope=control-plane在control-plane节点创建Job",
		},
		{
			name: "Auto strategy ignores targetNodeRoles - worker rules",
			scan: &complianceapi.Scan{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "test-scan",
					Namespace: "default",
				},
				Spec: complianceapi.ScanSpec{
					NodeScopeStrategy: "auto",
					TargetNodeRoles:   []string{"control-plane"}, // 这应该被忽略
				},
			},
			rules: []complianceapi.Rule{workerRule},
			expectedJobsByNodeScope: map[string][]string{
				"worker": {"worker-node-1", "worker-node-2"}, // 只在worker节点创建Job
			},
			description: "Auto策略应该忽略targetNodeRoles，只根据规则的nodeScope=worker在worker节点创建Job",
		},
		{
			name: "Auto strategy with mixed nodeScope rules",
			scan: &complianceapi.Scan{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "test-scan",
					Namespace: "default",
				},
				Spec: complianceapi.ScanSpec{
					NodeScopeStrategy: "auto",
					TargetNodeRoles:   []string{"worker"}, // 这应该被忽略
				},
			},
			rules: []complianceapi.Rule{controlPlaneRule, workerRule},
			expectedJobsByNodeScope: map[string][]string{
				"control-plane": {"control-plane-node"},
				"worker":        {"worker-node-1", "worker-node-2"},
			},
			description: "Auto策略应该根据每个规则的nodeScope分别在对应节点创建Job",
		},
		{
			name: "Auto strategy with all scope rule",
			scan: &complianceapi.Scan{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "test-scan",
					Namespace: "default",
				},
				Spec: complianceapi.ScanSpec{
					NodeScopeStrategy: "auto",
					TargetNodeRoles:   []string{"control-plane"}, // 这应该被忽略
				},
			},
			rules: []complianceapi.Rule{allScopeRule},
			expectedJobsByNodeScope: map[string][]string{
				"all": {"control-plane-node", "worker-node-1", "worker-node-2"},
			},
			description: "Auto策略对于nodeScope=all的规则应该在所有节点创建Job",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 设置reconciler
			r := setupReconciler(t, tc.scan, &controlPlaneNode, &workerNode1, &workerNode2)
			for _, rule := range tc.rules {
				err := r.Create(context.Background(), &rule)
				require.NoError(t, err)
			}

			// 调用createNodeScanJobsWithAutoStrategy
			allNodes := []corev1.Node{controlPlaneNode, workerNode1, workerNode2}
			err := r.createNodeScanJobsWithAutoStrategy(context.Background(), tc.scan, tc.rules, allNodes, "test-scan-id")
			require.NoError(t, err, tc.description)

			// 验证创建的Jobs
			var jobList batchv1.JobList
			err = r.List(context.Background(), &jobList, client.InNamespace("default"))
			require.NoError(t, err)

			// 按nodeScope分组验证Jobs
			actualJobsByNodeScope := make(map[string][]string)
			for _, job := range jobList.Items {
				nodeScope := job.Labels["compliance-operator.alauda.io/node-scope"]
				if nodeScope == "" {
					nodeScope = "all"
				}
				nodeName := job.Labels["compliance-operator.alauda.io/node"]
				actualJobsByNodeScope[nodeScope] = append(actualJobsByNodeScope[nodeScope], nodeName)
			}

			// 验证结果
			for expectedScope, expectedNodes := range tc.expectedJobsByNodeScope {
				actualNodes, exists := actualJobsByNodeScope[expectedScope]
				assert.True(t, exists, "Expected nodeScope %s not found in created jobs", expectedScope)
				assert.ElementsMatch(t, expectedNodes, actualNodes,
					"Jobs for nodeScope %s created on wrong nodes. Expected: %v, Actual: %v",
					expectedScope, expectedNodes, actualNodes)
			}

			// 验证没有额外的Jobs
			for actualScope := range actualJobsByNodeScope {
				_, expected := tc.expectedJobsByNodeScope[actualScope]
				assert.True(t, expected, "Unexpected jobs created for nodeScope %s", actualScope)
			}

			t.Logf("✅ %s", tc.description)
		})
	}
}

// TestFilterNodesForScanAutoStrategyExclusion 测试filterNodesForScan不再处理auto策略
func TestFilterNodesForScanAutoStrategyExclusion(t *testing.T) {
	r := &ScanReconciler{}

	controlPlaneNode := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "control-plane-node",
			Labels: map[string]string{
				"node-role.kubernetes.io/control-plane": "",
			},
		},
	}

	workerNode := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "worker-node",
			Labels: map[string]string{
				"node-role.kubernetes.io/worker": "",
			},
		},
	}

	allNodes := []corev1.Node{controlPlaneNode, workerNode}

	testCases := []struct {
		name          string
		scan          *complianceapi.Scan
		expectedNodes []string
		description   string
	}{
		{
			name: "Auto strategy should not be processed by filterNodesForScan",
			scan: &complianceapi.Scan{
				Spec: complianceapi.ScanSpec{
					NodeScopeStrategy: "auto",
					TargetNodeRoles:   []string{"worker"}, // 这应该被忽略
				},
			},
			expectedNodes: []string{"control-plane-node", "worker-node"}, // 应该返回所有节点
			description:   "filterNodesForScan对auto策略应该返回所有节点，不进行过滤",
		},
		{
			name: "Manual strategy should still be processed",
			scan: &complianceapi.Scan{
				Spec: complianceapi.ScanSpec{
					NodeScopeStrategy: "manual",
					TargetNodeRoles:   []string{"worker"},
				},
			},
			expectedNodes: []string{"worker-node"}, // 应该根据targetNodeRoles过滤
			description:   "filterNodesForScan对manual策略应该根据targetNodeRoles过滤节点",
		},
		{
			name: "Strict strategy should still be processed",
			scan: &complianceapi.Scan{
				Spec: complianceapi.ScanSpec{
					NodeScopeStrategy: "strict",
					TargetNodeRoles:   []string{"control-plane"},
				},
			},
			expectedNodes: []string{"control-plane-node"}, // 应该根据targetNodeRoles过滤
			description:   "filterNodesForScan对strict策略应该根据targetNodeRoles过滤节点",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			filteredNodes := r.filterNodesForScan(allNodes, tc.scan)

			var nodeNames []string
			for _, node := range filteredNodes {
				nodeNames = append(nodeNames, node.Name)
			}

			assert.ElementsMatch(t, tc.expectedNodes, nodeNames, tc.description)
			t.Logf("✅ %s", tc.description)
		})
	}
}

// TestCreateNodeScanJobsStrategySelection 测试createNodeScanJobs的策略选择
func TestCreateNodeScanJobsStrategySelection(t *testing.T) {
	controlPlaneNode := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "control-plane-node",
			Labels: map[string]string{
				"node-role.kubernetes.io/control-plane": "",
			},
		},
	}

	workerNode := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "worker-node",
			Labels: map[string]string{
				"node-role.kubernetes.io/worker": "",
			},
		},
	}

	controlPlaneRule := complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "control-plane-rule",
			Namespace: "default",
		},
		Spec: complianceapi.RuleSpec{
			CheckType:   "node",
			NodeScope:   "control-plane",
			CheckScript: "echo test",
		},
	}

	testCases := []struct {
		name                string
		scan                *complianceapi.Scan
		expectedJobsOnNodes []string
		description         string
	}{
		{
			name: "Auto strategy creates jobs based on rule nodeScope",
			scan: &complianceapi.Scan{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "auto-scan",
					Namespace: "default",
				},
				Spec: complianceapi.ScanSpec{
					NodeScopeStrategy: "auto",
					TargetNodeRoles:   []string{"worker"}, // 应该被忽略
				},
			},
			expectedJobsOnNodes: []string{"control-plane-node"}, // 只在control-plane节点
			description:         "Auto策略应该忽略scan的targetNodeRoles，只根据rule的nodeScope创建Job",
		},
		{
			name: "Manual strategy respects scan targetNodeRoles",
			scan: &complianceapi.Scan{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "manual-scan",
					Namespace: "default",
				},
				Spec: complianceapi.ScanSpec{
					NodeScopeStrategy: "manual",
					TargetNodeRoles:   []string{"worker"},
				},
			},
			expectedJobsOnNodes: []string{"worker-node"}, // 根据scan的targetNodeRoles
			description:         "Manual策略应该根据scan的targetNodeRoles创建Job",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			r := setupReconciler(t, tc.scan, &controlPlaneNode, &workerNode, &controlPlaneRule)

			err := r.createNodeScanJobs(context.Background(), tc.scan, []complianceapi.Rule{controlPlaneRule}, "test-scan-id")
			require.NoError(t, err)

			// 验证创建的Jobs
			var jobList batchv1.JobList
			err = r.List(context.Background(), &jobList, client.InNamespace("default"))
			require.NoError(t, err)

			var actualJobNodes []string
			for _, job := range jobList.Items {
				nodeName := job.Labels["compliance-operator.alauda.io/node"]
				if nodeName != "" {
					actualJobNodes = append(actualJobNodes, nodeName)
				}
			}

			assert.ElementsMatch(t, tc.expectedJobsOnNodes, actualJobNodes, tc.description)
			t.Logf("✅ %s", tc.description)
		})
	}
}

// TestGroupRulesByNodeScopeWithAutoStrategy 测试规则按nodeScope分组
func TestGroupRulesByNodeScopeWithAutoStrategy(t *testing.T) {
	r := &ScanReconciler{}

	rules := []complianceapi.Rule{
		{
			ObjectMeta: metav1.ObjectMeta{Name: "control-plane-rule-1"},
			Spec: complianceapi.RuleSpec{
				CheckType: "node",
				NodeScope: "control-plane",
			},
		},
		{
			ObjectMeta: metav1.ObjectMeta{Name: "control-plane-rule-2"},
			Spec: complianceapi.RuleSpec{
				CheckType: "node",
				NodeScope: "control-plane",
			},
		},
		{
			ObjectMeta: metav1.ObjectMeta{Name: "worker-rule"},
			Spec: complianceapi.RuleSpec{
				CheckType: "node",
				NodeScope: "worker",
			},
		},
		{
			ObjectMeta: metav1.ObjectMeta{Name: "all-rule"},
			Spec: complianceapi.RuleSpec{
				CheckType: "node",
				NodeScope: "all",
			},
		},
		{
			ObjectMeta: metav1.ObjectMeta{Name: "empty-scope-rule"},
			Spec: complianceapi.RuleSpec{
				CheckType: "node",
				NodeScope: "", // 空的nodeScope应该默认为"all"
			},
		},
		{
			ObjectMeta: metav1.ObjectMeta{Name: "platform-rule"},
			Spec: complianceapi.RuleSpec{
				CheckType: "platform", // 非node类型应该被忽略
				NodeScope: "control-plane",
			},
		},
	}

	ruleGroups := r.groupRulesByNodeScope(rules)

	// 验证分组结果
	expectedGroups := map[string][]string{
		"control-plane": {"control-plane-rule-1", "control-plane-rule-2"},
		"worker":        {"worker-rule"},
		"all":           {"all-rule", "empty-scope-rule"},
	}

	assert.Equal(t, len(expectedGroups), len(ruleGroups), "Rule groups count mismatch")

	for expectedScope, expectedRuleNames := range expectedGroups {
		actualRules, exists := ruleGroups[expectedScope]
		assert.True(t, exists, "Expected nodeScope %s not found", expectedScope)

		var actualRuleNames []string
		for _, rule := range actualRules {
			actualRuleNames = append(actualRuleNames, rule.Name)
		}

		assert.ElementsMatch(t, expectedRuleNames, actualRuleNames,
			"Rules for nodeScope %s mismatch. Expected: %v, Actual: %v",
			expectedScope, expectedRuleNames, actualRuleNames)
	}

	// 验证platform规则被忽略
	for _, ruleGroup := range ruleGroups {
		for _, rule := range ruleGroup {
			assert.Equal(t, "node", rule.Spec.CheckType, "Non-node rule should be filtered out")
		}
	}

	t.Log("✅ 规则按nodeScope正确分组，platform规则被忽略，空nodeScope默认为all")
}
