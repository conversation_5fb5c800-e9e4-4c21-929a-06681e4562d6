
---
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: stig-os-micoos
  namespace: {{ .Values.global.complianceNamespace }}
spec:
  title: "DISA STIG for SUSE Linux Enterprise Micro (SLEM)"
  id: "xccdf_org.ssgproject.content_profile_stig"
  description: "This profile contains configuration checks that align to the DISA STIG for SUSE Linux Enterprise Micro (SLEM)"
  dataStream:
    contentFile: ssg-slmicro5-ds.xml
    contentImage: {{ .Values.global.registry.address }}/{{ .Values.global.images.content.repository }}:{{ .Values.global.images.content.tag }}
    profileId: xccdf_org.ssgproject.content_profile_stig
    scanType: node
---
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: stig-os-ubuntu2204
  namespace: {{ .Values.global.complianceNamespace }}
spec:
  title: "DISA STIG for Ubuntu 22.04"
  id: "xccdf_org.ssgproject.content_profile_stig"
  description: "This profile contains configuration checks that align to the DISA STIG for Ubuntu 22.04"
  dataStream:
    contentFile: ssg-ubuntu2204-ds.xml
    contentImage: {{ .Values.global.registry.address }}/{{ .Values.global.images.content.repository }}:{{ .Values.global.images.content.tag }}
    profileId: xccdf_org.ssgproject.content_profile_stig
    scanType: node

