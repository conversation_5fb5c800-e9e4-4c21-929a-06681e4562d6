#!/bin/bash

# 聚合Job功能测试脚本
set -e

NAMESPACE="compliance-system"
SCAN_NAME="test-aggregated-scan"

echo "=== 聚合Job功能测试脚本 ==="
echo "命名空间: $NAMESPACE"
echo "扫描名称: $SCAN_NAME"
echo ""

# 检查kubectl可用性
if ! command -v kubectl &> /dev/null; then
    echo "错误: kubectl 未找到"
    exit 1
fi

# 检查集群连接
if ! kubectl cluster-info &> /dev/null; then
    echo "错误: 无法连接到Kubernetes集群"
    exit 1
fi

echo "1. 检查现有资源状态..."

# 检查operator状态
echo "   检查compliance-operator状态..."
if kubectl get deployment compliance-operator -n $NAMESPACE &> /dev/null; then
    kubectl get deployment compliance-operator -n $NAMESPACE
else
    echo "   警告: compliance-operator deployment未找到"
fi

# 检查测试资源是否存在
echo "   检查测试资源..."
if kubectl get scan $SCAN_NAME -n $NAMESPACE &> /dev/null; then
    echo "   测试扫描已存在: $SCAN_NAME"
else
    echo "   测试扫描不存在，将创建..."
    if [ -f "examples/test-aggregated-scan.yaml" ]; then
        kubectl apply -f examples/test-aggregated-scan.yaml
        echo "   测试资源已创建"
    else
        echo "   错误: 测试配置文件不存在: examples/test-aggregated-scan.yaml"
        exit 1
    fi
fi

echo ""
echo "2. 触发聚合扫描..."

# 触发扫描
kubectl annotate scan $SCAN_NAME -n $NAMESPACE \
    compliance-operator.alauda.io/force-scan=true --overwrite

echo "   扫描已触发"

echo ""
echo "3. 监控Job创建..."

# 等待Job创建
echo "   等待Job创建（最多60秒）..."
for i in {1..60}; do
    JOB_COUNT=$(kubectl get jobs -n $NAMESPACE -l compliance-operator.alauda.io/scan=$SCAN_NAME --no-headers 2>/dev/null | wc -l)
    if [ $JOB_COUNT -gt 0 ]; then
        echo "   发现 $JOB_COUNT 个Job"
        break
    fi
    echo -n "."
    sleep 1
done

echo ""

# 显示创建的Job
echo "   创建的Job列表:"
kubectl get jobs -n $NAMESPACE -l compliance-operator.alauda.io/scan=$SCAN_NAME

# 检查是否是聚合Job
echo ""
echo "4. 验证聚合Job特征..."

AGGREGATED_JOBS=$(kubectl get jobs -n $NAMESPACE -l compliance-operator.alauda.io/scan=$SCAN_NAME,compliance-operator.alauda.io/job-type=aggregated --no-headers 2>/dev/null | wc -l)
TOTAL_JOBS=$(kubectl get jobs -n $NAMESPACE -l compliance-operator.alauda.io/scan=$SCAN_NAME --no-headers 2>/dev/null | wc -l)

echo "   聚合Job数量: $AGGREGATED_JOBS"
echo "   总Job数量: $TOTAL_JOBS"

if [ $AGGREGATED_JOBS -gt 0 ]; then
    echo "   ✅ 检测到聚合Job"
    
    # 显示聚合Job详情
    echo "   聚合Job详情:"
    kubectl get jobs -n $NAMESPACE -l compliance-operator.alauda.io/scan=$SCAN_NAME,compliance-operator.alauda.io/job-type=aggregated -o wide
    
    # 检查Job标签
    JOB_NAME=$(kubectl get jobs -n $NAMESPACE -l compliance-operator.alauda.io/scan=$SCAN_NAME,compliance-operator.alauda.io/job-type=aggregated -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
    if [ -n "$JOB_NAME" ]; then
        echo "   Job标签验证:"
        kubectl get job $JOB_NAME -n $NAMESPACE -o jsonpath='{.metadata.labels}' | jq .
    fi
else
    echo "   ⚠️  未检测到聚合Job，可能使用的是原有模式"
fi

echo ""
echo "5. 监控扫描进度..."

# 监控扫描状态
echo "   等待扫描完成（最多300秒）..."
for i in {1..300}; do
    SCAN_PHASE=$(kubectl get scan $SCAN_NAME -n $NAMESPACE -o jsonpath='{.status.phase}' 2>/dev/null || echo "Unknown")
    echo "   扫描状态: $SCAN_PHASE"
    
    if [ "$SCAN_PHASE" = "Done" ] || [ "$SCAN_PHASE" = "Error" ]; then
        break
    fi
    
    sleep 5
done

echo ""
echo "6. 检查扫描结果..."

# 显示扫描状态
kubectl get scan $SCAN_NAME -n $NAMESPACE -o yaml | grep -A 20 "status:"

# 检查结果ConfigMap
echo ""
echo "   结果ConfigMap:"
kubectl get configmap -n $NAMESPACE -l compliance-operator.alauda.io/scan=$SCAN_NAME,compliance-operator.alauda.io/result=true

# 检查CheckResult
echo ""
echo "   CheckResult:"
kubectl get checkresult -n $NAMESPACE -l compliance-operator.alauda.io/scan=$SCAN_NAME

echo ""
echo "7. 性能对比分析..."

# 计算理论Job数量
RULE_COUNT=$(kubectl get profile test-profile-aggregated -n $NAMESPACE -o jsonpath='{.spec.rules}' | jq length 2>/dev/null || echo "3")
NODE_COUNT=$(kubectl get nodes -l node-role.kubernetes.io/control-plane --no-headers | wc -l)

EXPECTED_INDIVIDUAL_JOBS=$((RULE_COUNT * NODE_COUNT))
EXPECTED_AGGREGATED_JOBS=$NODE_COUNT

echo "   规则数量: $RULE_COUNT"
echo "   目标节点数量: $NODE_COUNT"
echo "   原有模式预期Job数量: $EXPECTED_INDIVIDUAL_JOBS"
echo "   聚合模式预期Job数量: $EXPECTED_AGGREGATED_JOBS"
echo "   实际创建Job数量: $TOTAL_JOBS"

if [ $TOTAL_JOBS -eq $EXPECTED_AGGREGATED_JOBS ]; then
    REDUCTION_PERCENT=$(( (EXPECTED_INDIVIDUAL_JOBS - TOTAL_JOBS) * 100 / EXPECTED_INDIVIDUAL_JOBS ))
    echo "   ✅ 聚合模式工作正常，Job数量减少了 $REDUCTION_PERCENT%"
elif [ $TOTAL_JOBS -eq $EXPECTED_INDIVIDUAL_JOBS ]; then
    echo "   ⚠️  使用的是原有模式，未启用聚合功能"
else
    echo "   ❓ Job数量异常，需要进一步检查"
fi

echo ""
echo "8. 清理测试资源（可选）..."
echo "   如需清理测试资源，请运行："
echo "   kubectl delete scan $SCAN_NAME -n $NAMESPACE"
echo "   kubectl delete profile test-profile-aggregated -n $NAMESPACE"
echo "   kubectl delete rule test-rule-file-permissions test-rule-process-check test-rule-network-check -n $NAMESPACE"

echo ""
echo "=== 测试完成 ==="
