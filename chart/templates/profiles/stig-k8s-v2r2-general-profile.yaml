---
# STIG Kubernetes V2R2 General Profile
# Contains general configuration and file permission checks
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: stig-k8s-v2r2-general
  namespace: {{ .Values.global.complianceNamespace }}
spec:
  id: "STIG-K8S-V2R2-GENERAL"
  title: "STIG Kubernetes V2R2 General Security Profile"
  description: |
    This profile contains general security checks based on the 
    Security Technical Implementation Guide (STIG) for Kubernetes V2R2.
    Includes file permissions, configuration ownership, and general security settings.
  version: "1.0"
  rules:
    - name: stig-k8s-admin-conf-file-permissions
    - name: stig-k8s-control-plane-conf-file-ownership
    - name: stig-k8s-dashboard-disabled
    - name: stig-k8s-dynamic-auditing-disabled
    - name: stig-k8s-endpoints-certificates
    - name: stig-k8s-kube-proxy-conf-file-ownership
    - name: stig-k8s-kube-proxy-conf-file-permissions
    - name: stig-k8s-kubeadm-conf-ownership
    - name: stig-k8s-kubectl-version
    - name: stig-k8s-manifest-file-permissions
    - name: stig-k8s-manifest-permissions
    - name: stig-k8s-manifests-owned-by-root
    - name: stig-k8s-manifests-ownership
    - name: stig-k8s-non-privileged-ports
    - name: stig-k8s-old-components-removed
    - name: stig-k8s-pki-certificate-file-permissions
    - name: stig-k8s-pki-directory-ownership
    - name: stig-k8s-pki-key-file-permissions
    - name: stig-k8s-pod-security-admission-control-file
    - name: stig-k8s-pod-security-admission-enabled
    - name: stig-k8s-pod-security-policy
    - name: stig-k8s-secrets-not-environment-variables
    - name: stig-k8s-ssh-service-disabled
    - name: stig-k8s-ssh-service-stopped
    - name: stig-k8s-user-functionality-separation
    - name: stig-k8s-user-resources-dedicated-namespaces
    - name: stig-k8s-version-up-to-date 