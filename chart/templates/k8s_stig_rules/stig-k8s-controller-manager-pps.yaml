apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-controller-manager-pps
  namespace: compliance-system
spec:
  id: "V-242412"
  title: "The Kubernetes Controllers must enforce ports, protocols, and services (PPS) that adhere to the Ports, Protocols, and Services Management Category Assurance List (PPSM CAL)."
  description: "Kubernetes Controller Manager PPSs must be controlled and conform to the PPSM CAL. Those PPS that fall outside the PPSM CAL must be blocked. Instructions on the PPSM can be found in DoD Instruction 8551.01 Policy."
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep kube-controller-manager.manifest -I -secure-port *\n-edit manifest file:\nVIM <Manifest Name>\nReview livenessProbe:\nHttpGet:\nPort:\nReview ports:\n- containerPort:\nhostPort:\n- containerPort:\nhostPort:\n\nRun command: \nkubectl describe services --all-namespaces \nSearch labels for any controller manager namespaces.\nPort:\n\nAny manifest and namespace PPS or services configuration not in compliance with PPSM CAL is a finding.\n\nReview the information systems documentation and interview the team, gain an understanding of the Controller Manager architecture, and determine applicable PPS. If there are any PPS in the system documentation not in compliance with the CAL PPSM, this is a finding. Any PPS not set in the system documentation is a finding.\n\nReview findings against the most recent PPSM CAL:\nhttps://cyber.mil/ppsm/cal/\n\nVerify Controller Manager network boundary with the PPS associated with the CAL Assurance Categories. Any PPS not in compliance with the CAL Assurance Category requirements is a finding."
  fixText: "Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of --secure-port to a port that is in compliance with the PPSM CAL.\n\nAny other ports used by the Controller Manager must be in compliance with the PPSM CAL.\n\nReview the information systems documentation and interview the team, gain an understanding of the Controller Manager architecture, and determine applicable PPS. Document all PPS in the system documentation. All PPS must be in compliance with the CAL PPSM."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting Controller Manager PPSM CAL compliance check (V-242412)"
    echo ""
    echo "MANUAL: This rule requires manual verification and cannot be automated"
    echo ""
    echo "PPSM CAL Compliance Requirements:"
    echo "All ports, protocols, and services must conform to the PPSM Category Assurance List"
    echo ""
    echo "Manual Verification Checklist:"
    echo ""
    echo "1. Controller Manager Manifest Review:"
    echo "   - Check /etc/kubernetes/manifests/kube-controller-manager.yaml"
    echo "   - Verify --secure-port setting complies with PPSM CAL"
    echo "   - Review livenessProbe and readinessProbe port configurations"
    echo "   - Check containerPort and hostPort settings"
    echo ""
    echo "2. Service Configuration Review:"
    echo "   - Run: kubectl describe services --all-namespaces"
    echo "   - Identify any controller manager related services"
    echo "   - Verify all service ports comply with PPSM CAL"
    echo ""
    echo "3. Network Boundary Verification:"
    echo "   - Review Controller Manager network architecture"
    echo "   - Verify all PPS align with CAL Assurance Categories"
    echo "   - Ensure no unauthorized ports/protocols are in use"
    echo ""
    echo "4. Documentation Requirements:"
    echo "   - All PPS must be documented in system documentation"
    echo "   - Interview team to understand Controller Manager architecture"
    echo "   - Verify documented PPS compliance with PPSM CAL"
    echo ""
    echo "Reference Resources:"
    echo "- PPSM CAL: https://cyber.mil/ppsm/cal/"
    echo "- STIG Details: https://stigviewer.com/stigs/kubernetes/2024-08-22/finding/V-242412"
    echo "- DoD Instruction 8551.01 Policy for PPSM requirements"
    echo ""
    echo "This check requires manual verification by authorized personnel"
    echo "Exit code 2 indicates manual verification is required"
    
    exit 2 