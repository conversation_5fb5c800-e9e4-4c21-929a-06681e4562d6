apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-audit-log-maxsize
  namespace: compliance-system
spec:
  id: "V-242462"
  title: "The Kubernetes API Server must be set to audit log max size."
  description: "The Kubernetes API Server must be set for enough storage to retain log information over the period required. When audit logs are large in size, the monitoring service for events becomes degraded. The function of the maximum log file size is to set these limits."
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i audit-log-maxsize * \n\nIf the setting \"--audit-log-maxsize\" is not set in the Kubernetes API Server manifest file or it is set to less than \"100\", this is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--audit-log-maxsize\" to a minimum of \"100\"."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Checking API Server audit log max size configuration..."
    manifest_file="/host/etc/kubernetes/manifests/kube-apiserver.yaml"
    echo "Checking API Server manifest: $manifest_file"

    # Check if manifest file exists
    if [[ ! -f "$manifest_file" ]]; then
        echo "ERROR: API Server manifest file not found"
        echo "FAIL: API Server manifest missing"
        exit 1
    fi

    echo "Found API Server manifest file"

    # Check audit log max size configuration
    echo "Searching for audit-log-maxsize parameter..."
            max_size=$(grep -i "\-\-audit-log-maxsize" "$manifest_file" | awk -F'=' '{print $2}' 2>/dev/null | tr -d ' ')
    
    if [[ -z "$max_size" ]]; then
        echo "VIOLATION: audit-log-maxsize parameter not configured"
        echo "FAIL: audit-log-maxsize not configured"
        exit 1
    fi

    echo "Found audit-log-maxsize configuration: $max_size"

    # Validate max size value is numeric
    if ! [[ "$max_size" =~ ^[0-9]+$ ]]; then
        echo "ERROR: Invalid audit-log-maxsize value (not numeric): $max_size"
        echo "FAIL: Invalid audit-log-maxsize value"
        exit 1
    fi

    # Check if max size meets minimum requirement (≥100 MB)
    if [[ $max_size -lt 100 ]]; then
        echo "VIOLATION: audit-log-maxsize is less than 100 MB (current: $max_size)"
        echo "FAIL: audit-log-maxsize <100 MB (current: $max_size)"
        exit 1
    fi

    echo "OK: audit-log-maxsize meets ≥100 MB requirement (current: $max_size)"
    echo "PASS: audit-log-maxsize properly configured"
    exit 0 