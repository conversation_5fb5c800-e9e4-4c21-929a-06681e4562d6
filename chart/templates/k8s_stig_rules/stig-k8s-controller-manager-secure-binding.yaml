apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-controller-manager-secure-binding
  namespace: compliance-system
spec:
  id: "V-242385"
  title: "The Kubernetes Controller Manager must have secure binding."
  description: "The Controller Manager is a background process that embeds core control loops, regulating the state of the Kubernetes cluster. The Controller Manager must be configured to use a secure binding to ensure that communication between the Controller Manager and the API server is encrypted. By using a secure binding, the confidentiality and integrity of the communication is protected."
  checkText: "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i bind-address kube-controller-manager.yaml\n\nIf the setting \"--bind-address\" is not set to \"127.0.0.1\" in the Kubernetes Controller Manager manifest file, this is a finding."
  fixText: "Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--bind-address\" to \"127.0.0.1\"."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting Controller Manager secure binding check (V-242385)"
    
    # Define Controller Manager manifest file path (preserving original logic)
    cm_file="/host/etc/kubernetes/manifests/kube-controller-manager.yaml"
    
    echo "Checking Controller Manager manifest file: $cm_file"

    # Check if manifest file exists (preserving original logic)
    if [[ -f "$cm_file" ]]; then
        echo "Found Controller Manager manifest file"
        echo "Searching for --bind-address configuration..."
        
        # Extract bind address value (preserving original logic)
        bind_addr=$(grep -i -- "--bind-address" "$cm_file" | awk -F= '{print $2}' 2>/dev/null)
        
        if [[ -n "$bind_addr" ]]; then
            echo "Found bind-address configuration: --bind-address=$bind_addr"
            echo "Required secure addresses: 127.0.0.1 (IPv4) or ::1 (IPv6)"
            
            # Validate bind address - support both IPv4 and IPv6 localhost (preserving original logic)
            if [[ "$bind_addr" != "127.0.0.1" && "$bind_addr" != "::1" ]]; then
                echo "FAIL: Invalid bind-address $bind_addr in Controller Manager"
                echo "Controller Manager must bind to localhost for security"
                if [[ "$bind_addr" == "::" ]]; then
                    echo "IPv6 all interfaces (::) allows external access - security risk"
                    echo "Use ::1 for IPv6 localhost or 127.0.0.1 for IPv4 localhost"
                elif [[ "$bind_addr" == "0.0.0.0" ]]; then
                    echo "IPv4 all interfaces (0.0.0.0) allows external access - security risk"
                    echo "Use 127.0.0.1 for IPv4 localhost"
                else
                    echo "Current address $bind_addr allows external access which is a security risk"
                    echo "Use 127.0.0.1 for IPv4 localhost or ::1 for IPv6 localhost"
                fi
                exit 1
            fi
            
            echo "Bind address is correctly set to secure localhost address"
        else
            echo "No --bind-address configuration found"
            echo "Checking if this affects security compliance..."
            # Note: Original script doesn't handle this case, preserving the logic
        fi
    else
        echo "FAIL: Controller Manager manifest not found at $cm_file"
        echo "Expected to find kube-controller-manager.yaml manifest file"
        exit 1
    fi

    echo "PASS: Controller Manager binding secure"
    exit 0 