apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-streaming-timeout
  namespace: compliance-system
spec:
  id: "V-245541"
  title: "Kubernetes Kubelet must not disable timeouts."
  description: "Kubernetes Kubelet must have streaming connection timeouts configured to prevent resource exhaustion."
  fixText: "Follow these steps to configure streaming-connection-idle-timeout:\n1. On the Control Plane, run the command:\nps -ef | grep kubelet\n\nRemove the \"--streaming-connection-idle-timeout\" option if present.\n\nNote the path to the config file (identified by --config).\n\n2. Edit the Kubernetes Kubelet file in the --config directory on the Kubernetes Control Plane:\n\nSet the argument \"--streaming-connection-idle-timeout\" to a value of \"5m\"."
  checkText: "Follow these steps to check streaming-connection-idle-timeout:\n\n1. On the Control Plane, run the command:\n\nps -ef | grep kubelet\n\nIf the \"--streaming-connection-idle-timeout\" option exists, this is a finding.\n\nNote the path to the config file (identified by --config).\n\n2. Run the command:\n\ngrep -i streamingConnectionIdleTimeout <path_to_config_file>\n\nIf the setting \"streamingConnectionIdleTimeout\" is set to less than \"5m\" or is not configured, this is a finding."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting kubelet streaming connection timeout check (V-245541)"
    
    # Define kubelet config file path with /host prefix for container environment
    config_file="/host/var/lib/kubelet/config.yaml"
    
    echo "Checking kubelet configuration file: $config_file"
    
    # Check if config file exists
    if [[ -f "$config_file" ]]; then
        echo "Found kubelet configuration file"
        
        # Check for streamingConnectionIdleTimeout setting in config file
        timeout=$(grep 'streamingConnectionIdleTimeout' "$config_file" | awk -F: '{print $2}' | tr -d ' "' 2>/dev/null)
        
        if [[ -z "$timeout" ]]; then
            echo "FAIL: streamingConnectionIdleTimeout not configured"
            echo "streamingConnectionIdleTimeout setting is missing from config file"
            echo "Streaming connection timeout must be configured to prevent resource exhaustion"
            exit 1
        else
            echo "Found streamingConnectionIdleTimeout setting: $timeout"
            
            # Convert timeout to minutes (supports complex formats like 5m0s, 1h30m, etc.)
            minutes=0
            
            # Extract hours and convert to minutes
            if [[ $timeout =~ ([0-9]+)h ]]; then
                hours=${BASH_REMATCH[1]}
                ((minutes += hours * 60))
            fi
            
            # Extract minutes
            if [[ $timeout =~ ([0-9]+)m ]]; then
                mins=${BASH_REMATCH[1]}
                ((minutes += mins))
            fi
            
            # Extract seconds and convert to minutes (fractional)
            if [[ $timeout =~ ([0-9]+)s ]]; then
                seconds=${BASH_REMATCH[1]}
                ((minutes += (seconds + 59) / 60))  # Round up seconds to minutes
            fi
            
            # If no units found, assume it's just a number in minutes
            if [[ $minutes -eq 0 && $timeout =~ ^[0-9]+$ ]]; then
                minutes=$timeout
            fi
            
            echo "Timeout value in minutes: $minutes"
            echo "Minimum required timeout: 5 minutes"
            
            if [[ $minutes -lt 5 ]]; then
                echo "FAIL: streamingConnectionIdleTimeout too short"
                echo "Current timeout: $timeout ($minutes minutes)"
                echo "Minimum required: 5 minutes"
                echo "Short timeouts may cause resource exhaustion"
                exit 1
            else
                echo "PASS: streaming-connection-idle-timeout properly configured"
                echo "Current timeout: $timeout ($minutes minutes)"
                echo "Timeout meets minimum requirement of 5 minutes"
                exit 0
            fi
        fi
    else
        echo "FAIL: Kubelet config file missing"
        echo "Configuration file does not exist at: $config_file"
        echo "Expected kubelet configuration file must exist for timeout verification"
        exit 1
    fi