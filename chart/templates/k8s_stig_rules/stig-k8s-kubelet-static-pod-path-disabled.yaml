apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-static-pod-path-disabled
  namespace: compliance-system
spec:
  id: "V-242397"
  title: "The Kubernetes kubelet staticPodPath must not enable static pods."
  description: "Allowing kubelet to set a staticPodPath gives containers with root access permissions to traverse the hosting filesystem. The danger comes when the container can create a manifest file within the /etc/kubernetes/manifests directory. When a manifest is created within this directory, containers are entirely governed by the Kubelet not the API Server. The container is not susceptible to admission control at all. Any containers or pods instantiated in this manner are called \"static pods\" and are meant to be used for pods such as the API server, scheduler, controller, etc., not workload pods that need to be governed by the API Server."
  checkText: "If staticPodPath is missing in the Kubelet config and in the systemd arguments, the node does not support static pods.\n\n1. To find the staticPodPath setting on Kubernetes worker nodes, follow these steps:\n\n a. On the Worker nodes, run the command:\n     ps -ef | grep kubelet\n\nb. Note the path to the Kubelet configuration file (identified by --config).\n    (ls /var/lib/kubelet/config.yaml is the common location.)\n\nc. Run the command:\n    grep -i staticPodPath <path_to_config_file>\n\nIf any of the Worker nodes return a value for \"staticPodPath\", this is a finding.\n\nIf staticPodPath is not in the config file, check if it is set as a command-line argument.\n\n2. Check Kubelet Systemd Service Arguments.\n\na. Run the following command to check the Kubelet service:\n    sudo systemctl cat kubelet | grep pod-manifest-path\n\nIf there is no output, staticPodPath is not set in systemd arguments.\n\nIf there is any return, this is a finding."
  fixText: "1. Remove staticPodPath setting on Kubernetes worker nodes:\n\na. On each Worker node, run the command:\n    ps -ef | grep kubelet\n\nb. Note the path to the config file (identified by --config).\n\nc. Edit the Kubernetes kubelet file in the --config directory on the Worker nodes. Remove the setting \"staticPodPath\".\n\nd. Restart the kubelet service using the following command:\n    systemctl daemon-reload && systemctl restart kubelet\n\n2. Remove Kubelet Systemd Service Arguments:\n\na. Modify the systemd Service File. Run the command:\n    sudo systemctl edit --full kubelet\n\n(Example Return:ExecStart=/usr/bin/kubelet --pod-manifest-path=/etc/kubernetes/manifests)\n\nb. Find and remove --pod-manifest-path.\n\nc. Save and exit the editor.\n\nd. Restart the kubelet service using the following command:\n    systemctl daemon-reload && systemctl restart kubelet"
  severity: "high"
  checkType: "node"
  nodeScope: "all"
  checkScript: |
    #!/bin/bash
    
    echo "Starting kubelet static pod path disabled check (V-242397)"
    
    # Define kubelet config file path with /host prefix for container environment
    config_file="/host/var/lib/kubelet/config.yaml"
    
    echo "Checking kubelet configuration file: $config_file"
    
    # Check if config file exists
    if [ -f "$config_file" ]; then
        echo "Found kubelet configuration file"
        
        # Check for staticPodPath setting in config file
        static_path=$(grep 'staticPodPath' "$config_file" 2>/dev/null)
        
        if [ -n "$static_path" ]; then
            echo "FAIL: Static pod path configured: $static_path"
            echo "Static pod path should not be configured on worker nodes"
            echo "This allows containers to bypass API Server admission control"
            exit 1
        else
            echo "PASS: No static pod path configured"
            echo "No staticPodPath setting found in kubelet configuration"
            echo "Worker node properly configured without static pod support"
            exit 0
        fi
    else
        echo "FAIL: Kubelet config file missing"
        echo "Configuration file does not exist at: $config_file"
        echo "Expected kubelet configuration file must exist for staticPodPath verification"
        exit 1
    fi
