apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: compliance-operator-role
rules:
- apiGroups:
  - apps
  - extensions.k8s.io
  resources:
  - deployments
  - daemonsets
  - replicasets
  - statefulsets
  - deployments/status
  - daemonsets/status
  - replicasets/status
  - statefulsets/status
  verbs:
  - "*"
- apiGroups:
  - batch
  resources:
  - jobs
  - cronjobs
  - jobs/status
  - cronjobs/status
  verbs:
  - "*"
- apiGroups:
  - ""
  resources:
  - configmaps/status
  - configmaps/finalizers
  - configmaps
  - pods
  - pods/log
  - nodes
  - namespaces
  - events
  - services
  - jobs
  - cronjobs
  - jobs/status
  - cronjobs/status
  - resourcequotas
  - persistentvolumeclaims
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
  - deletecollection
- apiGroups:
  - compliance-operator.alauda.io
  resources:
  - profilebundles
  - profiles
  - rules
  - scans
  - checkresults
  - compliancesuites
  - profilebundles/status
  - scans/status
  - compliancesuites/status
  - checkresults/status
  verbs:
  - "*"
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - create
  - delete
  - get
  - patch
  - update
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - get
  - list
  - watch
---
# Namespace-level Role for leader election
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: compliance-operator-leader-election-role
  namespace: {{ .Values.global.complianceNamespace }}
rules:
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - "*"
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - "*"
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: compliance-operator-leader-election-rolebinding
  namespace: {{ .Values.global.complianceNamespace }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: compliance-operator-leader-election-role
subjects:
- kind: ServiceAccount
  name: compliance-operator
  namespace: {{ .Values.global.complianceNamespace }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: compliance-operator-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: compliance-operator-role
subjects:
- kind: ServiceAccount
  name: compliance-operator
  namespace: {{ .Values.global.complianceNamespace }}
---
# Scanner service account and RBAC
apiVersion: v1
kind: ServiceAccount
metadata:
  name: compliance-scanner
  namespace: {{ .Values.global.complianceNamespace }}
---
# 最大权限的 ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: compliance-scanner-admin
rules:
- apiGroups: ["*"]
  resources: ["*"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources:
  - configmaps
  - pods
  - pods/log
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - create
  - delete
  - get
  - patch
  - update
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - get
  - list
  - watch
---
# ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: compliance-scanner-admin-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: compliance-scanner-admin
subjects:
- kind: ServiceAccount
  name: compliance-scanner
  namespace: {{ .Values.global.complianceNamespace }}