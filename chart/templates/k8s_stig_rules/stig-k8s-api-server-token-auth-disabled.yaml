apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-token-auth-disabled
  namespace: compliance-system
spec:
  id: "V-245543"
  title: "Kubernetes API Server must disable token authentication to protect information in transit."
  description: "Kubernetes API Server must disable token authentication to prevent credential interception."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nRemove the setting \"--token-auth-file\"."
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i token-auth-file * \n\nIf \"--token-auth-file\" is set in the Kubernetes API server manifest file, this is a finding."
  severity: "high"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting API Server token authentication check (V-245543)"
    
    manifest_path="/host/etc/kubernetes/manifests/kube-apiserver.yaml"
    
    echo "Checking API Server manifest file: $manifest_path"

    if [[ ! -f "$manifest_path" ]]; then
        echo "FAIL: API Server manifest missing at $manifest_path"
        echo "Expected to find kube-apiserver.yaml manifest file"
        exit 1
    fi
    
    echo "Found API Server manifest file"
    echo "Checking for --token-auth-file configuration..."
    
    # Check for token authentication configuration (preserving original logic)
    if grep -qE -- "--token-auth-file(=\S+|\s+\S+)" "$manifest_path" 2>/dev/null; then
        echo "FAIL: Insecure static token authentication configured"
        echo "Found --token-auth-file parameter in manifest"
        
        # Show the actual configuration found
        token_config=$(grep -E -- "--token-auth-file(=\S+|\s+\S+)" "$manifest_path" 2>/dev/null)
        echo "Configuration found: $token_config"
        echo "Static token authentication must be disabled for security"
        exit 1
    else
        echo "No --token-auth-file configuration found"
        echo "PASS: Secure authentication mechanism enforced"
        echo "Static token authentication is properly disabled"
        exit 0
    fi 