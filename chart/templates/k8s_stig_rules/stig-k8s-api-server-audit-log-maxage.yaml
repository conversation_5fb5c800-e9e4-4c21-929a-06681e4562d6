apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-audit-log-maxage
  namespace: compliance-system
spec:
  id: "V-242464"
  title: "The Kubernetes API Server audit log retention must be set."
  description: "The Kubernetes API Server must set enough storage to retain logs for monitoring suspicious activity and system misconfiguration, and provide evidence for Cyber Security Investigations."
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i audit-log-maxage * \n\nIf the setting \"audit-log-maxage\" is not set in the Kubernetes API Server manifest file or it is set less than \"30\", this is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of \"--audit-log-maxage\" to a minimum of \"30\"."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Checking API Server audit log max age configuration..."
    manifest_file="/host/etc/kubernetes/manifests/kube-apiserver.yaml"
    echo "Checking API Server manifest: $manifest_file"

    # Check if manifest file exists
    if [[ ! -f "$manifest_file" ]]; then
        echo "ERROR: API Server manifest file not found"
        echo "FAIL: API Server manifest missing"
        exit 1
    fi

    echo "Found API Server manifest file"

    # Check audit log max age configuration
    echo "Searching for audit-log-maxage parameter..."
    max_age=$(grep -i "\-\-audit-log-maxage" "$manifest_file" | awk -F'=' '{print $2}' 2>/dev/null | tr -d ' ')
    
    if [[ -z "$max_age" ]]; then
        echo "VIOLATION: audit-log-maxage parameter not configured"
        echo "FAIL: audit-log-maxage not configured"
        exit 1
    fi

    echo "Found audit-log-maxage configuration: $max_age"

    # Validate max age value is numeric
    if ! [[ "$max_age" =~ ^[0-9]+$ ]]; then
        echo "ERROR: Invalid audit-log-maxage value (not numeric): $max_age"
        echo "FAIL: Invalid audit-log-maxage value"
        exit 1
    fi

    # Check if max age meets minimum requirement (≥30 days)
    if [[ $max_age -lt 30 ]]; then
        echo "VIOLATION: audit-log-maxage is less than 30 days (current: $max_age)"
        echo "FAIL: audit-log-maxage <30 days (current: $max_age)"
        exit 1
    fi

    echo "OK: audit-log-maxage meets ≥30 day requirement (current: $max_age)"
    echo "PASS: audit-log-maxage properly configured"
    exit 0 