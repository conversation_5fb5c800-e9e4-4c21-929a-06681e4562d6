apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-control-plane-conf-file-ownership
  namespace: compliance-system
spec:
  id: "V-242446"
  title: "Kubernetes control plane configs must be root-owned"
  description: "The Kubernetes admin kubeconfig files contain the arguments and settings for the Control Plane services. These services are controller and scheduler. If these files can be changed, the scheduler will be implementing the changes immediately."
  checkText: "Review the Kubernetes control plane config files by using the command:\n\nstat -c %U:%G /etc/kubernetes/admin.conf | grep -v root:root\nstat -c %U:%G /etc/kubernetes/scheduler.conf | grep -v root:root\nstat -c %U:%G /etc/kubernetes/controller-manager.conf | grep -v root:root\n\nIf any of the commands returns output, this is a finding."
  fixText: "Change the ownership of the control plane config files to root:root by executing the command:\n\nchown root:root /etc/kubernetes/admin.conf\nchown root:root /etc/kubernetes/scheduler.conf\nchown root:root /etc/kubernetes/controller-manager.conf"
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash

    echo "Starting control plane config file ownership check (V-242446)"
    
    # Define critical configuration files (preserving original logic)
    critical_files=(
        "/host/etc/kubernetes/admin.conf"
        "/host/etc/kubernetes/scheduler.conf"
        "/host/etc/kubernetes/controller-manager.conf"
    )

    echo "Checking ownership of control plane configuration files..."
    echo "Required ownership: root:root"

    # Check each critical file (preserving original logic)
    for file in "${critical_files[@]}"; do
        echo "Checking file: $file"
        
        if [[ -f "$file" ]]; then
            echo "File exists, checking ownership..."
            
            # Get file ownership (preserving original logic)
            ownership=$(stat -c '%U:%G' "$file" 2>/dev/null)
            echo "Current ownership: $ownership"
            
            # Validate ownership (preserving original logic)
            if [[ "$ownership" != "root:root" ]]; then
                echo "FAIL: Invalid ownership for $file"
                echo "Expected: root:root, Found: $ownership"
                echo "This file contains sensitive control plane configuration"
                exit 1
            fi
            
            echo "Ownership is correct for $file"
        else
            echo "FAIL: Missing critical file: $file"
            echo "Expected control plane configuration file not found"
            exit 1
        fi
        
        echo "---"
    done

    echo "PASS: All control plane configs have proper ownership"
    exit 0 