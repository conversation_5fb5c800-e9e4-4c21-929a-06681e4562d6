apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-dashboard-disabled
  namespace: compliance-system
spec:
  id: "V-242395"
  title: "Kubernetes Dashboard must be disabled"
  description: "The Kubernetes Dashboard is a web-based user interface for Kubernetes clusters. While it provides a convenient way to manage and monitor cluster resources, it also introduces security risks. The dashboard has had security vulnerabilities in the past, and if not properly secured, it could provide an attacker with access to the cluster."
  checkText: "On the Control Plane, run the command:\nkubectl get pods --all-namespaces -l k8s-app=kubernetes-dashboard\n\nIf any pods are returned, this is a finding."
  fixText: "Remove the Kubernetes Dashboard by running the command:\nkubectl delete deployment kubernetes-dashboard --namespace=kube-system"
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting Kubernetes Dashboard disabled check (V-242395)"
    
    echo "Checking for Kubernetes Dashboard pods across all namespaces..."
    
    # Check dashboard related resources (preserving original logic)
    dashboard_pods=$(kubectl get pods --all-namespaces -l k8s-app=kubernetes-dashboard 2>/dev/null)
    
    if [[ -n "$dashboard_pods" ]]; then
        echo "FAIL: Active dashboard pods detected"
        echo "Found dashboard pods: $dashboard_pods"
        echo "Kubernetes Dashboard must be disabled for security compliance"
        exit 1
    fi
    
    echo "No dashboard pods found"
    echo "Checking for Kubernetes Dashboard namespace..."
    
    # Check dashboard namespace (preserving original logic)
    dashboard_ns=$(kubectl get namespace kubernetes-dashboard 2>/dev/null)
    if [[ -n "$dashboard_ns" ]]; then
        echo "FAIL: Kubernetes dashboard namespace exists"
        echo "Dashboard namespace should be removed for complete cleanup"
        echo "Run: kubectl delete namespace kubernetes-dashboard"
        exit 1
    fi
    
    echo "No dashboard namespace found"
    echo "PASS: No dashboard components found"
    exit 0 