apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-basic-auth-disabled
  namespace: compliance-system
spec:
  id: "V-245542"
  title: "Kubernetes API Server must disable basic authentication to protect information in transit."
  description: "Kubernetes API Server must disable basic authentication to prevent credential interception."
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i basic-auth-file * \n\nIf \"basic-auth-file\" is set in the Kubernetes API server manifest file this is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Remove the setting \"--basic-auth-file\"."
  severity: "high"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Checking API Server basic authentication configuration..."
    manifest_path="/host/etc/kubernetes/manifests/kube-apiserver.yaml"
    echo "Checking API Server manifest: $manifest_path"

    # Check if manifest file exists
    if [[ ! -f "$manifest_path" ]]; then
        echo "ERROR: API Server manifest file not found"
        echo "FAIL: API Server manifest missing"
        exit 1
    fi

    echo "Found API Server manifest file"

    # Check for basic authentication configuration
    echo "Searching for basic-auth-file parameter..."
    
    if grep -qE -- "--basic-auth-file(=\S+|\s+\S+)" "$manifest_path" 2>/dev/null; then
        echo "VIOLATION: Basic authentication is configured"
        basic_auth_config=$(grep -E -- "--basic-auth-file(=\S+|\s+\S+)" "$manifest_path" 2>/dev/null)
        echo "  Found configuration: $basic_auth_config"
        echo "FAIL: Insecure basic authentication configured"
        exit 1
    fi

    echo "OK: No basic authentication configuration found"
    echo "OK: Secure authentication mechanism enforced"
    echo "PASS: Secure authentication mechanism enforced"
    exit 0 