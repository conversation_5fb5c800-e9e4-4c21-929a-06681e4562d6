apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-etcd-cafile
  namespace: compliance-system
spec:
  id: "V-242429"
  title: "Kubernetes etcd must have the SSL Certificate Authority set."
  description: "Kubernetes stores configuration and state information in a distributed key-value store called etcd. Anyone who can write to etcd can effectively control a Kubernetes cluster. Even just reading the contents of etcd could easily provide helpful hints to a would-be attacker. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server and etcd with a means to be able to authenticate sessions and encrypt traffic. \n\nTo enable encrypted communication for etcd, the parameter \"--etcd-cafile\" must be set. This parameter gives the location of the SSL Certificate Authority file used to secure etcd communication."
  checkText: "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\ngrep -i etcd-cafile * \n\nIf the setting \"--etcd-cafile\" is not configured in the Kubernetes API Server manifest file, this is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--etcd-cafile\" to the Certificate Authority for etcd."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Checking API Server etcd CA file configuration..."
    manifest="/host/etc/kubernetes/manifests/kube-apiserver.yaml"
    required_param="etcd-cafile"
    echo "Checking API Server manifest: $manifest"
    echo "Required parameter: --$required_param"

    # Check if manifest file exists
    if [[ ! -f "$manifest" ]]; then
        echo "ERROR: API Server manifest file not found"
        echo "FAIL: API Server manifest missing"
        exit 1
    fi

    echo "Found API Server manifest file"

    # Check if etcd-cafile parameter exists
    echo "Searching for $required_param parameter..."
    
    if ! grep -q "$required_param" "$manifest" 2>/dev/null; then
        echo "VIOLATION: Missing $required_param configuration"
        echo "FAIL: Missing $required_param configuration"
        exit 1
    fi

    echo "Found $required_param parameter in manifest"

    # Extract and validate the CA file path
    echo "Extracting CA file path..."
    ca_path=$(grep -oP "$required_param=\K[^ ]+" "$manifest" 2>/dev/null)
    
    if [[ -z "$ca_path" ]]; then
        echo "ERROR: Could not extract CA file path from $required_param parameter"
        echo "FAIL: Invalid $required_param configuration"
        exit 1
    fi

    echo "Found CA file path: $ca_path"

    # Check if CA certificate file exists (add /host prefix for container environment)
    if [[ "$ca_path" =~ ^/host ]]; then
        host_ca_path="$ca_path"
    else
        host_ca_path="/host$ca_path"
    fi
    echo "Checking if CA certificate file exists: $host_ca_path"
    
    if [[ ! -f "$host_ca_path" ]]; then
        echo "ERROR: CA certificate file not found: $ca_path"
        echo "FAIL: CA certificate file not found: $ca_path"
        exit 1
    fi

    echo "OK: CA certificate file exists"
    echo "OK: API Server etcd-cafile properly configured"
    echo "PASS: API Server etcd-cafile properly configured"
    exit 0 