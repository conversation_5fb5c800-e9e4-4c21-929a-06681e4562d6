apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-user-functionality-separation
  namespace: compliance-system
spec:
  id: "V-242417"
  title: "Kubernetes must separate user functionality."
  description: "Separating user functionality from management functionality is a requirement for all the components within the Kubernetes Control Plane. Without the separation, users may have access to management functions that can degrade the Kubernetes architecture and the services being offered, and can offer a method to bypass testing and validation of functions before introduced into a production environment."
  checkText: "On the Control Plane, run the command:\nkubectl get pods --all-namespaces\n\nReview the namespaces and pods that are returned. Kubernetes system namespaces are kube-node-lease, kube-public, and kube-system.\n\nIf any user pods are present in the Kubernetes system namespaces, this is a finding."
  fixText: "Move any user pods that are present in the Kubernetes system namespaces to user specific namespaces."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Checking for user functionality separation in Kubernetes system namespaces..."
    
    # Define system namespaces that should only contain system components
    sys_namespaces=("kube-system" "kube-public" "kube-node-lease")
    found_violations=false
    
    # Check each system namespace for user pods
    for ns in "${sys_namespaces[@]}"; do
        echo "Checking namespace: $ns"
        
        # Get all pods in system namespace and filter out known system components
        user_pods=$(kubectl get pods -n "$ns" -o jsonpath='{.items[*].metadata.name}' 2>/dev/null | grep -v -E '(kube-apiserver|kube-controller|kube-proxy|kube-scheduler|etcd|core-dns|calico)')
        
        if [[ -n "$user_pods" ]]; then
            echo "  Found user pods in system namespace $ns: $user_pods"
            found_violations=true
        else
            echo "  No user pods found in $ns - compliant"
        fi
    done
    
    if [[ "$found_violations" == "true" ]]; then
        echo "FAILED: User pods found in Kubernetes system namespaces"
        echo "User functionality must be separated from system functionality"
        echo "Move user pods to dedicated user namespaces"
        exit 1
    else
        echo "PASSED: No user pods found in system namespaces"
        echo "User functionality is properly separated from system functionality"
        exit 0
    fi