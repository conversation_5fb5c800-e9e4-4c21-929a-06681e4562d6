apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-dynamic-auditing-disabled
  namespace: compliance-system
spec:
  id: "V-242398"
  title: "Kubernetes must disable DynamicAuditing feature gate"
  description: "Dynamic auditing allows for the configuration of audit webhooks through an API rather than through the command line. This feature was deprecated in Kubernetes v1.19 and removed in v1.22. Using deprecated features can lead to security vulnerabilities as they may not receive security updates."
  checkText: "Check if the DynamicAuditing feature gate is enabled in the API server and kubelet configurations. If it is enabled, this is a finding."
  fixText: "Remove the DynamicAuditing=true setting from any feature-gates parameters in the API server and kubelet configurations."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting Kubernetes DynamicAuditing disabled check (V-242398)"
    
    # Define manifests directory path (preserving original logic)
    manifests_dir="/host/etc/kubernetes/manifests"
    
    echo "Checking control plane manifests for DynamicAuditing feature gate..."
    echo "Manifests directory: $manifests_dir"
    
    # Check control plane configuration files (preserving original logic)
    found_issue=false
    if grep -ir --include=*.yaml "feature-gates.*DynamicAuditing=true" "$manifests_dir" 2>/dev/null | grep -v "^#" >/dev/null; then
        found_issue=true
    fi
    
    if [[ "$found_issue" == "true" ]]; then
        echo "FAIL: DynamicAuditing enabled in control plane manifests"
        echo "Found DynamicAuditing=true in Kubernetes manifests"
        echo "This deprecated feature must be disabled for security"
        exit 1
    fi
    
    echo "Control plane manifests check passed"
    echo "Checking kubelet process arguments for DynamicAuditing..."
    
    # Check kubelet process arguments (preserving original logic)
    kubelet_cmd=$(chroot /host ps -ef | grep kubelet | grep -v grep 2>/dev/null)
    if echo "$kubelet_cmd" | grep -q -- "--feature-gates" && echo "$kubelet_cmd" | grep -q "DynamicAuditing=true"; then
        echo "FAIL: DynamicAuditing enabled in kubelet process arguments"
        echo "Found DynamicAuditing=true in kubelet command line"
        echo "Remove DynamicAuditing from kubelet --feature-gates parameter"
        exit 1
    fi
    
    echo "Kubelet process arguments check passed"
    echo "Checking kubelet configuration file for DynamicAuditing..."
    
    # Check kubelet configuration file (preserving original logic)
    config_file=$(echo "$kubelet_cmd" | grep -o -- "--config=[^ ]*" | cut -d= -f2 2>/dev/null)
    if [[ -n "$config_file" && ! "$config_file" =~ ^/host ]]; then
        config_file="/host$config_file"
    fi
    [[ -z "$config_file" ]] && config_file="/host/var/lib/kubelet/config.yaml"
    
    echo "Kubelet config file: $config_file"
    
    if [[ -f "$config_file" ]]; then
        echo "Found kubelet config file, checking for DynamicAuditing..."
        if grep -q "featureGates" "$config_file" 2>/dev/null && grep -q "DynamicAuditing.*true" "$config_file" 2>/dev/null; then
            echo "FAIL: DynamicAuditing enabled in kubelet config file"
            echo "Found DynamicAuditing=true in kubelet configuration"
            echo "Remove DynamicAuditing from featureGates in config file"
            exit 1
        fi
        echo "Kubelet config file check passed"
    else
        echo "Kubelet config file not found, skipping config file check"
    fi
    
    echo "PASS: DynamicAuditing properly disabled"
    exit 0 