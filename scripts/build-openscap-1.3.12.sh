#!/bin/bash

# OpenSCAP 1.3.12 镜像构建脚本
# 用于构建安全的OpenSCAP 1.3.12版本镜像

set -e

echo "=== OpenSCAP 1.3.12 镜像构建脚本 ==="
echo "时间: $(date)"
echo

# 配置变量
REGISTRY="build-harbor.alauda.cn"
NAMESPACE="test/compliance"
IMAGE_NAME="openscap-scanner"
TAG="1.3.12"
FULL_IMAGE_NAME="${REGISTRY}/${NAMESPACE}/${IMAGE_NAME}:${TAG}"

# 检查Docker是否可用
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装或不可用"
    exit 1
fi

echo "构建镜像: $FULL_IMAGE_NAME"
echo "基础版本: OpenSCAP 1.3.12"
echo

# 切换到项目根目录
cd "$(dirname "$0")/.."

# 构建镜像
echo "=== 开始构建镜像 ==="
docker build \
    -f images/openscap-scanner/Dockerfile.1.3.12 \
    -t "$FULL_IMAGE_NAME" \
    images/openscap-scanner/

if [ $? -eq 0 ]; then
    echo "✅ 镜像构建成功: $FULL_IMAGE_NAME"
else
    echo "❌ 镜像构建失败"
    exit 1
fi

# 验证镜像
echo "=== 验证镜像 ==="
docker run --rm "$FULL_IMAGE_NAME" oscap --version

# 推送镜像
echo "=== 推送镜像到仓库 ==="
if docker push "$FULL_IMAGE_NAME"; then
    echo "✅ 镜像推送成功: $FULL_IMAGE_NAME"
else
    echo "❌ 镜像推送失败"
    exit 1
fi

# 创建最新标签
LATEST_TAG="${REGISTRY}/${NAMESPACE}/${IMAGE_NAME}:1.3.12-latest"
docker tag "$FULL_IMAGE_NAME" "$LATEST_TAG"
docker push "$LATEST_TAG"

echo "✅ 构建完成"
echo "镜像标签:"
echo "  - $FULL_IMAGE_NAME"
echo "  - $LATEST_TAG"
echo
echo "使用方法:"
echo "  在Controller中使用: registry.alauda.cn:60070/test/compliance/openscap-scanner:1.3.12"
echo
echo "=== 构建脚本完成 ===" 