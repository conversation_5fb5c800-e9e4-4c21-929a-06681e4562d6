apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-controller-manager-root-ca-file
  namespace: compliance-system
spec:
  id: "V-242421"
  title: "Kubernetes Controller Manager must have the SSL Certificate Authority set."
  description: "The Kubernetes Controller Manager is responsible for creating service accounts and tokens for the API Server, maintaining the correct number of pods for every replication controller and provides notifications when nodes are offline.  \n\nAny<PERSON> who gains access to the Controller Manager can generate backdoor accounts, take possession of, or diminish system performance without detection by disabling system notification. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes Controller Manager with a means to be able to authenticate sessions and encrypt traffic."
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i root-ca-file *\n\nIf the setting \"--root-ca-file\" is not set in the Kubernetes Controller Manager manifest file or contains no value, this is a finding."
  fixText: "Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--root-ca-file\" to path containing Approved Organizational Certificate."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting Controller Manager SSL Certificate Authority check (V-242421)"
    
    # Define Controller Manager manifest file path (preserving original logic)
    cm_file="/host/etc/kubernetes/manifests/kube-controller-manager.yaml"
    
    echo "Checking Controller Manager manifest file: $cm_file"
    
    # Check if manifest file exists (preserving original logic)
    if [[ ! -f "$cm_file" ]]; then
        echo "FAIL: No Controller Manager manifest found at $cm_file"
        echo "Expected to find kube-controller-manager.yaml manifest file"
        exit 1
    fi
    
    echo "Found Controller Manager manifest file"
    echo "Searching for --root-ca-file configuration..."
    
    # Check for root-ca-file configuration (preserving original logic)
    if ! grep -iq -- "--root-ca-file" "$cm_file" 2>/dev/null; then
        echo "FAIL: root-ca-file not configured in $cm_file"
        echo "The --root-ca-file parameter must be set for SSL Certificate Authority"
        echo "This parameter is required for secure service account token verification"
        exit 1
    fi
    
    # Show the actual configuration found
    root_ca_config=$(grep -i -- "--root-ca-file" "$cm_file" 2>/dev/null)
    echo "Found root-ca-file configuration: $root_ca_config"
    
    echo "PASS: Controller Manager CA properly configured"
    exit 0 