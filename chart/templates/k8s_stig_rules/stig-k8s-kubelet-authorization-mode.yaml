apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-authorization-mode
  namespace: compliance-system
spec:
  id: "V-242392"
  title: "The Kubernetes kubelet must enable explicit authorization."
  description: "<PERSON><PERSON><PERSON> is the primary agent on each node. The API server communicates with each kubelet to perform tasks such as starting/stopping pods. By default, kubelets allow all authenticated requests, even anonymous ones, without requiring any authorization checks from the API server. This default behavior bypasses any authorization controls put in place to limit what users may perform within the Kubernetes cluster. To change this behavior, the default setting of AlwaysAllow for the authorization mode must be set to \"Webhook\"."
  checkText: "Run the following command on each Worker Node:\nps -ef | grep kubelet\nVerify that the --authorization-mode exists and is set to \"Webhook\".\n\nIf the --authorization-mode argument is not set to \"Webhook\" or doesn't exist, this is a finding."
  fixText: "Edit the Kubernetes Kubelet service file in the --config directory on the Kubernetes Worker Node:\n\nSet the value of \"--authorization-mode\" to \"Webhook\" in KUBELET_SYSTEM_PODS_ARGS variable.\n\nRestart the kubelet service using the following command:\n\nsystemctl daemon-reload && systemctl restart kubelet"
  severity: "high"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting kubelet authorization mode check (V-242392)"
    
    # Define kubelet config file path (preserving original logic)
    config_file="/host/var/lib/kubelet/config.yaml"
    
    echo "Checking kubelet configuration file: $config_file"
    
    # Check if config file exists
    if [[ ! -f "$config_file" ]]; then
        echo "FAIL: Kubelet config file not found"
        echo "Config file does not exist at: $config_file"
        exit 1
    fi
    
    echo "Found kubelet configuration file"
    echo "Checking authorization mode setting..."
    
    # Check authorization.mode setting in config file (preserving original logic)
    mode=$(yq '.authorization.mode' "$config_file" 2>/dev/null | tr -d '"')
    
    echo "Authorization mode setting: $mode"
    echo "Required setting: Webhook"
    
    # Validate authorization mode setting (preserving original logic)
    if [[ "$mode" == "Webhook" ]]; then
        echo "PASS: Authorization mode correctly set to Webhook"
        echo "Kubelet authorization is properly configured"
        exit 0
    elif [[ -z "$mode" ]]; then
        echo "FAIL: Authorization mode not configured"
        echo "Authorization mode field missing (authorization.mode field missing)"
        echo "Default AlwaysAllow mode may be in effect, which bypasses authorization"
        exit 1
    else
        echo "FAIL: Authorization mode does not meet requirements"
        echo "Current value: $mode"
        echo "Required value: Webhook"
        echo "Authorization mode must be set to Webhook for security compliance"
        exit 1
    fi