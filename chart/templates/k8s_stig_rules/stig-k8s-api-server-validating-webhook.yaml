apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-validating-webhook
  namespace: compliance-system
spec:
  id: "V-242436"
  title: "The Kubernetes API server must have the ValidatingAdmissionWebhook enabled."
  description: "Enabling the admissions webhook allows for Kubernetes to apply policies against objects that are to be created, read, updated, or deleted. By applying a pod security policy, control can be given to not allow images to be instantiated that run as the root user. If pods run as the root user, the pod then has root privileges to the host system and all the resources it has. An attacker can use this to attack the Kubernetes cluster. By implementing a policy that does not allow root or privileged pods, the pod users are limited in what the pod can do and access."
  checkText: "Prior to version 1.21, to enforce security policiesPod Security Policies (psp) were used. Those are now deprecated and will be removed from version 1.25.\n\nMigrate from PSP to PSA:\nhttps://kubernetes.io/docs/tasks/configure-pod-container/migrate-from-psp/ \n\nPre-version 1.25 Check:\nChange to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\ngrep -i ValidatingAdmissionWebhook * \n\nIf a line is not returned that includes enable-admission-plugins and ValidatingAdmissionWebhook, this is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument \"--enable-admission-plugins\" to include \"ValidatingAdmissionWebhook\".  Each enabled plugin is separated by commas.\n\nNote: It is best to implement policies first and then enable the webhook, otherwise a denial of service may occur."
  severity: "high"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "MANUAL: This check is not applicable for Kubernetes versions 1.25 and newer"
    echo "Current version ($kube_api_version) uses Pod Security Admission (PSA) instead of Pod Security Policies (PSP)"
    echo "Please manually verify that Pod Security Standards are properly configured:"
    echo "1. Check for Pod Security Admission configuration in API server"
    echo "2. Verify namespace-level pod-security labels are set"
    echo "3. Confirm security policies are enforced via PSA instead of PSP"
    echo "For more information: https://kubernetes.io/docs/concepts/security/pod-security-admission/"
    exit 2