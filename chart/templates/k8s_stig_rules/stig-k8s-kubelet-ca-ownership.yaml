apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-ca-ownership
  namespace: compliance-system
spec:
  id: "V-242450"
  title: "Kubernetes Kubelet CA must be root-owned"
  description: "Kubernetes Kubelet CA file must be owned by root to prevent unauthorized modifications."
  fixText: "On the Control Plane, run the command:\nps -ef | grep kubelet\n\nRemove the \"--client-ca-file\" option.\n\nNote the path to the config file (identified by --config).\n\nRun the command:\ngrep -i clientCAFile <path_to_config_file>\n\nNote the path to the client ca file.\n\nRun the command:\nchown root:root <path_to_client_ca_file>"
  checkText: "On the Control Plane, run the command:\nps -ef | grep kubelet\n\nIf the \"--client-ca-file\" option exists, this is a finding. \n\nNote the path to the config file (identified by --config).\n\nRun the command:\ngrep -i clientCAFile <path_to_config_file>\n\nNote the path to the client ca file.\n\nRun the command:\nstat -c %U:%G <path_to_client_ca_file>\n\nIf the command returns any non root:root file permissions, this is a finding."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting kubelet CA file ownership check (V-242450)"
    
    # Check kubelet process arguments (preserving original logic)
    echo "Checking kubelet process arguments..."
    kubelet_cmd=$(chroot /host ps -ef | grep kubelet | grep -v grep)
    
    if [[ -z "$kubelet_cmd" ]]; then
        echo "FAIL: Kubelet process not found"
        echo "Unable to locate running kubelet process"
        exit 1
    fi
    
    echo "Found kubelet process"
    
    # Define config file path (preserving original logic)
    config_file="/host/var/lib/kubelet/config.yaml"
    
    echo "Checking kubelet configuration file: $config_file"
    
    # Check if config file exists
    if [[ ! -f "$config_file" ]]; then
        echo "FAIL: Kubelet config file not found"
        echo "Config file does not exist at: $config_file"
        exit 1
    fi
    
    echo "Found kubelet configuration file"
    echo "Checking authentication.x509.clientCAFile setting..."
    
    # Check config file for CA file path (preserving original logic)
    ca_file=$(yq '.authentication.x509.clientCAFile' "$config_file" 2>/dev/null)
    
    if [[ -z "$ca_file" || "$ca_file" == "null" ]]; then
        echo "FAIL: Client CA file not configured"
        echo "authentication.x509.clientCAFile setting is missing or null"
        echo "Kubelet client CA file must be configured for proper authentication"
        exit 1
    fi
    
    echo "Found CA file configuration: $ca_file"
    echo "Checking CA file existence and ownership..."
    
    # Ensure CA file has /host prefix for container access
    actual_ca_file=""
    if [[ -f "$ca_file" ]]; then
        actual_ca_file="$ca_file"
    elif [[ -f "/host$ca_file" ]]; then
        actual_ca_file="/host$ca_file"
    else
        echo "FAIL: Client CA file not found"
        echo "Checked paths:"
        echo "  - $ca_file"
        echo "  - /host$ca_file"
        exit 1
    fi
    
    echo "Using CA file: $actual_ca_file"
    
    # Check CA file ownership (preserving original logic)
    ownership=$(stat -c '%U:%G' "$actual_ca_file" 2>/dev/null)
    
    if [[ -z "$ownership" ]]; then
        echo "FAIL: Unable to read CA file ownership"
        echo "Cannot determine ownership for: $actual_ca_file"
        exit 1
    fi
    
    echo "Current CA file ownership: $ownership"
    echo "Required ownership: root:root"
    
    # Validate ownership (preserving original logic)
    if [[ "$ownership" != "root:root" ]]; then
        echo "FAIL: Invalid CA ownership"
        echo "CA file: $actual_ca_file"
        echo "Current ownership: $ownership"
        echo "Required ownership: root:root"
        echo "CA file must be owned by root:root for security compliance"
        exit 1
    else
        echo "PASS: Kubelet CA has proper ownership"
        echo "CA file: $actual_ca_file"
        echo "Ownership: $ownership (compliant)"
        exit 0
    fi