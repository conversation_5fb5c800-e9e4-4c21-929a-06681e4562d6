apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-audit-log-maxbackup
  namespace: compliance-system
spec:
  id: "V-242463"
  title: "The Kubernetes API Server must be set to audit log maximum backup."
  description: "The Kubernetes API Server must set enough storage to retain logs for monitoring suspicious activity and system misconfiguration, and provide evidence for Cyber Security Investigations."
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i audit-log-maxbackup * \n\nIf the setting \"audit-log-maxbackup\" is not set in the Kubernetes API Server manifest file or it is set less than \"10\", this is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of \"--audit-log-maxbackup\" to a minimum of \"10\"."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Checking API Server audit log max backup configuration..."
    manifest_file="/host/etc/kubernetes/manifests/kube-apiserver.yaml"
    echo "Checking API Server manifest: $manifest_file"

    # Check if manifest file exists
    if [[ ! -f "$manifest_file" ]]; then
        echo "ERROR: API Server manifest file not found"
        echo "FAIL: API Server manifest missing"
        exit 1
    fi

    echo "Found API Server manifest file"

    # Check audit log max backup configuration
    echo "Searching for audit-log-maxbackup parameter..."
    max_backup=$(grep -i "\-\-audit-log-maxbackup" "$manifest_file" | awk -F'=' '{print $2}' 2>/dev/null | tr -d ' ')
    
    if [[ -z "$max_backup" ]]; then
        echo "VIOLATION: audit-log-maxbackup parameter not configured"
        echo "FAIL: audit-log-maxbackup not configured"
        exit 1
    fi

    echo "Found audit-log-maxbackup configuration: $max_backup"

    # Validate max backup value is numeric
    if ! [[ "$max_backup" =~ ^[0-9]+$ ]]; then
        echo "ERROR: Invalid audit-log-maxbackup value (not numeric): $max_backup"
        echo "FAIL: Invalid audit-log-maxbackup value"
        exit 1
    fi

    # Check if max backup meets minimum requirement (≥10)
    if [[ $max_backup -lt 10 ]]; then
        echo "VIOLATION: audit-log-maxbackup is less than 10 (current: $max_backup)"
        echo "FAIL: audit-log-maxbackup <10 (current: $max_backup)"
        exit 1
    fi

    echo "OK: audit-log-maxbackup meets ≥10 requirement (current: $max_backup)"
    echo "PASS: audit-log-maxbackup properly configured"
    exit 0 