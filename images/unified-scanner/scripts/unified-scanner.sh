#!/bin/bash

# Unified compliance scanner script for both platform and node checks
set -eo pipefail

# Extract environment variables
RULE_ID="$RULE_ID"
RULE_CHECK_TYPE="${RULE_CHECK_TYPE:-$CHECK_TYPE}"  # Use RULE_CHECK_TYPE if available, fall back to CHECK_TYPE
CHECK_TYPE="${RULE_CHECK_TYPE:-$CHECK_TYPE}"  # Normalize CHECK_TYPE
SCAN_NAME="$SCAN_NAME"
NAMESPACE="${NAMESPACE:-$SCAN_NAMESPACE}"  # Use SCAN_NAMESPACE if available
NAMESPACE="${NAMESPACE:-compliance-system}"  # Default to compliance-system
SCAN_ID="${SCAN_ID}"  # Get scan ID from environment variable

# Aggregated mode environment variables
SCAN_MODE="${SCAN_MODE:-individual}"  # Default to individual mode
RULE_COUNT="${RULE_COUNT:-0}"  # Number of rules in aggregated mode
RULE_NAMES="${RULE_NAMES:-}"  # Comma-separated rule names

# Get the actual job name from environment variable or extract from hostname
# We should use the Job name (not Pod name) for ConfigMap naming and labels
if [ -n "$JOB_NAME" ]; then
    # Use provided JOB_NAME environment variable (this is the actual Job name)
    ACTUAL_JOB_NAME="$JOB_NAME"
else
    # Fallback: extract job name from hostname by removing pod suffix
    # Pod name format: {job-name}-{pod-suffix}
    # Kubernetes adds 5-character random suffix to pod names
    HOSTNAME_VAR=${HOSTNAME:-"unknown"}
    if [ ${#HOSTNAME_VAR} -gt 6 ]; then
        # Remove the last 6 characters (dash + 5 random chars) if they match pattern
        if echo "$HOSTNAME_VAR" | grep -q '.*-[a-z0-9]\{5\}$'; then
            ACTUAL_JOB_NAME="${HOSTNAME_VAR%-*}"
        else
            ACTUAL_JOB_NAME="$HOSTNAME_VAR"
        fi
    else
        ACTUAL_JOB_NAME="$HOSTNAME_VAR"
    fi
fi

# Keep the original JOB_NAME for reference
ORIGINAL_JOB_NAME="$JOB_NAME"

# Environment variables
NODE_NAME=${NODE_NAME:-""}

# Log function with context-aware prefixes
log() {
    local prefix="[PLATFORM]"
    if [ "$CHECK_TYPE" = "node" ]; then
        prefix="[NODE: ${NODE_NAME}]"
    fi
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $prefix [${RULE_ID}] $*" >&2
}

# Debug function to show environment
debug_environment() {
    log "=== Unified Scanner Environment Debug ==="
    log "CHECK_TYPE: $CHECK_TYPE"
    log "RULE_ID: $RULE_ID"
    log "SCAN_NAME: $SCAN_NAME"
    log "SCAN_ID: $SCAN_ID"
    log "NODE_NAME: $NODE_NAME"
    log "JOB_NAME: $JOB_NAME"
    log "NAMESPACE: ${NAMESPACE}"
    log "SCAN_MODE: ${SCAN_MODE}"
    log "RULE_COUNT: ${RULE_COUNT}"
    log "RULE_NAMES: ${RULE_NAMES}"
    log "USER: $(whoami)"
    log "PWD: $(pwd)"
    log "Available tools:"
    log "  kubectl: $(which kubectl 2>/dev/null || echo 'NOT FOUND')"
    log "  ps: $(which ps 2>/dev/null || echo 'NOT FOUND')"
    log "  netstat: $(which netstat 2>/dev/null || echo 'NOT FOUND')"
    log "  jq: $(which jq 2>/dev/null || echo 'NOT FOUND')"
    log "  chroot: $(which chroot 2>/dev/null || echo 'NOT FOUND')"
    
    if [ "$CHECK_TYPE" = "node" ]; then
        log "Host filesystem mounts:"
        df -h | grep -E "host" || log "  No host mounts found"
        log "Host directory contents:"
        ls -la /host/ 2>/dev/null | head -5 | while read line; do log "  $line"; done || log "  /host not accessible"
        log "Host /proc contents:"
        ls -la /host/proc/ 2>/dev/null | head -3 | while read line; do log "  $line"; done || log "  /host/proc not accessible"
    else
        log "Platform mounted volumes:"
        df -h | grep -E "(host|proc|etc)" || log "  No special mounts found"
    fi
    # Test exit code capture mechanism
    log "Testing exit code capture mechanism:"
    test_exit_code_capture "exit 0" "0"
    test_exit_code_capture "exit 1" "1"
    test_exit_code_capture "exit 42" "42"
    test_exit_code_capture "echo 'test'; exit 5" "5"
    
    log "=== End Environment Debug ==="
}

# Function to store result in ConfigMap
store_result() {
    local exit_code="$1"
    local output_content="$2"
    
    log "Storing result in ConfigMap..."
    log "Exit code: $exit_code"
    log "Rule ID: $RULE_ID"
    log "Scan name: $SCAN_NAME"
    log "Scan ID: $SCAN_ID"
    log "Original Job name: $ORIGINAL_JOB_NAME"
    log "Actual Job name: $ACTUAL_JOB_NAME"
    log "Check type: $CHECK_TYPE"
    log "Output length: ${#output_content} characters"
    
    # Use the original job name directly as ConfigMap name and in all fields
    local configmap_name="$ACTUAL_JOB_NAME"
    log "Using Job name as ConfigMap name: $configmap_name"
    
    # Clean up the output by removing timestamp prefixes and limiting length
    local clean_output=$(echo "$output_content" | sed 's/^\[.*\] \[.*\] \[.*\] //' | sed 's/^\[.*\] //')
    if [ ${#clean_output} -gt 32768 ]; then
        clean_output="${clean_output:0:32768}... [truncated]"
    fi
    
    # Use base64 encoding to completely avoid character escaping issues
    local escaped_output=""
    if [ -n "$clean_output" ]; then
        # Base64 encode the output to avoid any YAML escaping issues
        escaped_output=$(printf '%s' "$clean_output" | base64 -w 0)
        log "Output encoded to base64, length: ${#escaped_output} characters"
    fi
    
    # Try to create ConfigMap with the result
    set +e  # Disable exit on error for kubectl commands
    
    # Use a more robust approach: create a temporary file with proper YAML formatting
    local temp_file="/tmp/configmap-data-${RULE_ID}.yaml"
    
    # Use provided scan ID or generate one if not provided
    if [ -z "$SCAN_ID" ]; then
        SCAN_ID="${SCAN_NAME}-$(date +%Y%m%d-%H%M%S)-$(cat /dev/urandom | tr -dc 'a-z0-9' | fold -w 4 | head -n 1)"
        log "No SCAN_ID provided, generated: $SCAN_ID"
    fi
    
    # Create ConfigMap with labels for better discovery
    cat > "$temp_file" <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: ${configmap_name}
  namespace: ${NAMESPACE}
  labels:
    compliance-operator.alauda.io/scan: "${SCAN_NAME}"
    compliance-operator.alauda.io/rule: "${RULE_ID}"
    compliance-operator.alauda.io/scan-type: "${CHECK_TYPE}"
    compliance-operator.alauda.io/job: "${configmap_name}"
    compliance-operator.alauda.io/result: "true"
    compliance-operator.alauda.io/scan-id: "${SCAN_ID}"
    compliance-operator.alauda.io/resource-type: "result"
    compliance-operator.alauda.io/temporary: "true"
data:
  exit_code: "${exit_code}"
  rule_id: "${RULE_ID}"
  scan_name: "${SCAN_NAME}"
  job_name: "${configmap_name}"
  check_type: "${CHECK_TYPE}"
  output: "${escaped_output}"
  output_encoding: "base64"
  timestamp: "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
  scan_id: "${SCAN_ID}"
EOF
    
    # Add node-specific data if this is a node scan
    if [ "$CHECK_TYPE" = "node" ] && [ -n "$NODE_NAME" ]; then
        cat >> "$temp_file" <<EOF
  node_name: "${NODE_NAME}"
EOF
    fi
    
    # Apply the ConfigMap
    log "Applying ConfigMap from temp file: $temp_file"
    if kubectl apply -f "$temp_file"; then
        log "Successfully created ConfigMap: $configmap_name"
        rm -f "$temp_file"
    else
        log "Failed to create ConfigMap"
        log "ConfigMap content:"
        cat "$temp_file" >&2
        rm -f "$temp_file"
        return 1
    fi
    
    set -e  # Re-enable exit on error
}

# Function to check if we're running in a node (chroot environment)
is_node_environment() {
    [ -d "/host" ] && [ -d "/host/proc" ] && [ -d "/host/sys" ]
}

# Function to test exit code capture mechanism
test_exit_code_capture() {
    local test_script="$1"
    local expected_code="$2"
    
    log "Testing exit code capture for script: $test_script"
    log "Expected exit code: $expected_code"
    
    # Temporarily disable exit on error for testing
    set +e
    
    local output_file="/tmp/test_output_$$"
    local exit_code_file="/tmp/test_exit_code_$$"
    
    {
        bash -c "$test_script" > "$output_file" 2>&1
        echo $? > "$exit_code_file"
    }
    
    local actual_code=$(cat "$exit_code_file")
    local output=$(cat "$output_file")
    
    rm -f "$output_file" "$exit_code_file"
    
    local test_result=0
    if [ "$actual_code" = "$expected_code" ]; then
        log "Exit code test PASSED: got $actual_code, expected $expected_code"
    else
        log "Exit code test FAILED: got $actual_code, expected $expected_code"
        log "Test output: $output"
        test_result=1
    fi
    
    # Re-enable exit on error
    set -e
    return $test_result
}

# Execute platform check
execute_platform_check() {
    local script="$1"
    local output_file="/tmp/check_output_$$"
    local exit_code_file="/tmp/check_exit_code_$$"
    
    log "Executing platform check script..."
    log "Running platform script with bash -c..."
    
    # Temporarily disable exit on error to capture check script results
    set +e
    
    # Use a more reliable method to capture both output and exit code
    {
        bash -c "$script" > "$output_file" 2>&1
        echo $? > "$exit_code_file"
    }
    
    local result=$(cat "$exit_code_file")
    local output=$(cat "$output_file")
    
    # Cleanup temp files
    rm -f "$output_file" "$exit_code_file"
    
    # Re-enable exit on error
    set -e
    
    log "Platform check completed with result code: $result"
    # Return only the actual script output
    echo "$output"
    return $result
}

# Execute node check
execute_node_check() {
    local script="$1"
    local output_file="/tmp/check_output_$$"
    local exit_code_file="/tmp/check_exit_code_$$"
    
    log "Executing node check script..."
    
    # Temporarily disable exit on error to capture check script results
    set +e
    
    if is_node_environment; then
        log "Running in node environment, using chroot..."
        
        # Prepare enhanced script with embedded yq binary
        log "Preparing enhanced script with embedded tools..."
        
        local enhanced_script=""
        
        # Check if we have the static yq binary available
        if command -v yq-static >/dev/null 2>&1; then
            log "Static yq binary found, embedding in chroot script..."
            
            # Create a base64 encoded version of yq-static for embedding
            local yq_base64=$(base64 -w 0 "$(which yq-static)")
            log "yq binary encoded to base64, length: ${#yq_base64} characters"
            
            # Create enhanced script that decodes and uses embedded yq
            enhanced_script="
# Embedded yq binary setup
if ! command -v yq >/dev/null 2>&1; then
    # Create temporary yq binary from embedded base64
    YQ_TEMP=\"/tmp/yq-embedded-\$\$\"
    echo '$yq_base64' | base64 -d > \"\$YQ_TEMP\" 2>/dev/null && chmod +x \"\$YQ_TEMP\" 2>/dev/null
    if [ -x \"\$YQ_TEMP\" ]; then
        # Create yq function that uses the temporary binary
        yq() { \"\$YQ_TEMP\" \"\$@\"; }
        export -f yq
        # Clean up function
        cleanup_yq() { rm -f \"\$YQ_TEMP\" 2>/dev/null || true; }
        trap cleanup_yq EXIT
    fi
fi

$script"
            log "Enhanced script prepared with embedded yq binary"
        else
            log "Static yq binary not found, using original script"
            enhanced_script="$script"
        fi
        
        # Try chroot approach first
        log "Attempting chroot execution with embedded tools..."
        {
            chroot /host /bin/bash -c "$enhanced_script" > "$output_file" 2>&1
            echo $? > "$exit_code_file"
        }
        
        local result=$(cat "$exit_code_file")
        local output=$(cat "$output_file")
        
        if [ $result -eq 0 ]; then
            log "Chroot execution successful"
            rm -f "$output_file" "$exit_code_file"
            # Re-enable exit on error
            set -e
            echo "$output"
            return $result
        else
            log "Chroot execution failed with code $result, running script directly without path modification..."
            
            # Run the original script directly without any path modifications
            # Rules should handle their own path prefixes for node environments
            log "Running original script directly..."
            
            # Clear previous results and run original script
            rm -f "$output_file" "$exit_code_file"
            {
                bash -c "$script" > "$output_file" 2>&1
                echo $? > "$exit_code_file"
            }
            
            result=$(cat "$exit_code_file")
            output=$(cat "$output_file")
            
            # Cleanup temp files
            rm -f "$output_file" "$exit_code_file"
            
            # Re-enable exit on error
            set -e
            
            log "Direct script execution completed with result code: $result"
            echo "$output"
            return $result
        fi
    else
        log "Not in node environment, running as regular script..."
        # If not in node environment, try to set up environment variables
        local env_script="export PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin; $script"
        
        {
            bash -c "$env_script" > "$output_file" 2>&1
            echo $? > "$exit_code_file"
        }
        
        local result=$(cat "$exit_code_file")
        local output=$(cat "$output_file")
        
        # Cleanup temp files
        rm -f "$output_file" "$exit_code_file"
        
        # Re-enable exit on error
        set -e
        
        log "Environment script execution completed with result code: $result"
        echo "$output"
        return $result
    fi
}

# Execute aggregated scan mode
execute_aggregated_scan() {
    log "Starting aggregated compliance scan mode"
    log "Rule count: ${RULE_COUNT:-0}"
    log "Rule names: ${RULE_NAMES:-none}"

    # Initialize result tracking
    local RESULTS_DIR="/tmp/results"
    mkdir -p "${RESULTS_DIR}"
    local OVERALL_EXIT_CODE=0
    local EXECUTED_RULES=0
    local PASSED_RULES=0
    local FAILED_RULES=0
    local ERROR_RULES=0

    # Read rule metadata
    local RULE_METADATA_FILE="/tmp/rule-scripts/rule_metadata.txt"
    if [ ! -f "${RULE_METADATA_FILE}" ]; then
        log "ERROR: Rule metadata file not found: ${RULE_METADATA_FILE}"
        store_result 1 "Rule metadata file not found"
        exit 1
    fi

    log "Processing rules from metadata file: ${RULE_METADATA_FILE}"

    # Execute each rule
    while IFS=':' read -r rule_index rule_name rule_severity rule_scope; do
        # Skip empty lines
        [ -z "$rule_index" ] && continue

        log ""
        log "=== Executing Rule ${rule_index}: ${rule_name} ==="
        log "Severity: ${rule_severity}"
        log "Node Scope: ${rule_scope}"

        local RULE_SCRIPT="/tmp/rule-scripts/rule_${rule_index}_$(echo ${rule_name} | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]/-/g').sh"
        local RULE_RESULT_FILE="${RESULTS_DIR}/rule_${rule_index}_${rule_name}.result"

        if [ ! -f "${RULE_SCRIPT}" ]; then
            log "ERROR: Rule script not found: ${RULE_SCRIPT}"
            echo "1" > "${RULE_RESULT_FILE}.exit_code"
            echo "Rule script not found" > "${RULE_RESULT_FILE}.output"
            ERROR_RULES=$((ERROR_RULES + 1))
            OVERALL_EXIT_CODE=1

            # Create individual result ConfigMap for this rule
            create_individual_rule_result "${rule_name}" "1" "Rule script not found"
            continue
        fi

        # Make script executable
        chmod +x "${RULE_SCRIPT}" 2>/dev/null || true

        # Execute rule with timeout
        local RULE_EXIT_CODE=0
        local RULE_OUTPUT=""

        log "Executing rule script: ${RULE_SCRIPT}"

        # Temporarily disable exit on error for rule execution
        set +e

        # Execute the rule script based on check type
        case "$CHECK_TYPE" in
            "node")
                RULE_OUTPUT=$(execute_node_check "$(cat "${RULE_SCRIPT}")")
                RULE_EXIT_CODE=$?
                ;;
            "platform")
                RULE_OUTPUT=$(execute_platform_check "$(cat "${RULE_SCRIPT}")")
                RULE_EXIT_CODE=$?
                ;;
            *)
                RULE_OUTPUT="Unknown check type: $CHECK_TYPE"
                RULE_EXIT_CODE=1
                ;;
        esac

        # Re-enable exit on error
        set -e

        # Validate exit code
        if ! [[ "$RULE_EXIT_CODE" =~ ^[0-9]+$ ]]; then
            log "WARNING: Invalid exit code '$RULE_EXIT_CODE' for rule ${rule_name}, defaulting to 1"
            RULE_EXIT_CODE=1
        fi

        # Update counters
        if [ ${RULE_EXIT_CODE} -eq 0 ]; then
            PASSED_RULES=$((PASSED_RULES + 1))
            log "✓ Rule ${rule_name} PASSED"
        else
            FAILED_RULES=$((FAILED_RULES + 1))
            OVERALL_EXIT_CODE=1
            log "✗ Rule ${rule_name} FAILED (exit code: ${RULE_EXIT_CODE})"
        fi

        # Save rule result metadata
        echo "${RULE_EXIT_CODE}" > "${RULE_RESULT_FILE}.exit_code"
        echo "$(date -u +%Y-%m-%dT%H:%M:%SZ)" > "${RULE_RESULT_FILE}.timestamp"
        echo "${RULE_OUTPUT}" > "${RULE_RESULT_FILE}.output"

        # Create individual result ConfigMap for this rule
        create_individual_rule_result "${rule_name}" "${RULE_EXIT_CODE}" "${RULE_OUTPUT}"

        EXECUTED_RULES=$((EXECUTED_RULES + 1))

    done < "${RULE_METADATA_FILE}"

    log ""
    log "=== Aggregated Scan Summary ==="
    log "Total Rules: ${RULE_COUNT:-0}"
    log "Executed: ${EXECUTED_RULES}"
    log "Passed: ${PASSED_RULES}"
    log "Failed: ${FAILED_RULES}"
    log "Errors: ${ERROR_RULES}"
    log "Overall Result: $([ ${OVERALL_EXIT_CODE} -eq 0 ] && echo "PASS" || echo "FAIL")"

    # Create summary result
    cat > "${RESULTS_DIR}/scan_summary.json" << EOF
{
  "scan_id": "${SCAN_ID}",
  "job_name": "${ACTUAL_JOB_NAME}",
  "node_name": "${NODE_NAME}",
  "total_rules": ${RULE_COUNT:-0},
  "executed_rules": ${EXECUTED_RULES},
  "passed_rules": ${PASSED_RULES},
  "failed_rules": ${FAILED_RULES},
  "error_rules": ${ERROR_RULES},
  "overall_exit_code": ${OVERALL_EXIT_CODE},
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
}
EOF

    log "=== Aggregated Compliance Scan Completed ==="

    # Store overall summary (optional, for debugging)
    local summary_output="Aggregated scan completed. Executed: ${EXECUTED_RULES}, Passed: ${PASSED_RULES}, Failed: ${FAILED_RULES}, Errors: ${ERROR_RULES}"

    # Always exit successfully - individual rule results are stored separately
    exit 0
}

# Function to create individual rule result ConfigMap (compatible with existing system)
create_individual_rule_result() {
    local rule_name="$1"
    local exit_code="$2"
    local output_content="$3"

    log "Creating individual result ConfigMap for rule: ${rule_name}"

    # Generate ConfigMap name for this specific rule
    local rule_configmap_name="${ACTUAL_JOB_NAME}-${rule_name}"

    # Clean up the output
    local clean_output=$(echo "$output_content" | sed 's/^\[.*\] \[.*\] \[.*\] //' | sed 's/^\[.*\] //')
    if [ ${#clean_output} -gt 32768 ]; then
        clean_output="${clean_output:0:32768}... [truncated]"
    fi

    # Base64 encode the output to avoid YAML escaping issues
    local escaped_output=""
    if [ -n "$clean_output" ]; then
        escaped_output=$(printf '%s' "$clean_output" | base64 -w 0)
    fi

    # Create temporary file for this rule's ConfigMap
    local temp_file="/tmp/configmap-rule-${rule_name}-$$.yaml"

    cat > "$temp_file" <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: ${rule_configmap_name}
  namespace: ${NAMESPACE}
  labels:
    compliance-operator.alauda.io/scan: "${SCAN_NAME}"
    compliance-operator.alauda.io/rule: "${rule_name}"
    compliance-operator.alauda.io/scan-type: "${CHECK_TYPE}"
    compliance-operator.alauda.io/job: "${ACTUAL_JOB_NAME}"
    compliance-operator.alauda.io/result: "true"
    compliance-operator.alauda.io/scan-id: "${SCAN_ID}"
    compliance-operator.alauda.io/resource-type: "result"
    compliance-operator.alauda.io/temporary: "true"
    compliance-operator.alauda.io/aggregated: "true"
data:
  exit_code: "${exit_code}"
  rule_id: "${rule_name}"
  scan_name: "${SCAN_NAME}"
  job_name: "${ACTUAL_JOB_NAME}"
  check_type: "${CHECK_TYPE}"
  output: "${escaped_output}"
  output_encoding: "base64"
  timestamp: "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
  scan_id: "${SCAN_ID}"
EOF

    # Add node-specific data if this is a node scan
    if [ "$CHECK_TYPE" = "node" ] && [ -n "$NODE_NAME" ]; then
        cat >> "$temp_file" <<EOF
  node_name: "${NODE_NAME}"
EOF
    fi

    # Apply the ConfigMap
    set +e  # Disable exit on error for kubectl
    if kubectl apply -f "$temp_file" >/dev/null 2>&1; then
        log "Successfully created individual result ConfigMap: ${rule_configmap_name}"
    else
        log "Failed to create individual result ConfigMap for rule: ${rule_name}"
    fi
    set -e  # Re-enable exit on error

    rm -f "$temp_file"
}

# Main execution
main() {
    log "Starting unified compliance scan"
    log "Check type: $CHECK_TYPE"
    log "Scan mode: ${SCAN_MODE:-individual}"

    # Show debug information
    debug_environment

    # Create results directory
    mkdir -p /tmp/results

    # Check if we're in aggregated mode
    if [ "${SCAN_MODE}" = "aggregated" ] || [ "$1" = "--mode=aggregated" ]; then
        execute_aggregated_scan
        return $?
    fi

    # Original individual rule execution mode
    if [ $# -gt 0 ] && [ "$1" != "--mode=aggregated" ]; then
        local script="$*"
        local check_output=""
        local check_result=0

        # Route to appropriate execution method based on check type
        # Temporarily disable exit on error for check execution
        set +e

        case "$CHECK_TYPE" in
            "platform")
                check_output=$(execute_platform_check "$script")
                check_result=$?
                ;;
            "node")
                check_output=$(execute_node_check "$script")
                check_result=$?
                ;;
            *)
                log "Unknown check type: $CHECK_TYPE"
                check_output="Unknown check type: $CHECK_TYPE"
                check_result=1
                ;;
        esac

        # Re-enable exit on error
        set -e

        # Validate exit code is numeric
        if ! [[ "$check_result" =~ ^[0-9]+$ ]]; then
            log "WARNING: Invalid exit code '$check_result', defaulting to 1"
            check_result=1
        fi

        log "Check completed with result code: $check_result"
        log "Exit code validation: $([ $check_result -eq 0 ] && echo 'PASS' || echo 'FAIL')"
        log "Check output preview (first 200 chars):"
        echo "$check_output" | head -c 200 | while read line; do log "  $line"; done

        # Additional debugging for exit code accuracy
        log "Exit code details:"
        log "  Raw result: '$check_result'"
        log "  Numeric check: $([ "$check_result" -eq "$check_result" ] 2>/dev/null && echo 'OK' || echo 'INVALID')"
        log "  Final status: $([ $check_result -eq 0 ] && echo 'SUCCESS' || echo 'FAILURE')"

        # Store result in ConfigMap - only the actual check output
        store_result "$check_result" "$check_output"

        # Store results in files
        if [ $check_result -eq 0 ]; then
            echo "PASS" > /tmp/results/status
            log "Check passed"
        else
            echo "FAIL" > /tmp/results/status
            log "Check failed"
        fi

        echo "$check_output" > /tmp/results/output.log
        echo "$check_result" > /tmp/results/exit_code

        log "Results stored in /tmp/results/"
        log "Files created:"
        ls -la /tmp/results/ | while read line; do log "  $line"; done

        # Always exit successfully - the actual result is in the configmap
        log "Unified scanner completed successfully"
        exit 0
    else
        log "No check script provided and not in aggregated mode"
        echo "ERROR" > /tmp/results/status
        echo "1" > /tmp/results/exit_code
        store_result 1 "No check script provided"
        exit 0  # Exit successfully even if no script provided
    fi
}

main "$@" 