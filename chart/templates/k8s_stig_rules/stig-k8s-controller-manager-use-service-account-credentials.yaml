apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-controller-manager-use-service-account-credentials
  namespace: compliance-system
spec:
  id: "V-242381"
  title: "The Kubernetes Controller Manager must create unique service accounts for each work payload."
  description: "The Kubernetes Controller Manager is a background process that embeds core control loops regulating cluster system state through the API Server. Every process executed in a pod has an associated service account. By default, service accounts use the same credentials for authentication. Implementing the default settings poses a High risk to the Kubernetes Controller Manager. Setting the \"--use-service-account-credential\" value lowers the attack surface by generating unique service accounts settings for each controller instance."
  checkText: "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i use-service-account-credentials * \n\nIf the setting \"--use-service-account-credentials\" is not configured in the Kubernetes Controller Manager manifest file or it is set to \"false\", this is a finding."
  fixText: "Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.\n\nSet the value of \"--use-service-account-credentials\" to \"true\"."
  severity: "high"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting Controller Manager service account credentials check (V-242381)"
    
    # Define Controller Manager manifest file path (preserving original logic)
    manifest_path="/host/etc/kubernetes/manifests/kube-controller-manager.yaml"
    
    echo "Checking Controller Manager manifest file: $manifest_path"

    # Check if manifest file exists (preserving original logic)
    if [[ ! -f "$manifest_path" ]]; then
        echo "FAIL: Controller Manager manifest missing"
        echo "Expected to find kube-controller-manager.yaml manifest file"
        exit 1
    fi
    
    echo "Found Controller Manager manifest file"
    echo "Searching for --use-service-account-credentials configuration..."

    # Check for use-service-account-credentials parameter (preserving original logic)
    if ! grep -q -- "--use-service-account-credentials" "$manifest_path" 2>/dev/null; then
        echo "FAIL: Missing service account credentials parameter"
        echo "Controller Manager must have --use-service-account-credentials configured"
        echo "This parameter is required for unique service account generation"
        exit 1
    fi
    
    echo "Found --use-service-account-credentials parameter"
    echo "Validating parameter value..."

    # Check if parameter is set to false (preserving original logic)
    if grep -q -- "--use-service-account-credentials=false" "$manifest_path" 2>/dev/null; then
        echo "FAIL: Service account credentials parameter set to false"
        echo "Controller Manager --use-service-account-credentials must be set to true"
        echo "Setting to false uses shared credentials which poses security risk"
        exit 1
    fi

    # If we got here, the check passed (preserving original logic)
    echo "Parameter is correctly configured (not set to false)"
    echo "PASS: Service account credentials properly configured"
    exit 0 