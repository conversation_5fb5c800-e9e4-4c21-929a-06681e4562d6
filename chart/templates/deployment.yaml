apiVersion: apps/v1
kind: Deployment
metadata:
  name: compliance-operator
  namespace: {{ .Values.global.complianceNamespace }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: compliance-operator
  template:
    metadata:
      labels:
        app: compliance-operator
        cpaas.io/product: Platform-Center
    spec:
      {{- if .Values.global.registry.imagePullSecrets }}
      imagePullSecrets:
        {{- range $secret := .Values.global.registry.imagePullSecrets }}
        - name: {{ $secret }}
        {{- end }}
      {{- end }}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - compliance-operator
              topologyKey: kubernetes.io/hostname
            weight: 1
      serviceAccountName: compliance-operator
      containers: 
      - name: compliance-operator
        env:
          - name: COMPLIANCE_NAMESPACE
            value: {{ .Values.global.complianceNamespace }}
          - name: IMAGE_REGISTRY
            value: {{ .Values.global.registry.address }}
          - name: UNIFIED_SCANNER_IMAGE
            value: {{ .Values.global.registry.address }}/{{ .Values.global.images.scanner.repository }}:{{ .Values.global.images.scanner.tag }}
          - name: OPENSCAP_SCANNER_IMAGE
            value: {{ .Values.global.registry.address }}/{{ .Values.global.images.openscapScanner.repository }}:{{ .Values.global.images.openscapScanner.tag }}
          - name: CONTENT_IMAGE
            value: {{ .Values.global.registry.address }}/{{ .Values.global.images.content.repository }}:{{ .Values.global.images.content.tag }}
          - name: REPORT_SERVICE_IMAGE
            value: {{ .Values.global.registry.address }}/{{ .Values.global.images.report.repository }}:{{ .Values.global.images.report.tag }}
          - name: SCANNER_IMAGE
            value: {{ .Values.global.registry.address }}/{{ .Values.global.images.scanner.repository }}:{{ .Values.global.images.scanner.tag }}
          - name: LOG_LEVEL
            value: info
          - name: LOG_DEVELOPMENT
            value: "false"
        image: "{{ .Values.global.registry.address }}/{{ .Values.global.images.operator.repository }}:{{ .Values.global.images.operator.tag }}"
        args:
          - --leader-elect
          - --metrics-bind-address=0.0.0.0:8080
          - --health-probe-bind-address=0.0.0.0:8081
          {{- if .Values.reportStorage.storageClass }}
          - --report-storage-class={{ .Values.reportStorage.storageClass }}
          - --report-storage-size={{ .Values.reportStorage.size }}
          {{- end }}
        resources:
          limits:
            cpu: "2"
            memory: "2Gi"
          requests:
            cpu: "500m"
            memory: "512Mi"
        ports:
          - name: metrics
            containerPort: 8080
            protocol: TCP
          # - name: webhook-server
          #   containerPort: 9443
          #   protocol: TCP
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
      tolerations:
      - effect: NoSchedule
        key: node-role.kubernetes.io/master
        operator: Exists
      - effect: NoSchedule
        key: node-role.kubernetes.io/control-plane
        operator: Exists
      - effect: NoSchedule
        key: node-role.kubernetes.io/cpaas-system
        operator: Exists


