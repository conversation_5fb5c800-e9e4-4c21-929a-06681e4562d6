---
# Basic Test Profile for NodeScopeStrategy Testing
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: test-profile-basic
  namespace: compliance-system
  labels:
    compliance-operator.alauda.io/profile-type: "test"
spec:
  id: "TEST-BASIC-V1"
  title: "Basic Test Profile for NodeScopeStrategy"
  description: |
    This is a basic test profile designed to validate the NodeScopeStrategy functionality
    in the compliance-operator. It includes rules for different node scopes.
  version: "1.0"
  rules:
    - name: test-rule-control-plane-basic
    - name: test-rule-worker-basic
    - name: test-rule-all-nodes-basic
    - name: test-rule-platform-basic
