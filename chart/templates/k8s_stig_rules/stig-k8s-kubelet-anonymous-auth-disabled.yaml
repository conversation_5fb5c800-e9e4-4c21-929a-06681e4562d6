apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-anonymous-auth-disabled
  namespace: compliance-system
spec:
  id: "V-242391"
  title: "The Kubernetes Kubelet must have anonymous authentication disabled."
  description: "A user who has access to the Kubelet essentially has root access to the nodes contained within the Kubernetes Control Plane. To control access, users must be authenticated and authorized. By allowing anonymous connections, the controls put in place to secure the Kubelet can be bypassed.\n\nSetting anonymous authentication to \"false\" also disables unauthenticated requests from kubelets.\n\nWhile there are instances where anonymous connections may be needed (e.g., health checks) and Role-Based Access Controls (RBAC) are in place to limit the anonymous access, this access must be disabled and only enabled when necessary."
  checkText: "On each Control Plane and Worker Node, run the command:\nps -ef | grep kubelet\n\nIf the \"--anonymous-auth\" option exists, this is a finding. \n\nNote the path to the config file (identified by --config).\n\nInspect the content of the config file:\nLocate the \"anonymous\" section under \"authentication\".  In this section, if the field \"enabled\" does not exist or is set to \"true\", this is a finding."
  fixText: "On each Control Plane and Worker Node, run the command:\nps -ef | grep kubelet\n\nRemove the \"anonymous-auth\" option if present.\n\nNote the path to the config file (identified by --config).\n\nEdit the config file: \nLocate the \"authentication\" section and the \"anonymous\" subsection. Within the \"anonymous\" subsection, set \"enabled\" to \"false\".\n\nRestart the kubelet service using the following command:\nsystemctl daemon-reload && systemctl restart kubelet"
  severity: "high"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting kubelet anonymous authentication disabled check (V-242391)"
    echo ""
    
    # Environment debugging
    echo "=== Environment Information ==="
    echo "Current user: $(whoami)"
    echo "Current working directory: $(pwd)"
    echo "Available tools:"
    echo "  ps: $(chroot /host which ps 2>/dev/null || echo 'NOT FOUND')"
    echo "  grep: $(which grep 2>/dev/null || echo 'NOT FOUND')"
    echo "  yq: $(which yq 2>/dev/null || echo 'NOT FOUND')"
    echo ""
    
    # Find kubelet process
    echo "=== Searching for kubelet process ==="
    kubelet_cmd=$(chroot /host ps -ef 2>/dev/null | grep kubelet | grep -v grep)
    
    if [[ -z "$kubelet_cmd" ]]; then
        echo "FAIL: Kubelet process not found"
        exit 1
    fi
    
    echo "SUCCESS: Found kubelet process"
    echo "$kubelet_cmd"
    echo ""
    
    # Extract config file path
    echo "=== Extracting kubelet config file ==="
    config_file=$(echo "$kubelet_cmd" | grep -oP -- '--config=\S+' | cut -d= -f2)
    
    if [[ -z "$config_file" ]]; then
        echo "FAIL: Kubelet config file path not found"
        exit 1
    fi
    
    echo "Found kubelet config file path: $config_file"
    
    # Check config file accessibility
    echo "=== Checking config file accessibility ==="
    actual_config_file=""
    if [[ -f "$config_file" ]]; then
        actual_config_file="$config_file"
    elif [[ -f "/host$config_file" ]]; then
        actual_config_file="/host$config_file"
    else
        echo "FAIL: Kubelet config file not found"
        echo "Checked paths:"
        echo "  - $config_file"
        echo "  - /host$config_file"
        exit 1
    fi
    
    echo "Using config file: $actual_config_file"
    echo ""
    
    # Check authentication settings
    echo "=== Checking authentication settings ==="
    
    # Try yq first, then fallback to grep-based parsing
    anonymous_enabled=""
    if command -v yq >/dev/null 2>&1; then
        echo "Using yq to parse config file..."
        anonymous_enabled=$(yq '.authentication.anonymous.enabled' "$actual_config_file" 2>/dev/null)
        echo "yq result: '$anonymous_enabled'"
    else
        echo "yq not available, using grep-based parsing..."
        # Parse YAML using grep
        auth_section=$(grep -A 20 "authentication:" "$actual_config_file" 2>/dev/null)
        if [[ -n "$auth_section" ]]; then
            echo "Found authentication section"
            anonymous_section=$(echo "$auth_section" | grep -A 5 "anonymous:" 2>/dev/null)
            if [[ -n "$anonymous_section" ]]; then
                echo "Found anonymous section:"
                echo "$anonymous_section"
                # Extract enabled value
                enabled_line=$(echo "$anonymous_section" | grep -E "^\s*enabled\s*:" | head -1)
                if [[ -n "$enabled_line" ]]; then
                    anonymous_enabled=$(echo "$enabled_line" | sed -E 's/^\s*enabled\s*:\s*([^[:space:]]+).*/\1/' | tr -d '"'"'"'')
                    echo "Extracted enabled value: '$anonymous_enabled'"
                fi
            fi
        fi
    fi
    
    echo "Anonymous authentication setting: '$anonymous_enabled'"
    echo ""
    
    # Final evaluation
    echo "=== Final evaluation ==="
    if [[ "$anonymous_enabled" == "false" ]]; then
        echo "PASS: Anonymous authentication properly disabled"
        exit 0
    else
        echo "FAIL: Anonymous authentication not properly disabled"
        echo "Current setting: ${anonymous_enabled:-undefined}"
        exit 1
    fi