---
# Test Scan 1: Auto Strategy - Automatic node scope detection and job creation
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: test-scan-auto-strategy
  namespace: compliance-system
  labels:
    compliance-operator.alauda.io/scan-type: "test"
spec:
  profile: test-profile-basic
  scanType: "node"  # Type of scan to perform
  nodeSelector: {}  # Select all nodes

  # NodeScopeStrategy Configuration
  nodeScopeStrategy: "auto"  # Automatically create jobs based on rule nodeScope
  targetNodeRoles:           # Optional: limit to specific node roles
    - "control-plane"
    - "worker"

---
# Test Scan 2: Manual Strategy - Traditional behavior with nodeSelector
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: test-scan-manual-strategy
  namespace: compliance-system
  labels:
    compliance-operator.alauda.io/scan-type: "test"
spec:
  profile: test-profile-basic
  scanType: "node"
  nodeSelector:
    kubernetes.io/os: "linux"  # Use traditional nodeSelector

  # NodeScopeStrategy Configuration
  nodeScopeStrategy: "manual"  # Use nodeSelector, ignore rule nodeScope

---
# Test Scan 3: Strict Strategy - Validate rule compatibility before scanning
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: test-scan-strict-strategy
  namespace: compliance-system
  labels:
    compliance-operator.alauda.io/scan-type: "test"
spec:
  profile: test-profile-basic
  scanType: "node"
  nodeSelector: {}

  # NodeScopeStrategy Configuration
  nodeScopeStrategy: "strict"  # Validate rule nodeScope compatibility
  targetNodeRoles:             # Must be compatible with rule nodeScope
    - "worker"                 # This should work with worker and all-scope rules

---
# Test Scan 4: Auto Strategy - Control Plane Only
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: test-scan-control-plane-only
  namespace: compliance-system
  labels:
    compliance-operator.alauda.io/scan-type: "test"
spec:
  profile: test-profile-basic
  scanType: "node"
  nodeSelector: {}

  # NodeScopeStrategy Configuration
  nodeScopeStrategy: "auto"
  targetNodeRoles:
    - "control-plane"  # Only scan control plane nodes

---
# Test Scan 5: Auto Strategy - Worker Nodes Only
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: test-scan-worker-only
  namespace: compliance-system
  labels:
    compliance-operator.alauda.io/scan-type: "test"
spec:
  profile: test-profile-basic
  scanType: "node"
  nodeSelector: {}

  # NodeScopeStrategy Configuration
  nodeScopeStrategy: "auto"
  targetNodeRoles:
    - "worker"  # Only scan worker nodes

---
# Test Scan 6: Strict Strategy - Incompatible Configuration (should fail)
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: test-scan-strict-incompatible
  namespace: compliance-system
  labels:
    compliance-operator.alauda.io/scan-type: "test"
spec:
  profile: test-profile-basic
  scanType: "node"
  nodeSelector: {}

  # NodeScopeStrategy Configuration
  nodeScopeStrategy: "strict"
  targetNodeRoles:
    - "control-plane"  # This should conflict with worker-scope rules

---
# Test Scan 7: Default Strategy (empty) - Should behave like manual
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: test-scan-default-strategy
  namespace: compliance-system
  labels:
    compliance-operator.alauda.io/scan-type: "test"
spec:
  profile: test-profile-basic
  scanType: "node"
  nodeSelector:
    kubernetes.io/os: "linux"

  # No nodeScopeStrategy specified - should default to manual behavior
