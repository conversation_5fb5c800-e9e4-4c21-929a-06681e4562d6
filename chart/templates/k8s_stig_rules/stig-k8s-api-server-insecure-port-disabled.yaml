apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-insecure-port-disabled
  namespace: compliance-system
spec:
  id: "V-242386"
  title: "The Kubernetes API server must have the insecure port flag disabled."
  description: "By default, the API server will listen on two ports. One port is the secure port and the other port is called the \"localhost port\". This port is also called the \"insecure port\", port 8080. Any requests to this port bypass authentication and authorization checks. If this port is left open, anyone who gains access to the host on which the Control Plane is running can bypass all authorization and authentication mechanisms put in place, and have full control over the entire cluster.\n\nClose the insecure port by setting the API server's \"--insecure-port\" flag to \"0\", ensuring that the \"--insecure-bind-address\" is not set."
  checkText: "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i insecure-port * \n\nIf the setting \"--insecure-port\" is not set to \"0\" or is not configured in the Kubernetes API server manifest file, this is a finding.\n\nNote: \"--insecure-port\" flag has been deprecated and can only be set to \"0\". **This flag  will be removed in v1.24.*"
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.\n\nSet the value of \"--insecure-port\" to \"0\"."
  severity: "high"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting STIG check: V-242386 - API Server Insecure Port Disabled"
    echo "Checking if insecure port is properly disabled..."
    
    manifest_file="/host/etc/kubernetes/manifests/kube-apiserver.yaml"
    
    if [[ ! -f "$manifest_file" ]]; then
        echo "FAIL: API Server manifest file not found: $manifest_file"
        exit 1
    fi
    
    echo "Found API Server manifest: $manifest_file"
    echo "Searching for insecure-port parameter in manifest..."
    
    # Check for insecure-port configuration
    insecure_port=$(grep -E -- "--insecure-port(=[^[:space:]]+| +[^[:space:]]+)" "$manifest_file" 2>/dev/null)
    
    if [[ -n "$insecure_port" ]]; then
        echo "Found insecure-port configuration: $insecure_port"
        
        # Extract port value
        port_value=$(echo "$insecure_port" | awk -F'[=[:space:]]+' '{print $2}' 2>/dev/null)
        echo "Extracted port value: $port_value"
        
        if [[ "$port_value" != "0" ]]; then
            echo "FAIL: Insecure port is enabled with value: $port_value"
            echo "Security Risk: API Server insecure port allows unauthenticated access"
            exit 1
        fi
        
        echo "Insecure port is correctly set to 0 (disabled)"
    else
        echo "No insecure-port parameter found in manifest"
        echo "Note: In Kubernetes 1.24+, the --insecure-port flag has been removed entirely"
        echo "This is the secure default behavior - insecure port is disabled by default"
    fi
    
    echo "PASS: API Server insecure port is properly disabled"
    echo "The API Server does not expose an insecure port that bypasses authentication"
    exit 0 