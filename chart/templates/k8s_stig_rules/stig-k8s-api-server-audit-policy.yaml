apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-audit-policy
  namespace: compliance-system
spec:
  id: "V-242403"
  title: "Kubernetes API Server must generate audit records."
  description: "Kubernetes API Server validates and configures pods and services for the API object. The REST operation provides frontend functionality to the cluster share state. Enabling audit logs provides a way to monitor and identify security risk events or misuse of information. Audit logs are necessary to provide evidence in the case the Kubernetes API Server is compromised requiring a Cyber Security Investigation."
  checkText: "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\ngrep -i audit-policy-file * \n\nIf the setting \"audit-policy-file\" is not set or is found in the Kubernetes API manifest file without valid content, this is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument \"--audit-policy-file\" to \"log file directory\"."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Checking API Server audit policy configuration..."
    manifests_dir="/host/etc/kubernetes/manifests"
    echo "Searching for API Server manifest files in: $manifests_dir"
    
    # Check if manifests directory exists
    if [[ ! -d "$manifests_dir" ]]; then
        echo "ERROR: Kubernetes manifests directory not found: $manifests_dir"
        echo "FAIL: Manifests directory missing"
        exit 1
    fi
    
    echo "Found manifests directory"
    
    # Find API Server manifest files
    api_files=$(grep -lis 'kube-apiserver' "$manifests_dir"/* 2>/dev/null)
    
    if [[ -z "$api_files" ]]; then
        echo "ERROR: No API Server manifest files found in $manifests_dir"
        echo "FAIL: No API Server manifest found"
        exit 1
    fi
    
    echo "Found API Server manifest files: $api_files"
    
    # Check each API Server manifest file for audit-policy-file
    all_configured=true
    missing_files=""
    
    for file in $api_files; do
        echo "Checking file: $(basename "$file")"
        if ! grep -iq -- "--audit-policy-file" "$file" 2>/dev/null; then
            echo "VIOLATION: audit-policy-file not configured in $(basename "$file")"
            all_configured=false
            if [[ -n "$missing_files" ]]; then
                missing_files="${missing_files}, $(basename "$file")"
            else
                missing_files="$(basename "$file")"
            fi
        else
            echo "OK: audit-policy-file found in $(basename "$file")"
        fi
    done
    
    # Final result
    if [[ "$all_configured" == false ]]; then
        echo "FAIL: audit-policy-file not configured in: $missing_files"
        exit 1
    fi
    
    echo "OK: All API Server manifests have audit-policy-file configured"
    echo "PASS: Audit policy properly configured"
    exit 0 