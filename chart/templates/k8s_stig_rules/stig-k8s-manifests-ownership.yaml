apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-manifests-ownership
  namespace: compliance-system
spec:
  id: "V-242405"
  title: "The Kubernetes manifests must be owned by root."
  description: "Kubernetes manifest files must be owned by root to prevent unauthorized modifications."
  fixText: "On the Control Plane, change to the /etc/kubernetes/manifest directory. Run the command:\nchown root:root *\n\nTo verify the change took place, run the command:\nls -l *\n\nAll the manifest files should now be owned by root:root."
  checkText: "On the Control Plane, change to the /etc/kubernetes/manifest directory. Run the command:\nls -l *\n\nEach manifest file must be owned by root:root.\n\nIf any manifest file is not owned by root:root, this is a finding."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting V-242405: Kubernetes manifests ownership check"
    
    manifests_dir="/host/etc/kubernetes/manifests"
    failed_files=""
    has_failures=false
    
    echo "Checking manifest files in directory: $manifests_dir"
    
    if [[ ! -d "$manifests_dir" ]]; then
        echo "FAIL: Manifest directory not found: $manifests_dir"
        exit 1
    fi
    
    echo "Scanning manifest files for ownership compliance..."
    
    while IFS= read -r -d $'\0' file; do
        if [[ -n "$file" ]]; then
            owner=$(stat -c %U:%G "$file" 2>/dev/null)
            echo "Checking file: $file (owner: $owner)"
            
            if [[ "$owner" != "root:root" ]]; then
                echo "FAIL: Invalid ownership $owner for $file (should be root:root)"
                failed_files+="${failed_files:+, }$(basename "$file"):$owner"
                has_failures=true
            else
                echo "PASS: Correct ownership $owner for $file"
            fi
        fi
    done < <(find "$manifests_dir" -type f -print0 2>/dev/null)
    
    if [[ "$has_failures" == true ]]; then
        echo "FAIL: Some manifest files have incorrect ownership: $failed_files"
        exit 1
    else
        echo "PASS: All manifest files have correct ownership (root:root)"
        exit 0
    fi