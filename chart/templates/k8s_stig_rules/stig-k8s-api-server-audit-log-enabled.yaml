apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-audit-log-enabled
  namespace: compliance-system
spec:
  id: "V-242465"
  title: "The Kubernetes API Server audit log path must be set."
  description: "Kubernetes API Server validates and configures pods and services for the API object. The REST operation provides frontend functionality to the cluster share state. Audit logs are necessary to provide evidence in the case the Kubernetes API Server is compromised requiring Cyber Security Investigation. To record events in the audit log the log path value must be set."
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i audit-log-path * \n\nIf the setting audit-log-path is not set in the Kubernetes API Server manifest file or it is not set to a valid path, this is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of \"--audit-log-path\" to valid location."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Checking API Server audit log path configuration..."
    
    # Initialize variables
    result="PASSED"
    details=""
    manifest_file="/host/etc/kubernetes/manifests/kube-apiserver.yaml"

    echo "Checking API Server manifest: $manifest_file"

    # Check audit log path configuration
    if [[ -f "$manifest_file" ]]; then
        echo "Found API Server manifest file"
        
        # Extract audit log path from manifest
        audit_path=$(grep -i "\-\-audit-log-path" "$manifest_file" 2>/dev/null | awk -F'=' '{print $2}')
        
        if [[ -z "$audit_path" || "$audit_path" == "" ]]; then
            result="FAILED"
            details="audit-log-path not configured"
            echo "VIOLATION: audit-log-path parameter not found or empty"
        else
            echo "Found audit-log-path configuration: $audit_path"
            
            # Check if audit log directory exists (add /host prefix for host filesystem)
            audit_dir=$(dirname "$audit_path")
            host_audit_dir="/host$audit_dir"
            if [[ ! -d "$host_audit_dir" ]]; then
                result="FAILED"
                details="audit log directory does not exist: $audit_dir"
                echo "VIOLATION: audit log directory does not exist: $audit_dir"
            else
                echo "OK: audit log directory exists: $audit_dir"
            fi
        fi
    else
        result="FAILED"
        details="API Server manifest missing"
        echo "FAIL: API Server manifest file not found: $manifest_file"
    fi

    if [[ "$result" == "PASSED" ]]; then
        details="audit-log-path properly configured"
        echo "PASS: $details"
        exit 0
    else
        echo "FAIL: $details"
        exit 1
    fi 