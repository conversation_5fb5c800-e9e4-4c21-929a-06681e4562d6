apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-etcd-pps
  namespace: compliance-system
spec:
  id: "V-242413"
  title: "The Kubernetes etcd must enforce ports, protocols, and services (PPS) that adhere to the Ports, Protocols, and Services Management Category Assurance List (PPSM CAL)."
  description: "Kubernetes etcd PPSs must be controlled and conform to the PPSM CAL. Those PPS that fall outside the PPSM CAL must be blocked. Instructions on the PPSM can be found in DoD Instruction 8551.01 Policy."
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep etcd.manifest -I -listen-client-urls *\ngrep etcd.manifest -I -listen-peer-urls *\n-edit manifest file:\nVIM <Manifest Name>\nReview livenessProbe:\nHttpGet:\nPort:\nReview ports:\n- containerPort:\nhostPort:\n- containerPort:\nhostPort:\n\nRun command: \nkubectl describe services --all-namespaces \nSearch labels for any etcd namespaces.\nPort:\n\nAny manifest and namespace PPS or services configuration not in compliance with PPSM CAL is a finding.\n\nReview the information systems documentation and interview the team, gain an understanding of the etcd architecture, and determine applicable PPS. If there are any PPS in the system documentation not in compliance with the CAL PPSM, this is a finding. Any PPS not set in the system documentation is a finding.\n\nReview findings against the most recent PPSM CAL:\nhttps://cyber.mil/ppsm/cal/\n\nVerify etcd network boundary with the PPS associated with the CAL Assurance Categories. Any PPS not in compliance with the CAL Assurance Category requirements is a finding."
  fixText: "Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of --listen-client-urls and --listen-peer-urls to ports that are in compliance with the PPSM CAL.\n\nAny other ports used by etcd must be in compliance with the PPSM CAL.\n\nReview the information systems documentation and interview the team, gain an understanding of the etcd architecture, and determine applicable PPS. Document all PPS in the system documentation. All PPS must be in compliance with the CAL PPSM."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting etcd PPSM CAL compliance check (V-242413)"
    echo "This check requires manual verification of ports, protocols, and services"
    
    echo "MANUAL CHECK REQUIRED: etcd PPSM CAL compliance"
    echo ""
    echo "Manual verification steps required:"
    echo "1. Review etcd manifest files for listen-client-urls and listen-peer-urls"
    echo "2. Check all etcd-related ports and services"
    echo "3. Verify compliance with PPSM CAL requirements"
    echo "4. Review system documentation for all PPS configurations"
    echo ""
    echo "Reference: https://cyber.mil/ppsm/cal/"
    echo "Details: https://stigviewer.com/stigs/kubernetes/2024-08-22/finding/V-242413"
    echo ""
    echo "All etcd ports, protocols, and services must comply with PPSM CAL"
    
    exit 2 