---
# 快速测试示例 - 最小化配置用于验证 NodeScopeStrategy 功能
# 这个文件包含了最基本的测试配置，可以快速验证功能是否正常

# 1. 简单的测试 ProfileBundle
# apiVersion: compliance-operator.alauda.io/v1alpha1
# kind: ProfileBundle
# metadata:
#   name: quick-test-content
#   namespace: compliance-system
# spec:
#   contentFile: "quick-test-datastream.xml"
#   contentImage: "registry.alauda.cn/ait/test-content:latest"

---
# 2. 简单的测试 Profile
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: quick-test-profile
  namespace: compliance-system
spec:
  id: "QUICK-TEST-V1"
  title: "Quick Test Profile"
  description: "Minimal profile for quick NodeScopeStrategy testing"
  version: "1.0"
  rules:
    - name: quick-test-rule-cp
    - name: quick-test-rule-worker
    - name: stig-k8s-admin-conf-file-permissions
    - name: stig-k8s-api-server-alpha-apis-disabled
    - name: stig-k8s-api-server-anonymous-auth-disabled
    - name: stig-k8s-api-server-audit-log-enabled
    - name: stig-k8s-api-server-audit-log-maxage
    - name: stig-k8s-api-server-audit-log-maxbackup
    - name: stig-k8s-api-server-audit-log-maxsize
    - name: stig-k8s-api-server-audit-log-path
    - name: stig-k8s-api-server-audit-policy-configured
    - name: stig-k8s-api-server-audit-policy
    - name: stig-k8s-api-server-authorization-mode
    - name: stig-k8s-api-server-basic-auth-disabled
    - name: stig-k8s-api-server-cipher-suites
    - name: stig-k8s-api-server-client-ca-file
    - name: stig-k8s-api-server-etcd-cafile
    - name: stig-k8s-api-server-etcd-certfile
    - name: stig-k8s-api-server-etcd-keyfile
    - name: stig-k8s-api-server-insecure-bind-address-not-set
    - name: stig-k8s-api-server-insecure-port-disabled
    - name: stig-k8s-api-server-pps
    - name: stig-k8s-api-server-request-timeout
    - name: stig-k8s-api-server-secure-port-set
    - name: stig-k8s-api-server-tls-cert-file
    - name: stig-k8s-api-server-tls-min-version
    - name: stig-k8s-api-server-token-auth-disabled
    - name: stig-k8s-api-server-validating-webhook
    - name: stig-k8s-control-plane-conf-file-ownership
    - name: stig-k8s-controller-manager-pps
    - name: stig-k8s-controller-manager-profiling-disabled
    - name: stig-k8s-controller-manager-root-ca-file
    - name: stig-k8s-controller-manager-secure-binding
    - name: stig-k8s-controller-manager-tls-min-version
    - name: stig-k8s-controller-manager-use-service-account-credentials
    - name: stig-k8s-dashboard-disabled
    - name: stig-k8s-dynamic-auditing-disabled
    - name: stig-k8s-endpoints-certificates
    - name: stig-k8s-etcd-auto-tls-disabled
    - name: stig-k8s-etcd-cert-file
    - name: stig-k8s-etcd-client-cert-auth
    - name: stig-k8s-etcd-data-directory-ownership
    - name: stig-k8s-etcd-file-permissions
    - name: stig-k8s-etcd-key-file
    - name: stig-k8s-etcd-peer-auto-tls-disabled
    - name: stig-k8s-etcd-peer-cert-file
    - name: stig-k8s-etcd-peer-client-cert-auth
    - name: stig-k8s-etcd-peer-key-file
    - name: stig-k8s-etcd-pps
    - name: stig-k8s-kube-proxy-conf-file-ownership
    - name: stig-k8s-kube-proxy-conf-file-permissions
    - name: stig-k8s-kubeadm-conf-ownership
    - name: stig-k8s-kubectl-version
    - name: stig-k8s-kubelet-anonymous-auth-disabled
    - name: stig-k8s-kubelet-authorization-mode
    - name: stig-k8s-kubelet-ca-file-permissions
    - name: stig-k8s-kubelet-ca-ownership
    - name: stig-k8s-kubelet-client-ca-file
    - name: stig-k8s-kubelet-conf-file-ownership
    - name: stig-k8s-kubelet-conf-file-permissions
    - name: stig-k8s-kubelet-config-file-ownership
    - name: stig-k8s-kubelet-config-file-permissions
    - name: stig-k8s-kubelet-config-ownership
    - name: stig-k8s-kubelet-config-permissions
    - name: stig-k8s-kubelet-dynamic-config-disabled
    - name: stig-k8s-kubelet-hostname-override-disabled
    - name: stig-k8s-kubelet-protect-kernel-defaults
    - name: stig-k8s-kubelet-readonly-port-disabled
    - name: stig-k8s-kubelet-static-pod-path-disabled
    - name: stig-k8s-kubelet-streaming-timeout
    - name: stig-k8s-kubelet-tls-cert-file
    - name: stig-k8s-kubelet-tls-private-key-file
    - name: stig-k8s-manifest-file-permissions
    - name: stig-k8s-manifest-permissions
    - name: stig-k8s-manifests-owned-by-root
    - name: stig-k8s-manifests-ownership
    - name: stig-k8s-non-privileged-ports
    - name: stig-k8s-old-components-removed
    - name: stig-k8s-pki-certificate-file-permissions
    - name: stig-k8s-pki-directory-ownership
    - name: stig-k8s-pki-key-file-permissions
    - name: stig-k8s-pod-security-admission-control-file
    - name: stig-k8s-pod-security-admission-enabled
    - name: stig-k8s-pod-security-policy
    - name: stig-k8s-scheduler-pps
    - name: stig-k8s-scheduler-secure-binding
    - name: stig-k8s-scheduler-tls-min-version
    - name: stig-k8s-secrets-not-environment-variables
    - name: stig-k8s-ssh-service-disabled
    - name: stig-k8s-ssh-service-stopped
    - name: stig-k8s-user-functionality-separation
    - name: stig-k8s-user-resources-dedicated-namespaces
    - name: stig-k8s-version-up-to-date




---
# 3. Control Plane 测试规则
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: quick-test-rule-cp
  namespace: compliance-system
spec:
  id: "QUICK-CP-001"
  title: "Quick Control Plane Test"
  description: "Quick test rule for control plane nodes"
  checkText: "Verify control plane node is properly configured for quick testing."
  fixText: "Apply basic control plane configurations for testing purposes."
  severity: "low"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    echo "=== Quick Control Plane Test ==="
    echo "Node: $(hostname)"
    echo "PASS: Quick control plane test completed"
    exit 0

---
# 4. Worker 测试规则
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: quick-test-rule-worker
  namespace: compliance-system
spec:
  id: "QUICK-WK-001"
  title: "Quick Worker Test"
  description: "Quick test rule for worker nodes"
  checkText: "Verify worker node is properly configured for quick testing."
  fixText: "Apply basic worker node configurations for testing purposes."
  severity: "low"
  checkType: "node"
  nodeScope: "worker"
  checkScript: |
    #!/bin/bash
    echo "=== Quick Worker Test ==="
    echo "Node: $(hostname)"
    echo "PASS: Quick worker test completed"
    exit 0

---
# 5. Manual Strategy 快速测试 - 修复后应该只在 control-plane 节点运行
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: quick-test-manual-fixed
  namespace: compliance-system
spec:
  profile: quick-test-profile
  scanType: "node"
  nodeSelector: {}

  # 使用 manual 策略测试，现在应该支持 targetNodeRoles
  nodeScopeStrategy: "auto"
  targetNodeRoles:
    - "control-plane"
    - "worker"

---
# stig-os-micoos
# 6. Auto Strategy 测试 - 应该根据规则的 nodeScope 自动筛选
# apiVersion: compliance-operator.alauda.io/v1alpha1
# kind: Scan
# metadata:
#   name: quick-test-auto-fixed
#   namespace: compliance-system
# spec:
#   profile: quick-test-profile
#   scanType: "node"
#   nodeSelector: {}

#   # 使用 auto 策略测试
#   nodeScopeStrategy: "auto"
#   targetNodeRoles:
#     - "control-plane"
#     # - "worker"
