apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kube-proxy-conf-file-permissions
  namespace: compliance-system
spec:
  id: "V-242447"
  title: "Kubernetes Kube-Proxy kubeconfig permissions must ≤644"
  description: "The Kubernetes Kube Proxy kubeconfig contain the argument and setting for the Control Planes. These settings contain network rules for restricting network communication between pods, clusters, and networks. If these files can be changed, data traversing between the Kubernetes Control Panel components would be compromised. Many of the security settings within the document are implemented through this file."
  checkText: "Check if Kube-Proxy is running use the following command:\nps -ef | grep kube-proxy\n\nIf Kube-Proxy exists:\nReview the permissions of the Kubernetes Kube Proxy by using the command:\nstat -c %a <location from --kubeconfig>\n\nIf the file has permissions more permissive than \"644\", this is a finding."
  fixText: "Change the permissions of the Kube Proxy to 644 by executing the command:\n\nchmod 644 <location from kubeconfig>."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash

    echo "Starting kube-proxy kubeconfig permissions check (V-242447)"
    
    # Define kube-proxy configuration path (preserving original logic)
    kube_proxy_conf="/var/lib/kube-proxy/..data/config.conf"

    echo "Checking for kube-proxy pod in kube-system namespace..."
    
    # Find kube-proxy pod (preserving original logic)
    pod_name=$(kubectl get pod -n kube-system | grep kube-proxy | awk '{print $1}' | head -1 2>/dev/null)

    if [[ -z "$pod_name" ]]; then
        echo "FAIL: No kube-proxy pod found in kube-system namespace"
        echo "Expected to find kube-proxy pod for configuration check"
        exit 1
    fi

    echo "Found kube-proxy pod: $pod_name"
    echo "Checking kube-proxy configuration file: $kube_proxy_conf"

    # Check if kube-proxy config file exists (preserving original logic)
    if kubectl exec -it -n kube-system $pod_name -- ls /var/lib/kube-proxy/..data/config.conf 2>/dev/null >/dev/null; then
        echo "Found kube-proxy configuration file"
        echo "Checking file permissions..."
        echo "Required: permissions must be 644 or more restrictive"
        
        # Get file permissions (preserving original logic)
        perm=$(kubectl exec -it -n kube-system $pod_name -- stat -c '%a' /var/lib/kube-proxy/..data/config.conf 2>/dev/null | tr -d '\r')
        
        echo "Current permissions: $perm"
        echo "Maximum allowed: 644"
        
        # Check if permissions are more permissive than 644 (preserving original logic)
        if [[ $perm -gt 644 ]]; then
            echo "FAIL: Excessive permissions for kube-proxy config"
            echo "File: $kube_proxy_conf"
            echo "Current: $perm"
            echo "Maximum allowed: 644"
            echo "Kube-proxy config must have restrictive permissions for security"
            exit 1
        else
            echo "PASS: Kube-proxy config has compliant permissions"
            echo "File permissions are 644 or more restrictive"
            exit 0
        fi
    else
        echo "FAIL: Kube-proxy config missing"
        echo "Expected to find configuration file at $kube_proxy_conf"
        exit 1
    fi 