apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-etcd-keyfile
  namespace: compliance-system
spec:
  id: "V-242431"
  title: "Kubernetes etcd must have a key file for secure communication."
  description: "Kubernetes stores configuration and state information in a distributed key-value store called etcd. Anyone who can write to etcd can effectively control a Kubernetes cluster. Even just reading the contents of etcd could easily provide helpful hints to a would-be attacker. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server and etcd with a means to be able to authenticate sessions and encrypt traffic. \n\nTo enable encrypted communication for etcd, the parameter \"--etcd-keyfile\" must be set. This parameter gives the location of the key file used to secure etcd communication."
  checkText: "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\ngrep -i etcd-keyfile * \n\nIf the setting \"--etcd-keyfile\" is not configured in the Kubernetes API Server manifest file, this is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--etcd-keyfile\" to the certificate to be used for communication with etcd."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Checking API Server etcd key file configuration..."
    manifest="/host/etc/kubernetes/manifests/kube-apiserver.yaml"
    required_param="etcd-keyfile"
    
    echo "Checking API Server manifest: $manifest"
    
    # Check if manifest file exists
    if [[ ! -f "$manifest" ]]; then
        echo "ERROR: API Server manifest file not found: $manifest"
        exit 1
    fi
    
    echo "Searching for $required_param parameter in manifest..."
    
    # Check if etcd-keyfile parameter is configured
    if ! grep -q -- "--$required_param" "$manifest" 2>/dev/null; then
        echo "FAIL: Missing --$required_param configuration in API Server manifest"
        exit 1
    fi
    
    # Extract key file path
    key_path=$(grep -oP -- "--$required_param=\K[^ ]+" "$manifest" 2>/dev/null)
    
    if [[ -z "$key_path" ]]; then
        echo "FAIL: Unable to extract key file path from --$required_param parameter"
        exit 1
    fi
    
    echo "Found etcd key file path: $key_path"
    
    # Check if key file exists (add /host prefix for container environment)
    if [[ "$key_path" =~ ^/host ]]; then
        host_key_path="$key_path"
    else
        host_key_path="/host$key_path"
    fi
    
    echo "Checking if key file exists: $host_key_path"
    
    if [[ ! -f "$host_key_path" ]]; then
        echo "FAIL: etcd key file not found: $key_path"
        exit 1
    fi
    
    echo "PASS: API Server etcd key file is properly configured and exists"
    echo "Key file: $key_path"
    exit 0 