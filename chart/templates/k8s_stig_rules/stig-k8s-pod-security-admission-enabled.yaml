apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-pod-security-admission-enabled
  namespace: compliance-system
spec:
  id: "V-254801"
  title: "Kubernetes must enable PodSecurity admission controller on static pods and Kubelets."
  description: "Kubernetes must enable PodSecurity admission controller to enforce security policies."
  fixText: "On the Control Plane, change to the manifests' directory at /etc/kubernetes/manifests and run the command:\ngrep -i feature-gates *\n\nFor each manifest file, if the \"--feature-gates\" setting does not exist, does not contain the \"--PodSecurity\" flag, or sets the flag to \"false\", this is a finding.\n\nOn each Control Plane and Worker Node, run the command:\nps -ef | grep kubelet\n\nIf the \"--feature-gates\" option exists, this is a finding. \n\nNote the path to the config file (identified by --config).\n\nInspect the content of the config file:\nIf the \"featureGates\" setting is not present, does not contain the \"PodSecurity\" flag, or sets the flag to \"false\", this is a finding."
  checkText: "On the Control Plane, change to the manifests' directory at /etc/kubernetes/manifests and run the command:\ngrep -i feature-gates *\n\nFor each manifest file, if the \"--feature-gates\" setting does not exist, does not contain the \"--PodSecurity\" flag, or sets the flag to \"false\", this is a finding.\n\nOn each Control Plane and Worker Node, run the command:\nps -ef | grep kubelet\n\nIf the \"--feature-gates\" option exists, this is a finding. \n\nNote the path to the config file (identified by --config).\n\nInspect the content of the config file:\nIf the \"featureGates\" setting is not present, does not contain the \"PodSecurity\" flag, or sets the flag to \"false\", this is a finding."
  severity: "high"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting V-254801: Kubernetes PodSecurity admission controller check"
    
    manifests_dir="/host/etc/kubernetes/manifests"
    kubelet_config="/host/var/lib/kubelet/config.yaml"
    failed=0
    failed_components=""
    
    echo "Checking PodSecurity feature gates in control plane components and kubelet..."
    
    # Control Plane Components Check
    echo "Checking control plane manifests in: $manifests_dir"
    
    if [[ -d "$manifests_dir" ]]; then
        echo "Manifests directory found, scanning YAML files..."
        
        while IFS= read -r -d '' manifest; do
            if [[ -n "$manifest" ]]; then
                echo "Checking manifest: $manifest"
                
                fg_line=$(grep -iE -- "--feature-gates(=[^[:space:]]+| +[^[:space:]]+)" "$manifest" 2>/dev/null)
                
                if [[ -z "$fg_line" ]]; then
                    echo "FAIL: Control plane component missing --feature-gates parameter: $manifest"
                    failed_components+="${failed_components:+, }$(basename "$manifest"):missing-feature-gates"
                    failed=1
                else
                    echo "Found feature gates: $fg_line"
                    
                    podsecurity_enabled=$(echo "$fg_line" | chroot /host awk -F'[=,]' '{
                        for(i=1; i<=NF; i++) {
                            if($i ~ /PodSecurity/) {
                                split($i, arr, "=")
                                print (arr[2] == "true") ? 1 : 0
                                exit
                            }
                        }
                        print 0
                    }')
                    
                    if [[ "$podsecurity_enabled" -eq 0 ]]; then
                        echo "FAIL: PodSecurity disabled or not set to 'true': $manifest"
                        failed_components+="${failed_components:+, }$(basename "$manifest"):podsecurity-disabled"
                        failed=1
                    else
                        echo "PASS: PodSecurity feature gate enabled in: $manifest"
                    fi
                fi
            fi
        done < <(find "$manifests_dir" -name "*.yaml" -print0 2>/dev/null)
    else
        echo "FAIL: Control plane manifests directory missing: $manifests_dir"
        exit 1
    fi
    
    # Kubelet Configuration Check
    echo "Checking kubelet configuration: $kubelet_config"
    
    if [[ -f "$kubelet_config" ]]; then
        echo "Kubelet config file found, checking PodSecurity feature gate..."
        
        podsecurity_config=$(yq '.featureGates.PodSecurity' "$kubelet_config" 2>/dev/null)
        echo "Kubelet PodSecurity setting: $podsecurity_config"
        
        if [[ "$podsecurity_config" != "true" ]]; then
            echo "FAIL: PodSecurity feature gate not enabled in kubelet config: $kubelet_config"
            failed_components+="${failed_components:+, }kubelet:podsecurity-disabled"
            failed=1
        else
            echo "PASS: PodSecurity feature gate enabled in kubelet config"
        fi
    else
        echo "FAIL: Kubelet config file missing: $kubelet_config"
        failed_components+="${failed_components:+, }kubelet:config-missing"
        failed=1
    fi
    
    # Result Aggregation
    if [[ $failed -gt 0 ]]; then
        echo "FAIL: Detected $failed violations in components: $failed_components"
        exit 1
    else
        echo "PASS: All components comply with PodSecurity feature gate requirements"
        exit 0
    fi