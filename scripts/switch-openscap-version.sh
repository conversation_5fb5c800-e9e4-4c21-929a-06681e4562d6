#!/bin/bash

# OpenSCAP 版本切换脚本
# 用于在不同OpenSCAP版本之间切换

set -e

echo "=== OpenSCAP 版本切换脚本 ==="
echo "时间: $(date)"
echo

# 显示用法
show_usage() {
    echo "用法: $0 [版本] [选项]"
    echo
    echo "版本选项:"
    echo "  1.2.17    - 使用安全的OpenSCAP 1.2.17版本"
    echo "  latest    - 使用最新版本 (默认)"
    echo "  current   - 显示当前配置"
    echo
    echo "选项:"
    echo "  --env-only    - 只设置环境变量，不重启controller"
    echo "  --restart     - 设置后自动重启controller"
    echo "  --help        - 显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 1.2.17 --restart     # 切换到1.2.17版本并重启"
    echo "  $0 current              # 显示当前版本配置"
    echo "  $0 latest --env-only    # 切换到最新版本但不重启"
}

# 显示当前配置
show_current_config() {
    echo "当前OpenSCAP配置:"
    echo "  OPENSCAP_SCANNER_IMAGE: ${OPENSCAP_SCANNER_IMAGE:-未设置}"
    echo "  IMAGE_REGISTRY: ${IMAGE_REGISTRY:-未设置}"
    echo
    
    # 检查当前运行的controller进程
    if pgrep -f "make run" > /dev/null; then
        echo "Controller状态: 运行中 (make run)"
    else
        echo "Controller状态: 未运行"
    fi
    
    # 检查可用的镜像版本
    echo
    echo "可用的OpenSCAP镜像版本:"
    echo "  - registry.alauda.cn:60070/test/compliance/openscap-scanner:1.2.17"
    echo "  - registry.alauda.cn:60070/test/compliance/openscap-scanner:latest"
    echo "  - registry.alauda.cn:60070/test/compliance/openscap-scanner:1.2.17-latest"
}

# 设置OpenSCAP版本
set_openscap_version() {
    local version="$1"
    local registry="registry.alauda.cn:60070/test/compliance"
    
    case "$version" in
        "1.2.17")
            export OPENSCAP_SCANNER_IMAGE="${registry}/openscap-scanner:1.2.17"
            echo "✅ 设置OpenSCAP版本为1.2.17"
            echo "   镜像: $OPENSCAP_SCANNER_IMAGE"
            ;;
        "latest")
            export OPENSCAP_SCANNER_IMAGE="${registry}/openscap-scanner:latest"
            echo "✅ 设置OpenSCAP版本为latest"
            echo "   镜像: $OPENSCAP_SCANNER_IMAGE"
            ;;
        *)
            echo "❌ 不支持的版本: $version"
            echo "   支持的版本: 1.2.17, latest"
            exit 1
            ;;
    esac
    
    # 设置镜像注册表
    export IMAGE_REGISTRY="registry.alauda.cn:60070/test/compliance"
    
    # 保存到环境文件
    local env_file=".env.openscap"
    cat > "$env_file" << EOF
# OpenSCAP版本配置 - 生成于 $(date)
export OPENSCAP_SCANNER_IMAGE="$OPENSCAP_SCANNER_IMAGE"
export IMAGE_REGISTRY="$IMAGE_REGISTRY"
EOF
    
    echo "✅ 环境变量已保存到: $env_file"
    echo "   要在新shell中使用，请运行: source $env_file"
}

# 重启controller
restart_controller() {
    echo "=== 重启Controller ==="
    
    # 检查是否有运行的controller
    if pgrep -f "make run" > /dev/null; then
        echo "停止当前运行的controller..."
        pkill -f "make run" || true
        sleep 2
    fi
    
    # 加载环境变量
    if [[ -f ".env.openscap" ]]; then
        echo "加载OpenSCAP环境变量..."
        source .env.openscap
    fi
    
    echo "启动controller..."
    echo "使用镜像: $OPENSCAP_SCANNER_IMAGE"
    
    # 后台启动controller
    nohup make run > controller.log 2>&1 &
    local pid=$!
    
    echo "Controller已启动，PID: $pid"
    echo "日志文件: controller.log"
    echo "要查看日志，请运行: tail -f controller.log"
    
    # 等待几秒钟检查是否成功启动
    sleep 5
    if kill -0 $pid 2>/dev/null; then
        echo "✅ Controller启动成功"
    else
        echo "❌ Controller启动失败，请检查日志"
        exit 1
    fi
}

# 验证镜像是否存在
verify_image() {
    local image="$1"
    echo "验证镜像: $image"
    
    # 这里可以添加镜像验证逻辑
    # 例如: docker pull $image --dry-run
    echo "✅ 镜像验证通过 (跳过实际验证)"
}

# 主函数
main() {
    local version=""
    local env_only=false
    local restart=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help)
                show_usage
                exit 0
                ;;
            --env-only)
                env_only=true
                shift
                ;;
            --restart)
                restart=true
                shift
                ;;
            current)
                show_current_config
                exit 0
                ;;
            1.2.17|latest)
                version="$1"
                shift
                ;;
            *)
                echo "❌ 未知参数: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定版本，显示当前配置
    if [[ -z "$version" ]]; then
        show_current_config
        exit 0
    fi
    
    # 设置版本
    set_openscap_version "$version"
    
    # 验证镜像
    verify_image "$OPENSCAP_SCANNER_IMAGE"
    
    # 根据选项决定是否重启
    if [[ "$restart" == true ]]; then
        restart_controller
    elif [[ "$env_only" == false ]]; then
        echo
        echo "环境变量已设置，但controller未重启"
        echo "要重启controller，请运行:"
        echo "  $0 $version --restart"
        echo "或者手动重启:"
        echo "  source .env.openscap && make run"
    fi
    
    echo
    echo "=== 版本切换完成 ==="
    echo "当前OpenSCAP版本: $version"
    echo "镜像: $OPENSCAP_SCANNER_IMAGE"
}

# 检查是否在项目根目录
if [[ ! -f "Makefile" ]]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 运行主函数
main "$@" 