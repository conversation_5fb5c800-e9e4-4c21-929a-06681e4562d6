apiVersion: v1
kind: Pod
metadata:
  name: scap
  namespace: compliance-system
spec:
  containers:
  - env:
    - name: PROFILE
      value: xccdf_org.ssgproject.content_profile_stig
    - name: CONTENT
      value: ssg-slmicro5-ds.xml
    - name: NODE_NAME
      value: worker-node-1
    - name: NODE_ROLE
      value: worker
    - name: SCAN_ID
      value: scan-stig-os-micoos-64af80fd-20250703-134553-3ve8
    - name: SCAN_NAME
      value: scan-stig-os-micoos
    - name: JOB_NAME
      value: scan-stig-os-micoos-6im5i
    - name: NAMESPACE
      value: compliance-system
    - name: HOSTROOT
      value: /host
    - name: REPORT_DIR
      value: /reports
    - name: CONTENT_IMAGE
      value: public-dbs-s4182.alaudatech.net:11443/ait/compliance-content:v0.0.0-fix.5.5.gfd08fb56-fix-crd
    - name: OPENSCAP_REPORT_SERVICE_URL
      value: http://openscap-report-service.compliance-system.svc.cluster.local:8080
    - name: NODE_SCOPE_STRATEGY
    image: public-dbs-s4182.alaudatech.net:11443/ait/compliance-openscap-scanner:v0.0.0-fix.5.5.gfd08fb56-fix-crd
    imagePullPolicy: Always
    name: openscap-scanner
    # 添加命令来延长Pod生命周期
    command: ["/bin/sh", "-c"]
    args: 
    - |
      echo "Starting OpenSCAP scanner..."
      # 这里执行原始的扫描逻辑
      /scripts/openscap-scanner.sh
      echo "Scan completed, sleeping for debugging..."
      sleep 3600
    resources: {}
    securityContext:
      allowPrivilegeEscalation: true
      capabilities:
        add:
        - SYS_ADMIN
        - DAC_OVERRIDE
        - FOWNER
        - SETUID
        - SETGID
      privileged: true
      readOnlyRootFilesystem: false
      runAsGroup: 0
      runAsUser: 0
    terminationMessagePath: /dev/termination-log
    terminationMessagePolicy: File
    volumeMounts:
    - mountPath: /shared-content
      name: shared-content
      readOnly: true
    - mountPath: /host
      name: host-root
      readOnly: true
    - mountPath: /reports
      name: reports
    - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
      name: kube-api-access-4v9vj
      readOnly: true
  dnsPolicy: ClusterFirst
  enableServiceLinks: true
  imagePullSecrets:
  - name: global-registry-auth
  initContainers:
  - args:
    - echo 'Extracting content files...' && cp -v /content/*.xml /shared-content/
      && ls -la /shared-content/ && echo 'Content extraction completed'
    command:
    - /bin/sh
    - -c
    image: public-dbs-s4182.alaudatech.net:11443/ait/compliance-content:v0.0.0-fix.5.5.gfd08fb56-fix-crd
    imagePullPolicy: Always
    name: content-extractor
    resources: {}
    securityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      readOnlyRootFilesystem: false
      runAsGroup: 0
      runAsUser: 0
    terminationMessagePath: /dev/termination-log
    terminationMessagePolicy: File
    volumeMounts:
    - mountPath: /shared-content
      name: shared-content
    - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
      name: kube-api-access-4v9vj
      readOnly: true
  preemptionPolicy: PreemptLowerPriority
  priority: 0
  restartPolicy: Never
  schedulerName: default-scheduler
  securityContext:
    fsGroup: 0
    runAsGroup: 0
    runAsUser: 0
  serviceAccount: compliance-scanner
  serviceAccountName: compliance-scanner
  terminationGracePeriodSeconds: 30
  tolerations:
  - effect: NoSchedule
    key: compliance-operator.alauda.io/scanner
    operator: Exists
  - effect: NoExecute
    key: node.kubernetes.io/not-ready
    operator: Exists
    tolerationSeconds: 300
  - effect: NoExecute
    key: node.kubernetes.io/unreachable
    operator: Exists
    tolerationSeconds: 300
  volumes:
  - emptyDir: {}
    name: shared-content
  - hostPath:
      path: /
      type: Directory
    name: host-root
  - emptyDir: {}
    name: reports
  - name: kube-api-access-4v9vj
    projected:
      defaultMode: 420
      sources:
      - serviceAccountToken:
          expirationSeconds: 3607
          path: token
      - configMap:
          items:
          - key: ca.crt
            path: ca.crt
          name: kube-root-ca.crt
      - downwardAPI:
          items:
          - fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
            path: namespace 