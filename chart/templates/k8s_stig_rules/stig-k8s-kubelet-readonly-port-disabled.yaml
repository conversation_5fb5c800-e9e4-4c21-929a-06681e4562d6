apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-readonly-port-disabled
  namespace: compliance-system
spec:
  id: "V-242387"
  title: "The Kubernetes Kubelet must have the \"readOnlyPort\" flag disabled."
  description: "Kubelet serves a small REST API with read access to port 10255. The read-only port for Kubernetes provides no authentication or authorization security control. Providing unrestricted access on port 10255 exposes Kubernetes pods and containers to malicious attacks or compromise. Port 10255 is deprecated and should be disabled."
  checkText: "On each Control Plane and Worker Node, run the command:\nps -ef | grep kubelet\n\nIf the \"--read-only-port\" option exists, this is a finding. \n\nNote the path to the config file (identified by --config).\n\nRun the command:\ngrep -i readOnlyPort <path_to_config_file>\n\nIf the setting \"readOnlyPort\" exists and is not set to \"0\", this is a finding."
  fixText: "On each Control Plane and Worker Node, run the command:\nps -ef | grep kubelet\n\nRemove the \"--read-only-port\" option if present.\n\nNote the path to the config file (identified by --config).\n\nEdit the config file: \nSet \"readOnlyPort\" to \"0\" or remove the setting.\n\nRestart the kubelet service using the following command:\nsystemctl daemon-reload && systemctl restart kubelet"
  severity: "high"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting kubelet readonly port disabled check (V-242387)"
    
    # Check kubelet process arguments (preserving original logic)
    echo "Checking kubelet process arguments..."
    kubelet_cmd=$(chroot /host ps -ef | grep kubelet | grep -v grep)
    
    if [[ -z "$kubelet_cmd" ]]; then
        echo "FAIL: Kubelet process not found"
        echo "Unable to locate running kubelet process"
        exit 1
    fi
    
    echo "Found kubelet process"
    
    # Check for --read-only-port in process arguments (preserving original logic)
    echo "Checking for --read-only-port option in process arguments..."
    if echo "$kubelet_cmd" | grep -q -- "--read-only-port"; then
        echo "FAIL: Readonly port configured via process arguments"
        echo "Found --read-only-port option in kubelet process"
        echo "This option should not be present as it exposes port 10255"
        echo "Port 10255 provides unrestricted access without authentication"
        exit 1
    fi
    
    echo "No --read-only-port found in process arguments"
    
    # Extract config file path from kubelet process (preserving original logic)
    echo "Extracting kubelet config file path..."
    extracted_config=$(echo "$kubelet_cmd" | grep -o -- '--config=[^ ]*' | cut -d= -f2 2>/dev/null)
    
    if [[ -z "$extracted_config" ]]; then
        config_file="/host/var/lib/kubelet/config.yaml"
        echo "Using default kubelet config location: $config_file"
    else
        # Ensure config file has /host prefix if extracted from process
        if [[ ! "$extracted_config" =~ ^/host ]]; then
            config_file="/host$extracted_config"
        else
            config_file="$extracted_config"
        fi
        echo "Found kubelet config from process: $config_file"
    fi
    
    echo "Checking kubelet configuration file: $config_file"
    
    # Check if config file exists (preserving original logic)
    if [[ ! -f "$config_file" ]]; then
        echo "FAIL: Kubelet config file missing"
        echo "Configuration file does not exist at: $config_file"
        exit 1
    fi
    
    echo "Found kubelet configuration file"
    echo "Checking readOnlyPort setting..."
    
    # Check if readOnlyPort exists in config file (preserving original logic)
    if grep 'readOnlyPort' "$config_file" >/dev/null 2>&1; then
        echo "Found readOnlyPort setting in config file"
        
        # Get the value of readOnlyPort (preserving original logic)
        readonly_port=$(yq '.readOnlyPort' "$config_file" 2>/dev/null)
        
        echo "readOnlyPort value: $readonly_port"
        echo "Required value: 0 (disabled)"
        
        # Check if value is not 0 (preserving original logic)
        if [[ "$readonly_port" != "0" ]]; then
            echo "FAIL: Readonly port enabled in config"
            echo "Current value: ${readonly_port:-undefined}"
            echo "ReadOnly port must be disabled (set to 0) for security"
            echo "Port 10255 exposes Kubernetes pods without authentication"
            exit 1
        else
            echo "ReadOnly port properly disabled in config file"
        fi
    else
        echo "No readOnlyPort setting found in config file"
        echo "Default behavior: readOnlyPort is disabled"
    fi
    
    echo "PASS: Readonly port properly disabled"
    echo "Kubelet readonly port (10255) is not exposed"
    echo "No unauthorized access to pod information available"
    exit 0