---
# STIG Kubernetes V2R2 Kubelet Profile
# Contains kubelet security checks
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: stig-k8s-v2r2-kubelet
  namespace: {{ .Values.global.complianceNamespace }}
spec:
  id: "STIG-K8S-V2R2-KUBELET"
  title: "STIG Kubernetes V2R2 Kubelet Security Profile"
  description: |
    This profile contains kubelet security checks based on the 
    Security Technical Implementation Guide (STIG) for Kubernetes V2R2.
    Focuses on kubelet configuration and node-level security.
  version: "1.0"
  rules:
    - name: stig-k8s-kubelet-anonymous-auth-disabled
    - name: stig-k8s-kubelet-authorization-mode
    - name: stig-k8s-kubelet-ca-file-permissions
    - name: stig-k8s-kubelet-ca-ownership
    - name: stig-k8s-kubelet-client-ca-file
    - name: stig-k8s-kubelet-conf-file-ownership
    - name: stig-k8s-kubelet-conf-file-permissions
    - name: stig-k8s-kubelet-config-file-ownership
    - name: stig-k8s-kubelet-config-file-permissions
    - name: stig-k8s-kubelet-config-ownership
    - name: stig-k8s-kubelet-config-permissions
    - name: stig-k8s-kubelet-dynamic-config-disabled
    - name: stig-k8s-kubelet-hostname-override-disabled
    - name: stig-k8s-kubelet-protect-kernel-defaults
    - name: stig-k8s-kubelet-readonly-port-disabled
    - name: stig-k8s-kubelet-static-pod-path-disabled
    - name: stig-k8s-kubelet-streaming-timeout
    - name: stig-k8s-kubelet-tls-cert-file
    - name: stig-k8s-kubelet-tls-private-key-file 