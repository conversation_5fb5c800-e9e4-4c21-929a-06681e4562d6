apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-pki-key-file-permissions
  namespace: compliance-system
spec:
  id: "V-242467"
  title: "The Kubernetes PKI keys must have file permissions set to 600 or more restrictive."
  description: "The Kubernetes PKI directory contains all certificate key files supporting secure network communications in the Kubernetes Control Plane. If these files can be modified, data traversing within the architecture components would become unsecure and compromised."
  checkText: "Review the permissions of the Kubernetes PKI key files by using the command:\n\nsudo find /etc/kubernetes/pki -name \"*.key\" | xargs stat -c '%n %a'\n\nIf any of the files have permissions more permissive than \"600\", this is a finding."
  fixText: "Change the ownership of the key files to \"600\" by executing the command: \n\nfind /etc/kubernetes/pki -name \"*.key\" | xargs chmod 600"
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting V-242467: Kubernetes PKI key file permissions check"
    
    pki_dir="/host/etc/kubernetes/pki"
    failed_files=""
    has_failures=false
    
    echo "Checking PKI key files in directory: $pki_dir"
    
    if [[ ! -d "$pki_dir" ]]; then
        echo "FAIL: PKI directory not found: $pki_dir"
        exit 1
    fi
    
    echo "Scanning for .key private key files..."
    
    # Check private key files
    key_files=$(find "$pki_dir" -type f -name "*.key" 2>/dev/null)
    if [[ -n "$key_files" ]]; then
        echo "Found key files, checking permissions..."
        
        while IFS= read -r file; do
            if [[ -n "$file" ]]; then
                perm=$(stat -c '%a' "$file" 2>/dev/null)
                echo "Checking file: $file (permissions: $perm)"
                
                if [[ $perm -gt 600 ]]; then
                    echo "FAIL: Excessive permissions $perm for private key $file (should be ≤600)"
                    failed_files+="${failed_files:+, }$(basename "$file"):$perm"
                    has_failures=true
                else
                    echo "PASS: Valid permissions $perm for private key $file"
                fi
            fi
        done <<< "$key_files"
    else
        echo "FAIL: No PKI key files found in $pki_dir"
        exit 1
    fi
    
    if [[ "$has_failures" == true ]]; then
        echo "FAIL: Some key files have excessive permissions: $failed_files"
        exit 1
    else
        echo "PASS: All PKI key files have compliant permissions (≤600)"
        exit 0
    fi 