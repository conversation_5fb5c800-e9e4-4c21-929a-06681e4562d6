apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-admin-conf-file-permissions
  namespace: compliance-system
spec:
  id: "V-242460"
  title: "The Kubernetes admin kubeconfig must have file permissions set to 644 or more restrictive."
  description: "The Kubernetes admin kubeconfig files contain the arguments and settings for the Control Plane services. These services are controller and scheduler. If these files can be changed, the scheduler will be implementing the changes immediately."
  checkText: "Review the permissions of the Kubernetes config files by using the command:\n\nstat -c %a /etc/kubernetes/admin.conf\nstat -c %a /etc/kubernetes/scheduler.conf\nstat -c %a /etc/kubernetes/controller-manager.conf\n\nIf any of the files are have permissions more permissive than \"644\", this is a finding."
  fixText: "Change the permissions of the conf files to \"644\" by executing the command:\n\nchmod 644 /etc/kubernetes/admin.conf \nchmod 644 /etc/kubernetes/scheduler.conf\nchmod 644 /etc/kubernetes/controller-manager.conf"
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    result="PASSED"
    details=""
    critical_files=(
        "/host/etc/kubernetes/admin.conf"
        "/host/etc/kubernetes/scheduler.conf"
        "/host/etc/kubernetes/controller-manager.conf"
    )

    for file in "${critical_files[@]}"; do
        if [[ -f "$file" ]]; then
            perm=$(stat -c '%a' "$file")
            echo "Checking $(basename "$file"): permissions $perm"
            if [[ $perm -gt 644 ]]; then
                result="FAILED"
                details+="Excessive permissions for $file ($perm)<br>"
            fi
        else
            result="FAILED"
            details+="Missing critical file: $file<br>"
            echo "WARNING: Missing file $(basename "$file")"
        fi
    done

    if [[ "$result" == "PASSED" ]]; then
        details="All kubeconfig files have compliant permissions"
        echo "PASS: $details"
        exit 0
    else
        echo "FAIL: $details"
        exit 1
    fi 