apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-scheduler-pps
  namespace: compliance-system
spec:
  id: "V-242411"
  title: "The Kubernetes Scheduler must enforce ports, protocols, and services (PPS) that adhere to the Ports, Protocols, and Services Management Category Assurance List (PPSM CAL)."
  description: "Kubernetes Scheduler PPSs must be controlled and conform to the PPSM CAL. Those PPS that fall outside the PPSM CAL must be blocked. Instructions on the PPSM can be found in DoD Instruction 8551.01 Policy."
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep kube-scheduler.manifest -I -secure-port *\n-edit manifest file:\nVIM <Manifest Name>\nReview livenessProbe:\nHttpGet:\nPort:\nReview ports:\n- containerPort:\nhostPort:\n- containerPort:\nhostPort:\n\nRun command: \nkubectl describe services --all-namespaces \nSearch labels for any scheduler namespaces.\nPort:\n\nAny manifest and namespace PPS or services configuration not in compliance with PPSM CAL is a finding.\n\nReview the information systems documentation and interview the team, gain an understanding of the Scheduler architecture, and determine applicable PPS. If there are any PPS in the system documentation not in compliance with the CAL PPSM, this is a finding. Any PPS not set in the system documentation is a finding.\n\nReview findings against the most recent PPSM CAL:\nhttps://cyber.mil/ppsm/cal/\n\nVerify Scheduler network boundary with the PPS associated with the CAL Assurance Categories. Any PPS not in compliance with the CAL Assurance Category requirements is a finding."
  fixText: "Edit the Kubernetes Scheduler manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of --secure-port to a port that is in compliance with the PPSM CAL.\n\nAny other ports used by the Scheduler must be in compliance with the PPSM CAL.\n\nReview the information systems documentation and interview the team, gain an understanding of the Scheduler architecture, and determine applicable PPS. Document all PPS in the system documentation. All PPS must be in compliance with the CAL PPSM."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting V-242411: Kubernetes Scheduler PPSM CAL compliance check"
    echo ""
    echo "MANUAL: This rule requires manual verification and cannot be automated"
    echo ""
    echo "PPSM CAL Compliance Requirements:"
    echo "All ports, protocols, and services must conform to the PPSM Category Assurance List"
    echo ""
    echo "Manual Verification Steps:"
    echo ""
    echo "1. Scheduler Manifest Review:"
    echo "   a) Navigate to manifests directory:"
    echo "      cd /etc/kubernetes/manifests"
    echo ""
    echo "   b) Check scheduler manifest for secure port configuration:"
    echo "      grep -i 'secure-port' kube-scheduler.yaml"
    echo ""
    echo "   c) Review scheduler manifest file:"
    echo "      cat kube-scheduler.yaml"
    echo ""
    echo "   d) Verify the following configurations:"
    echo "      - --secure-port parameter setting"
    echo "      - livenessProbe httpGet port configuration"
    echo "      - readinessProbe httpGet port configuration"
    echo "      - containerPort settings"
    echo "      - hostPort settings (if any)"
    echo ""
    echo "2. Service Configuration Review:"
    echo "   a) List all services and check for scheduler-related services:"
    echo "      kubectl describe services --all-namespaces"
    echo ""
    echo "   b) Search for any scheduler-related namespaces and services:"
    echo "      kubectl get services --all-namespaces | grep -i scheduler"
    echo ""
    echo "   c) Review port configurations in any scheduler services found"
    echo ""
    echo "3. Network Boundary Verification:"
    echo "   a) Review Scheduler network architecture and topology"
    echo "   b) Identify all ports, protocols, and services used by the Scheduler"
    echo "   c) Verify each PPS against PPSM CAL requirements"
    echo "   d) Ensure no unauthorized ports/protocols are in use"
    echo ""
    echo "4. Documentation Requirements:"
    echo "   a) Review information systems documentation for Scheduler PPS"
    echo "   b) Interview team to understand Scheduler architecture"
    echo "   c) Verify all PPS are documented in system documentation"
    echo "   d) Ensure documented PPS comply with PPSM CAL"
    echo ""
    echo "5. PPSM CAL Verification:"
    echo "   a) Access the most recent PPSM CAL:"
    echo "      https://cyber.mil/ppsm/cal/"
    echo ""
    echo "   b) Cross-reference all identified Scheduler PPS with CAL"
    echo "   c) Verify compliance with CAL Assurance Categories"
    echo "   d) Document any exceptions or deviations"
    echo ""
    echo "Finding Criteria:"
    echo "- Any PPS configuration not in compliance with PPSM CAL"
    echo "- Undocumented PPS in system documentation"
    echo "- PPS not aligned with CAL Assurance Category requirements"
    echo "- Unauthorized or non-compliant ports/protocols in use"
    echo ""
    echo "Reference Resources:"
    echo "- PPSM CAL: https://cyber.mil/ppsm/cal/"
    echo "- STIG Details: https://stigviewer.com/stigs/kubernetes/2024-08-22/finding/V-242411"
    echo "- DoD Instruction 8551.01 Policy for PPSM requirements"
    echo ""
    echo "This check requires manual verification by authorized personnel"
    
    exit 2