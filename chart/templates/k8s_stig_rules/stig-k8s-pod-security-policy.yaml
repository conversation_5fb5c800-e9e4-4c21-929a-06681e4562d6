apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-pod-security-policy
  namespace: compliance-system
spec:
  id: "V-242437"
  title: "Kubernetes must have a pod security policy set."
  description: "Enabling the admissions webhook allows for Kubernet<PERSON> to apply policies against objects that are to be created, read, updated, or deleted. By applying a pod security policy, control can be given to not allow images to be instantiated that run as the root user. If pods run as the root user, the pod then has root privileges to the host system and all the resources it has. An attacker can use this to attack the Kubernetes cluster. By implementing a policy that does not allow root or privileged pods, the pod users are limited in what the pod can do and access."
  checkText: "Prior to version 1.21, to enforce security policiesPod Security Policies (psp) were used. Those are now deprecated and will be removed from version 1.25.\n\nMigrate from PSP to PSA:\nhttps://kubernetes.io/docs/tasks/configure-pod-container/migrate-from-psp/ \n\nPre-version 1.25 Check:\nOn the Control Plane, run the command:\nkubectl get podsecuritypolicy\n\nIf there is no pod security policy configured, this is a finding. \n\nFor any pod security policies listed, edit the policy with the command:\nkubectl edit podsecuritypolicy policyname\n(Note: \"policyname\" is the name of the policy.)\n\nReview the runAsUser, supplementalGroups and fsGroup sections of the policy.\n\nIf any of these sections are missing, this is a finding.\n\nIf the rule within the runAsUser section is not set to \"MustRunAsNonRoot\", this is a finding.\n\nIf the ranges within the supplementalGroups section has min set to \"0\" or min is missing, this is a finding.\n\nIf the ranges within the fsGroup section has a min set to \"0\" or the min is missing, this is a finding."
  fixText: "From the Control Plane, save the following policy to a file called restricted.yml.\n\napiVersion: policy/v1beta1\nkind: PodSecurityPolicy\nmetadata:\nname: restricted\nannotations:\napparmor.security.beta.kubernetes.io/allowedProfileNames: 'runtime/default',\nseccomp.security.alpha.kubernetes.io/defaultProfileName: 'runtime/default',\napparmor.security.beta.kubernetes.io/defaultProfileName: 'runtime/default'\nspec:\nprivileged: false\n# Required to prevent escalations to root.\nallowPrivilegeEscalation: false\n# This is redundant with non-root + disallow privilege escalation,\n# but we can provide it for defense in depth.\nrequiredDropCapabilities:\n- ALL\n# Allow core volume types.\nvolumes:\n- 'configMap'\n- 'emptyDir'\n- 'projected'\n- 'secret'\n- 'downwardAPI'\n# Assume that persistentVolumes set up by the cluster admin are safe to use.\n- 'persistentVolumeClaim'\nhostNetwork: false\nhostIPC: false\nhostPID: false\nrunAsUser:\n# Require the container to run without root privileges.\nrule: 'MustRunAsNonRoot'\nseLinux:\n# This policy assumes the nodes are using AppArmor rather than SELinux.\nrule: 'RunAsAny'\nsupplementalGroups:\nrule: 'MustRunAs'\nranges:\n# Forbid adding the root group.\n- min: 1\nmax: 65535\nfsGroup:\nrule: 'MustRunAs'\nranges:\n# Forbid adding the root group.\n- min: 1\nmax: 65535\nreadOnlyRootFilesystem: false\n\nTo implement the policy, run the command:\nkubectl create -f restricted.yml"
  severity: "high"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting V-242437: Kubernetes Pod Security Policy check"
    echo ""
    echo "MANUAL: This rule requires manual verification due to PSP deprecation"
    echo ""
    echo "IMPORTANT NOTICE:"
    echo "Pod Security Policies (PSP) are deprecated as of Kubernetes v1.21"
    echo "and will be removed in Kubernetes v1.25"
    echo ""
    echo "Migration Required:"
    echo "Organizations should migrate from PSP to Pod Security Admission (PSA)"
    echo "Reference: https://kubernetes.io/docs/tasks/configure-pod-container/migrate-from-psp/"
    echo ""
    echo "Manual Verification Steps:"
    echo ""
    echo "1. Check Kubernetes Version:"
    echo "   kubectl version --short"
    echo ""
    echo "2. For Kubernetes < v1.25 (Legacy PSP Check):"
    echo "   a) List existing Pod Security Policies:"
    echo "      kubectl get podsecuritypolicy"
    echo ""
    echo "   b) If policies exist, review each policy configuration:"
    echo "      kubectl get podsecuritypolicy <policy-name> -o yaml"
    echo ""
    echo "   c) Verify required security settings in each policy:"
    echo "      - runAsUser.rule must be 'MustRunAsNonRoot'"
    echo "      - supplementalGroups.ranges[].min must be > 0"
    echo "      - fsGroup.ranges[].min must be > 0"
    echo "      - privileged must be false"
    echo "      - allowPrivilegeEscalation must be false"
    echo ""
    echo "3. For Kubernetes >= v1.25 or PSP Migration:"
    echo "   a) Verify Pod Security Admission is configured:"
    echo "      kubectl get pods -n kube-system -l component=kube-apiserver -o yaml | grep admission-control-config-file"
    echo ""
    echo "   b) Check namespace security levels:"
    echo "      kubectl get namespaces -o yaml | grep 'pod-security'"
    echo ""
    echo "   c) Verify Pod Security Standards are enforced:"
    echo "      kubectl auth can-i create pods/exec --as=system:serviceaccount:default:default"
    echo ""
    echo "4. Verification Criteria:"
    echo "   - For PSP: At least one restrictive policy must exist with proper security controls"
    echo "   - For PSA: Pod Security Admission must be enabled with appropriate security levels"
    echo "   - No pods should be able to run as root or with privileged access"
    echo ""
    echo "5. Finding Criteria:"
    echo "   - No security policies configured (PSP or PSA)"
    echo "   - Policies allow root execution or privileged access"
    echo "   - Missing required security constraints"
    echo ""
    echo "Recommended Actions:"
    echo "- If using Kubernetes < v1.25: Ensure proper PSP configuration"
    echo "- If using Kubernetes >= v1.25: Migrate to Pod Security Admission"
    echo "- Implement 'restricted' security profile for production workloads"
    echo ""
    echo "This check requires manual verification by authorized personnel"
    
    exit 2