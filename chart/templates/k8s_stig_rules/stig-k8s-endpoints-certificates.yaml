apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-endpoints-certificates
  namespace: compliance-system
spec:
  id: "V-245544"
  title: "Kubernetes endpoints must use approved organizational certificate and key pair to protect information in transit."
  description: "Kubernetes endpoints must use approved certificates to ensure secure communication."
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i kubelet-client-certificate *\ngrep -I kubelet-client-key * \n\nIf the setting \"--kubelet-client-certificate\" is not configured in the Kubernetes API server manifest file or contains no value, this is a finding.\n\nIf the setting \"--kubelet-client-key\" is not configured in the Kubernetes API server manifest file or contains no value, this is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of \"--kubelet-client-certificate\" and \"--kubelet-client-key\" to an Approved Organizational Certificate and key pair"
  severity: "high"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting Kubernetes endpoints certificate validation check (V-245544)"
    echo ""
    
    # Environment debugging
    echo "=== Environment Information ==="
    echo "Current user: $(whoami)"
    echo "Current working directory: $(pwd)"
    echo "Available tools:"
    echo "  grep: $(which grep 2>/dev/null || echo 'NOT FOUND')"
    echo "  awk: $(which awk 2>/dev/null || echo 'NOT FOUND')"
    echo "  stat: $(which stat 2>/dev/null || echo 'NOT FOUND')"
    echo ""
    
    # Check manifest file
    echo "=== Checking API Server manifest ==="
    manifest_path="/etc/kubernetes/manifests/kube-apiserver.yaml"
    actual_manifest_path=""
    
    if [[ -f "$manifest_path" ]]; then
        actual_manifest_path="$manifest_path"
    elif [[ -f "/host$manifest_path" ]]; then
        actual_manifest_path="/host$manifest_path"
    else
        echo "FAIL: API Server manifest file not found"
        echo "Checked paths:"
        echo "  - $manifest_path"
        echo "  - /host$manifest_path"
        exit 1
    fi
    
    echo "Found API Server manifest: $actual_manifest_path"
    echo ""
    
    # Initialize tracking variables
    cert_configured=0
    key_configured=0
    cert_valid=0
    key_valid=0
    
    # Check kubelet client certificate configuration
    echo "=== Checking kubelet client certificate configuration ==="
    cert_line=$(grep -E -- "--kubelet-client-certificate(=[^[:space:]]+|[[:space:]]+[^[:space:]]+)" "$actual_manifest_path" 2>/dev/null)
    
    if [[ -n "$cert_line" ]]; then
        echo "Found certificate configuration line:"
        echo "  $cert_line"
        
        cert_path=$(echo "$cert_line" | awk -F= '{print $2}' | awk '{print $1}')
        cert_configured=1
        
        echo "Extracted certificate path: $cert_path"
        
        # Check certificate file existence
        actual_cert_path=""
        if [[ -f "$cert_path" ]]; then
            actual_cert_path="$cert_path"
        elif [[ -f "/host$cert_path" ]]; then
            actual_cert_path="/host$cert_path"
        fi
        
        if [[ -n "$actual_cert_path" ]]; then
            echo "SUCCESS: Certificate file found at $actual_cert_path"
            cert_valid=1
        else
            echo "FAIL: Certificate file not found"
            echo "Checked paths:"
            echo "  - $cert_path"
            echo "  - /host$cert_path"
        fi
    else
        echo "FAIL: --kubelet-client-certificate parameter not configured in manifest"
    fi
    echo ""
    
    # Check kubelet client key configuration
    echo "=== Checking kubelet client key configuration ==="
    key_line=$(grep -E -- "--kubelet-client-key(=[^[:space:]]+|[[:space:]]+[^[:space:]]+)" "$actual_manifest_path" 2>/dev/null)
    
    if [[ -n "$key_line" ]]; then
        echo "Found key configuration line:"
        echo "  $key_line"
        
        key_path=$(echo "$key_line" | awk -F= '{print $2}' | awk '{print $1}')
        key_configured=1
        
        echo "Extracted key path: $key_path"
        
        # Check key file existence
        actual_key_path=""
        if [[ -f "$key_path" ]]; then
            actual_key_path="$key_path"
        elif [[ -f "/host$key_path" ]]; then
            actual_key_path="/host$key_path"
        fi
        
        if [[ -n "$actual_key_path" ]]; then
            echo "SUCCESS: Key file found at $actual_key_path"
            
            # Check key file permissions
            perms=$(stat -c %a "$actual_key_path" 2>/dev/null)
            if [[ -n "$perms" ]]; then
                echo "Key file permissions: $perms"
                if [[ "$perms" == "600" ]]; then
                    echo "SUCCESS: Key file has correct permissions (600)"
                    key_valid=1
                else
                    echo "FAIL: Key file has incorrect permissions ($perms), should be 600"
                fi
            else
                echo "WARNING: Could not check key file permissions"
                key_valid=1  # Assume valid if we can't check permissions
            fi
        else
            echo "FAIL: Key file not found"
            echo "Checked paths:"
            echo "  - $key_path"
            echo "  - /host$key_path"
        fi
    else
        echo "FAIL: --kubelet-client-key parameter not configured in manifest"
    fi
    echo ""
    
    # Final evaluation
    echo "=== Final evaluation ==="
    echo "Configuration status:"
    echo "  Certificate configured: $([[ $cert_configured -eq 1 ]] && echo 'YES' || echo 'NO')"
    echo "  Certificate valid: $([[ $cert_valid -eq 1 ]] && echo 'YES' || echo 'NO')"
    echo "  Key configured: $([[ $key_configured -eq 1 ]] && echo 'YES' || echo 'NO')"
    echo "  Key valid: $([[ $key_valid -eq 1 ]] && echo 'YES' || echo 'NO')"
    echo ""
    
    if [[ $cert_configured -eq 1 && $key_configured -eq 1 && $cert_valid -eq 1 && $key_valid -eq 1 ]]; then
        echo "PASS: Secure kubelet client authentication properly configured"
        echo "Certificate: $cert_path"
        echo "Key: $key_path"
        exit 0
    else
        echo "FAIL: Missing or invalid client certificate/key configuration"
        
        if [[ $cert_configured -eq 0 ]]; then
            echo "  - kubelet-client-certificate parameter not configured"
        fi
        if [[ $key_configured -eq 0 ]]; then
            echo "  - kubelet-client-key parameter not configured"
        fi
        if [[ $cert_configured -eq 1 && $cert_valid -eq 0 ]]; then
            echo "  - Certificate file not accessible"
        fi
        if [[ $key_configured -eq 1 && $key_valid -eq 0 ]]; then
            echo "  - Key file not accessible or has wrong permissions"
        fi
        
        exit 1
    fi