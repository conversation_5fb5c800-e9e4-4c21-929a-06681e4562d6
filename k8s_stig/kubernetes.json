{"stig": {"date": "2025-02-20", "description": "This Security Technical Implementation Guide is published as a tool to improve the security of Department of Defense (DOD) information systems. The requirements are derived from the National Institute of Standards and Technology (NIST) 800-53 and related documents. Comments or proposed revisions to this document should be sent via email to the following address: <EMAIL>.", "findings": {"V-242376": {"checkid": "C-45651r863731_chk", "checktext": "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i tls-min-version * \n\nIf the setting \"tls-min-version\" is not configured in the Kubernetes Controller Manager manifest file or it is set to \"VersionTLS10\" or \"VersionTLS11\", this is a finding.", "description": "The Kubernetes Controller Manager will prohibit the use of SSL and unauthorized versions of TLS protocols to properly secure communication.\n\nThe use of unsupported protocol exposes vulnerabilities to the Kubernetes by rogue traffic interceptions, man-in-the-middle attacks, and impersonation of users or services from the container platform runtime, registry, and key store. To enable the minimum version of TLS to be used by the Kubernetes Controller Manager, the setting \"tls-min-version\" must be set.", "fixid": "F-45609r863732_fix", "fixtext": "Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of \"--tls-min-version\" to \"VersionTLS12\" or higher.", "iacontrols": null, "id": "V-242376", "ruleID": "SV-242376r960759_rule", "severity": "medium", "title": "The Kubernetes Controller Manager must use TLS 1.2, at a minimum, to protect the confidentiality of sensitive data during electronic dissemination.", "version": "TBD"}, "V-242377": {"checkid": "C-45652r863734_chk", "checktext": "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i tls-min-version * \n\nIf the setting \"tls-min-version\" is not configured in the Kubernetes Scheduler manifest file or it is set to \"VersionTLS10\" or \"VersionTLS11\", this is a finding.", "description": "The Kubernetes Scheduler will prohibit the use of SSL and unauthorized versions of TLS protocols to properly secure communication.\n\nThe use of unsupported protocol exposes vulnerabilities to the Kubernetes by rogue traffic interceptions, man-in-the-middle attacks, and impersonation of users or services from the container platform runtime, registry, and keystore. To enable the minimum version of TLS to be used by the Kubernetes API Server, the setting \"tls-min-version\" must be set.", "fixid": "F-45610r863735_fix", "fixtext": "Edit the Kubernetes Scheduler manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of \"--tls-min-version\" to \"VersionTLS12\" or higher.", "iacontrols": null, "id": "V-242377", "ruleID": "SV-242377r960759_rule", "severity": "medium", "title": "The Kubernetes Scheduler must use TLS 1.2, at a minimum, to protect the confidentiality of sensitive data during electronic dissemination.", "version": "TBD"}, "V-242378": {"checkid": "C-45653r863737_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i tls-min-version * \n\nIf the setting \"tls-min-version\" is not configured in the Kubernetes API Server manifest file or it is set to \"VersionTLS10\" or \"VersionTLS11\", this is a finding.", "description": "The Kubernetes API Server will prohibit the use of SSL and unauthorized versions of TLS protocols to properly secure communication.\n\nThe use of unsupported protocol exposes vulnerabilities to the Kubernetes by rogue traffic interceptions, man-in-the-middle attacks, and impersonation of users or services from the container platform runtime, registry, and keystore. To enable the minimum version of TLS to be used by the Kubernetes API Server, the setting \"tls-min-version\" must be set.", "fixid": "F-45611r863738_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of \"--tls-min-version\" to \"VersionTLS12\" or higher.", "iacontrols": null, "id": "V-242378", "ruleID": "SV-242378r960759_rule", "severity": "medium", "title": "The Kubernetes API Server must use TLS 1.2, at a minimum, to protect the confidentiality of sensitive data during electronic dissemination.", "version": "TBD"}, "V-242379": {"checkid": "C-45654r927069_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i  auto-tls * \n\nIf the setting \"--auto-tls\" is not configured in the Kubernetes etcd manifest file or it is set to true, this is a finding.", "description": "Kubernetes etcd will prohibit the use of SSL and unauthorized versions of TLS protocols to properly secure communication.\n\nThe use of unsupported protocol exposes vulnerabilities to the Kubernetes by rogue traffic interceptions, man-in-the-middle attacks, and impersonation of users or services from the container platform runtime, registry, and keystore. To enable the minimum version of TLS to be used by the Kubernetes API Server, the setting \"--auto-tls\" must be set.", "fixid": "F-45612r927070_fix", "fixtext": "Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--auto-tls\" to \"false\".", "iacontrols": null, "id": "V-242379", "ruleID": "SV-242379r960759_rule", "severity": "medium", "title": "The Kubernetes etcd must use TLS to protect the confidentiality of sensitive data during electronic dissemination.", "version": "TBD"}, "V-242380": {"checkid": "C-45655r927072_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\ngrep -I  peer-auto-tls * \n\nIf the setting \"--peer-auto-tls\" is not configured in the Kubernetes etcd manifest file or it is set to \"true\", this is a finding.", "description": "The Kubernetes API Server will prohibit the use of SSL and unauthorized versions of TLS protocols to properly secure communication.\n\nThe use of unsupported protocol exposes vulnerabilities to the Kubernetes by rogue traffic interceptions, man-in-the-middle attacks, and impersonation of users or services from the container platform runtime, registry, and keystore. To enable the minimum version of TLS to be used by the Kubernetes API Server, the setting \"--peer-auto-tls\" must be set.", "fixid": "F-45613r927073_fix", "fixtext": "Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--peer-auto-tls\" to \"false\".", "iacontrols": null, "id": "V-242380", "ruleID": "SV-242380r960759_rule", "severity": "medium", "title": "The Kubernetes etcd must use TLS to protect the confidentiality of sensitive data during electronic dissemination.", "version": "TBD"}, "V-242381": {"checkid": "C-45656r927075_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i use-service-account-credentials * \n\nIf the setting \"--use-service-account-credentials\" is not configured in the Kubernetes Controller Manager manifest file or it is set to \"false\", this is a finding.", "description": "The Kubernetes Controller Manager is a background process that embeds core control loops regulating cluster system state through the API Server. Every process executed in a pod has an associated service account. By default, service accounts use the same credentials for authentication. Implementing the default settings poses a High risk to the Kubernetes Controller Manager. Setting the \"--use-service-account-credential\" value lowers the attack surface by generating unique service accounts settings for each controller instance.", "fixid": "F-45614r927076_fix", "fixtext": "Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.\n\nSet the value of \"--use-service-account-credentials\" to \"true\".", "iacontrols": null, "id": "V-242381", "ruleID": "SV-242381r1043176_rule", "severity": "high", "title": "The Kubernetes Controller Manager must create unique service accounts for each work payload.", "version": "TBD"}, "V-242382": {"checkid": "C-45657r918144_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\ngrep -i authorization-mode * \n\nIf the setting authorization-mode is set to \"AlwaysAllow\" in the Kubernetes API Server manifest file or is not configured, this is a finding.", "description": "To mitigate the risk of unauthorized access to sensitive information by entities that have been issued certificates by DOD-approved PKIs, all DOD systems (e.g., networks, web servers, and web portals) must be properly configured to incorporate access control methods that do not rely solely on the possession of a certificate for access. Successful authentication must not automatically give an entity access to an asset or security boundary. Authorization procedures and controls must be implemented to ensure each authenticated entity also has a validated and current authorization. Authorization is the process of determining whether an entity, once authenticated, is permitted to access a specific asset.\n\nNode,RBAC is the method within Kubernetes to control access of users and applications. Kubernetes uses roles to grant authorization API requests made by kubelets.\n\nSatisfies: SRG-APP-000340-CTR-000770, SRG-APP-000033-CTR-000095, SRG-APP-000378-CTR-000880, SRG-APP-000033-CTR-000090", "fixid": "F-45615r918145_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--authorization-mode\" to \"Node,RBAC\".", "iacontrols": null, "id": "V-242382", "ruleID": "SV-242382r960792_rule", "severity": "medium", "title": "The Kubernetes API Server must enable Node,RBAC as the authorization mode.", "version": "TBD"}, "V-242383": {"checkid": "C-45658r863752_chk", "checktext": "To view the available namespaces, run the command:\n\nkubectl get namespaces\n\nThe default namespaces to be validated are default, kube-public, and kube-node-lease if it is created.\n\nFor the default namespace, execute the commands:\n\nkubectl config set-context --current --namespace=default\nkubectl get all\n\nFor the kube-public namespace, execute the commands:\n\nkubectl config set-context --current --namespace=kube-public\nkubectl get all\n\nFor the kube-node-lease namespace, execute the commands:\n\nkubectl config set-context --current --namespace=kube-node-lease\nkubectl get all\n\nThe only valid return values are the kubernetes service (i.e., service/kubernetes) and nothing at all.\n\nIf a return value is returned from the \"kubectl get all\" command and it is not the kubernetes service (i.e., service/kubernetes), this is a finding.", "description": "Creating namespaces for user-managed resources is important when implementing Role-Based Access Controls (RBAC). RBAC allows for the authorization of users and helps support proper API server permissions separation and network micro segmentation. If user-managed resources are placed within the default namespaces, it becomes impossible to implement policies for RBAC permission, service account usage, network policies, and more.", "fixid": "F-45616r863753_fix", "fixtext": "Move any user-managed resources from the default, kube-public, and kube-node-lease namespaces to user namespaces.", "iacontrols": null, "id": "V-242383", "ruleID": "SV-242383r960801_rule", "severity": "high", "title": "User-managed resources must be created in dedicated namespaces.", "version": "TBD"}, "V-242384": {"checkid": "C-45659r863755_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i bind-address * \n\nIf the setting \"bind-address\" is not set to \"127.0.0.1\" or is not found in the Kubernetes Scheduler manifest file, this is a finding.", "description": "Limiting the number of attack vectors and implementing authentication and encryption on the endpoints available to external sources is paramount when securing the overall Kubernetes cluster. The Scheduler API service exposes port 10251/TCP by default for health and metrics information use. This port does not encrypt or authenticate connections. If this port is exposed externally, an attacker can use this port to attack the entire Kubernetes cluster. By setting the bind address to localhost (i.e., 127.0.0.1), only those internal services that require health and metrics information can access the Scheduler API.", "fixid": "F-45617r863756_fix", "fixtext": "Edit the Kubernetes Scheduler manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument \"--bind-address\" to \"127.0.0.1\".", "iacontrols": null, "id": "V-242384", "ruleID": "SV-242384r960792_rule", "severity": "medium", "title": "The Kubernetes Scheduler must have secure binding.", "version": "TBD"}, "V-242385": {"checkid": "C-45660r863758_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i bind-address * \n\nIf the setting bind-address is not set to \"127.0.0.1\" or is not found in the Kubernetes Controller Manager manifest file, this is a finding.", "description": "Limiting the number of attack vectors and implementing authentication and encryption on the endpoints available to external sources is paramount when securing the overall Kubernetes cluster. The Controller Manager API service exposes port 10252/TCP by default for health and metrics information use. This port does not encrypt or authenticate connections. If this port is exposed externally, an attacker can use this port to attack the entire Kubernetes cluster. By setting the bind address to only localhost (i.e., 127.0.0.1), only those internal services that require health and metrics information can access the Control Manager API.", "fixid": "F-45618r863759_fix", "fixtext": "Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument \"--bind-address\" to \"127.0.0.1\".", "iacontrols": null, "id": "V-242385", "ruleID": "SV-242385r960792_rule", "severity": "medium", "title": "The Kubernetes Controller Manager must have secure binding.", "version": "TBD"}, "V-242386": {"checkid": "C-45661r927079_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i insecure-port * \n\nIf the setting \"--insecure-port\" is not set to \"0\" or is not configured in the Kubernetes API server manifest file, this is a finding.\n\nNote: \"--insecure-port\" flag has been deprecated and can only be set to \"0\". **This flag  will be removed in v1.24.*", "description": "By default, the API server will listen on two ports. One port is the secure port and the other port is called the \"localhost port\". This port is also called the \"insecure port\", port 8080. Any requests to this port bypass authentication and authorization checks. If this port is left open, anyone who gains access to the host on which the Control Plane is running can bypass all authorization and authentication mechanisms put in place, and have full control over the entire cluster.\n\nClose the insecure port by setting the API server's \"--insecure-port\" flag to \"0\", ensuring that the \"--insecure-bind-address\" is not set.", "fixid": "F-45619r927080_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.\n\nSet the value of \"--insecure-port\" to \"0\".", "iacontrols": null, "id": "V-242386", "ruleID": "SV-242386r960792_rule", "severity": "high", "title": "The Kubernetes API server must have the insecure port flag disabled.", "version": "TBD"}, "V-242387": {"checkid": "C-45662r918147_chk", "checktext": "On each Control Plane and Worker Node, run the command:\nps -ef | grep kubelet\n\nIf the \"--read-only-port\" option exists, this is a finding. \n\nNote the path to the config file (identified by --config).\n\nRun the command:\ngrep -i readOnlyPort <path_to_config_file>\n\nIf the setting \"readOnlyPort\" exists and is not set to \"0\", this is a finding.", "description": "Kubelet serves a small REST API with read access to port 10255. The read-only port for Kubernetes provides no authentication or authorization security control. Providing unrestricted access on port 10255 exposes Kubernetes pods and containers to malicious attacks or compromise. Port 10255 is deprecated and should be disabled.", "fixid": "F-45620r918148_fix", "fixtext": "On each Control Plane and Worker Node, run the command:\nps -ef | grep kubelet\n\nRemove the \"--read-only-port\" option if present.\n\nNote the path to the config file (identified by --config).\n\nEdit the config file: \nSet \"readOnlyPort\" to \"0\" or remove the setting.\n\nRestart the kubelet service using the following command:\nsystemctl daemon-reload && systemctl restart kubelet", "iacontrols": null, "id": "V-242387", "ruleID": "SV-242387r960792_rule", "severity": "high", "title": "The Kubernetes Kubelet must have the \"readOnlyPort\" flag disabled.", "version": "TBD"}, "V-242388": {"checkid": "C-45663r927082_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i insecure-bind-address * \n\nIf the setting \"--insecure-bind-address\" is found and set to \"localhost\" in the Kubernetes API manifest file, this is a finding.", "description": "By default, the API server will listen on two ports and addresses. One address is the secure address and the other address is called the \"insecure bind\" address and is set by default to localhost. Any requests to this address bypass authentication and authorization checks. If this insecure bind address is set to localhost, anyone who gains access to the host on which the Control Plane is running can bypass all authorization and authentication mechanisms put in place and have full control over the entire cluster.\n\nClose or set the insecure bind address by setting the API server's \"--insecure-bind-address\" flag to an IP or leave it unset and ensure that the \"--insecure-bind-port\" is not set.", "fixid": "F-45621r927083_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nRemove the value of \"--insecure-bind-address\" setting.", "iacontrols": null, "id": "V-242388", "ruleID": "SV-242388r960792_rule", "severity": "high", "title": "The Kubernetes API server must have the insecure bind address not set.", "version": "TBD"}, "V-242389": {"checkid": "C-45664r927085_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i secure-port * \n\nIf the setting \"--secure-port\" is set to \"0\" or is not configured in the Kubernetes API manifest file, this is a finding.", "description": "By default, the API server will listen on what is rightfully called the secure port, port 6443. Any requests to this port will perform authentication and authorization checks. If this port is disabled, anyone who gains access to the host on which the Control Plane is running has full control of the entire cluster over encrypted traffic.\n\nOpen the secure port by setting the API server's \"--secure-port\" flag to a value other than \"0\".", "fixid": "F-45622r927086_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--secure-port\" to a value greater than \"0\".", "iacontrols": null, "id": "V-242389", "ruleID": "SV-242389r960792_rule", "severity": "medium", "title": "The Kubernetes API server must have the secure port set.", "version": "TBD"}, "V-242390": {"checkid": "C-45665r927088_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i anonymous-auth * \n\nIf the setting \"--anonymous-auth\" is set to \"true\" in the Kubernetes API Server manifest file, this is a finding.", "description": "The Kubernetes API Server controls Kubernetes via an API interface. A user who has access to the API essentially has root access to the entire Kubernetes cluster. To control access, users must be authenticated and authorized. By allowing anonymous connections, the controls put in place to secure the API can be bypassed.\n\nSetting \"--anonymous-auth\" to \"false\" also disables unauthenticated requests from kubelets.\n\nWhile there are instances where anonymous connections may be needed (e.g., health checks) and Role-Based Access Controls (RBACs) are in place to limit the anonymous access, this access should be disabled, and only enabled when necessary.", "fixid": "F-45623r927089_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of  \"--anonymous-auth\" to \"false\".", "iacontrols": null, "id": "V-242390", "ruleID": "SV-242390r960792_rule", "severity": "high", "title": "The Kubernetes API server must have anonymous authentication disabled.", "version": "TBD"}, "V-242391": {"checkid": "C-45666r918150_chk", "checktext": "On each Control Plane and Worker Node, run the command:\nps -ef | grep kubelet\n\nIf the \"--anonymous-auth\" option exists, this is a finding. \n\nNote the path to the config file (identified by --config).\n\nInspect the content of the config file:\nLocate the \"anonymous\" section under \"authentication\".  In this section, if the field \"enabled\" does not exist or is set to \"true\", this is a finding.", "description": "A user who has access to the Kubelet essentially has root access to the nodes contained within the Kubernetes Control Plane. To control access, users must be authenticated and authorized. By allowing anonymous connections, the controls put in place to secure the Kubelet can be bypassed.\n\nSetting anonymous authentication to \"false\" also disables unauthenticated requests from kubelets.\n\nWhile there are instances where anonymous connections may be needed (e.g., health checks) and Role-Based Access Controls (RBAC) are in place to limit the anonymous access, this access must be disabled and only enabled when necessary.", "fixid": "F-45624r918151_fix", "fixtext": "On each Control Plane and Worker Node, run the command:\nps -ef | grep kubelet\n\nRemove the \"anonymous-auth\" option if present.\n\nNote the path to the config file (identified by --config).\n\nEdit the config file: \nLocate the \"authentication\" section and the \"anonymous\" subsection. Within the \"anonymous\" subsection, set \"enabled\" to \"false\".\n\nRestart the kubelet service using the following command:\nsystemctl daemon-reload && systemctl restart kubelet", "iacontrols": null, "id": "V-242391", "ruleID": "SV-242391r960792_rule", "severity": "high", "title": "The Kubernetes Kubelet must have anonymous authentication disabled.", "version": "TBD"}, "V-242392": {"checkid": "C-45667r1069459_chk", "checktext": "Run the following command on each Worker Node:\nps -ef | grep kubelet\nVerify that the --authorization-mode exists and is set to \"Webhook\".\n\nIf the --authorization-mode argument is not set to \"Webhook\" or doesn't exist, this is a finding.", "description": "<PERSON><PERSON><PERSON> is the primary agent on each node. The API server communicates with each kubelet to perform tasks such as starting/stopping pods. By default, kubelets allow all authenticated requests, even anonymous ones, without requiring any authorization checks from the API server. This default behavior bypasses any authorization controls put in place to limit what users may perform within the Kubernetes cluster. To change this behavior, the default setting of AlwaysAllow for the authorization mode must be set to \"Webhook\".", "fixid": "F-45625r1069460_fix", "fixtext": "Edit the Kubernetes Kubelet service file in the --config directory on the Kubernetes Worker Node:\n\nSet the value of \"--authorization-mode\" to \"Webhook\" in KUBELET_SYSTEM_PODS_ARGS variable.\n\nRestart the kubelet service using the following command:\n\nsystemctl daemon-reload && systemctl restart kubelet", "iacontrols": null, "id": "V-242392", "ruleID": "SV-242392r1069461_rule", "severity": "high", "title": "The Kubernetes kubelet must enable explicit authorization.", "version": "TBD"}, "V-242393": {"checkid": "C-45668r712533_chk", "checktext": "Log in to each worker node. Verify that the sshd service is not running. To validate that the service is not running, run the command:\n\nsystemctl status sshd\n\nIf the service sshd is active (running), this is a finding.\n\nNote: If console access is not available, SSH access can be attempted. If the worker nodes cannot be reached, this requirement is \"not a finding\".", "description": "Worker Nodes are maintained and monitored by the Control Plane. Direct access and manipulation of the nodes should not take place by administrators. Worker nodes should be treated as immutable and updated via replacement rather than in-place upgrades.", "fixid": "F-45626r863782_fix", "fixtext": "To stop the sshd service, run the command:\n\nsystemctl stop sshd\n\nNote: If access to the worker node is through an SSH session, it is important to realize there are two requirements for disabling and stopping the sshd service and they should be done during the same SSH session. Disabling the service must be performed first and then the service stopped to guarantee both settings can be made if the session is interrupted.", "iacontrols": null, "id": "V-242393", "ruleID": "SV-242393r960792_rule", "severity": "medium", "title": "Kubernetes Worker Nodes must not have sshd service running.", "version": "TBD"}, "V-242394": {"checkid": "C-45669r712536_chk", "checktext": "Log in to each worker node. Verify that the sshd service is not enabled. To validate the service is not enabled, run the command:\n\nsystemctl is-enabled sshd.service\n\nIf the service sshd is enabled, this is a finding.\n\nNote: If console access is not available, SSH access can be attempted. If the worker nodes cannot be reached, this requirement is \"not a finding\".", "description": "Worker Nodes are maintained and monitored by the Control Plane. Direct access and manipulation of the nodes must not take place by administrators. Worker nodes must be treated as immutable and updated via replacement rather than in-place upgrades.", "fixid": "F-45627r863784_fix", "fixtext": "To disable the sshd service, run the command:\n\nchkconfig sshd off\n\nNote: If access to the worker node is through an SSH session, it is important to realize there are two requirements for disabling and stopping the sshd service that must be done during the same SSH session. Disabling the service must be performed first and then the service stopped to guarantee both settings can be made if the session is interrupted.", "iacontrols": null, "id": "V-242394", "ruleID": "SV-242394r960792_rule", "severity": "medium", "title": "Kubernetes Worker Nodes must not have the sshd service enabled.", "version": "TBD"}, "V-242395": {"checkid": "C-45670r863786_chk", "checktext": "From the Control Plane, run the command:\n\nkubectl get pods --all-namespaces -l k8s-app=kubernetes-dashboard\n\nIf any resources are returned, this is a finding.", "description": "While the Kubernetes dashboard is not inherently insecure on its own, it is often coupled with a misconfiguration of Role-Based Access control (RBAC) permissions that can unintentionally over-grant access. It is not commonly protected with \"NetworkPolicies\", preventing all pods from being able to reach it. In increasingly rare circumstances, the Kubernetes dashboard is exposed publicly to the internet.", "fixid": "F-45628r712540_fix", "fixtext": "Delete the Kubernetes dashboard deployment with the following command:\n\nkubectl delete deployment kubernetes-dashboard --namespace=kube-system", "iacontrols": null, "id": "V-242395", "ruleID": "SV-242395r960792_rule", "severity": "medium", "title": "Kubernetes dashboard must not be enabled.", "version": "TBD"}, "V-242396": {"checkid": "C-45671r863788_chk", "checktext": "From the Control Plane and each Worker node, check the version of kubectl by executing the command:\n\nkubectl version --client\n\nIf the Control Plane or any Worker nodes are not using kubectl version 1.12.9 or newer, this is a finding.", "description": "One of the tools heavily used to interact with containers in the Kubernetes cluster is kubectl. The command is the tool System Administrators used to create, modify, and delete resources. One of the capabilities of the tool is to copy files to and from running containers (i.e., kubectl cp). The command uses the \"tar\" command of the container to copy files from the container to the host executing the \"kubectl cp\" command. If the \"tar\" command on the container has been replaced by a malicious user, the command can copy files anywhere on the host machine. This flaw has been fixed in later versions of the tool. It is recommended to use kubectl versions newer than 1.12.9.", "fixid": "F-45629r863789_fix", "fixtext": "Upgrade the Control Plane and Worker nodes to the latest version of kubectl.", "iacontrols": null, "id": "V-242396", "ruleID": "SV-242396r960792_rule", "severity": "medium", "title": "Kubernetes Kubectl cp command must give expected access and results.", "version": "TBD"}, "V-242397": {"checkid": "C-45672r1069462_chk", "checktext": "If staticPodPath is missing in the Kubelet config and in the systemd arguments, the node does not support static pods.\n\n1. To find the staticPodPath setting on Kubernetes worker nodes, follow these steps:\n\n a. On the Worker nodes, run the command:\n     ps -ef | grep kubelet\n\nb. Note the path to the Kubelet configuration file (identified by --config).\n    (ls /var/lib/kubelet/config.yaml is the common location.)\n\nc. Run the command:\n    grep -i staticPodPath <path_to_config_file>\n\nIf any of the Worker nodes return a value for \"staticPodPath\", this is a finding.\n\nIf staticPodPath is not in the config file, check if it is set as a command-line argument.\n\n2. Check Kubelet Systemd Service Arguments.\n\na. Run the following command to check the Kubelet service:\n    sudo systemctl cat kubelet | grep pod-manifest-path\n\nIf there is no output, staticPodPath is not set in systemd arguments.\n\nIf there is any return, this is a finding.\n\n(Example Return:ExecStart=/usr/bin/kubelet --pod-manifest-path=/etc/kubernetes/manifests\nThis means static pods are defined in /etc/kubernetes/manifests.)", "description": "Allowing kubelet to set a staticPodPath gives containers with root access permissions to traverse the hosting filesystem. The danger comes when the container can create a manifest file within the /etc/kubernetes/manifests directory. When a manifest is created within this directory, containers are entirely governed by the Kubelet not the API Server. The container is not susceptible to admission control at all. Any containers or pods instantiated in this manner are called \"static pods\" and are meant to be used for pods such as the API server, scheduler, controller, etc., not workload pods that need to be governed by the API Server.", "fixid": "F-45630r1069463_fix", "fixtext": "1. Remove staticPodPath setting on Kubernetes worker nodes:\n\na. On each Worker node, run the command:\n    ps -ef | grep kubelet\n\nb. Note the path to the config file (identified by --config).\n\nc. Edit the Kubernetes kubelet file in the --config directory on the Worker nodes. Remove the setting \"staticPodPath\".\n\nd. Restart the kubelet service using the following command:\n    systemctl daemon-reload && systemctl restart kubelet\n\n2. Remove Kubelet Systemd Service Arguments:\n\na. Modify the systemd Service File. Run the command:\n    sudo systemctl edit --full kubelet\n\n(Example Return:ExecStart=/usr/bin/kubelet --pod-manifest-path=/etc/kubernetes/manifests)\n\nb. Find and remove --pod-manifest-path.\n\nc. Save and exit the editor.\n\nd. Restart the kubelet service using the following command:\n    systemctl daemon-reload && systemctl restart kubelet", "iacontrols": null, "id": "V-242397", "ruleID": "SV-242397r1069464_rule", "severity": "high", "title": "The Kubernetes kubelet staticPodPath must not enable static pods.", "version": "TBD"}, "V-242398": {"checkid": "C-45673r918159_chk", "checktext": "On the Control Plane, change to the manifests' directory at /etc/kubernetes/manifests and run the command:\ngrep -i feature-gates *\n\nReview the feature-gates setting, if one is returned.\n\nIf the feature-gates setting is available and contains the DynamicAuditing flag set to \"true\", this is a finding.\n\nOn each Control Plane and Worker node, run the command:\nps -ef | grep kubelet\n\nIf the \"--feature-gates\" option exists, this is a finding. \n\nNote the path to the config file (identified by: --config).\n\nInspect the content of the config file:\nIf the \"featureGates\" setting is present and has the \"DynamicAuditing\" flag set to \"true\", this is a finding.", "description": "Protecting the audit data from change or deletion is important when an attack occurs. One way an attacker can cover their tracks is to change or delete audit records. This will either make the attack unnoticeable or make it more difficult to investigate how the attack took place and what changes were made. The audit data can be protected through audit log file protections and user authorization.\n\nOne way for an attacker to thwart these measures is to send the audit logs to another source and filter the audited results before sending them on to the original target. This can be done in Kubernetes through the configuration of dynamic audit webhooks through the DynamicAuditing flag.", "fixid": "F-45631r918160_fix", "fixtext": "On the Control Plane, change to the manifests' directory at /etc/kubernetes/manifests and run the command:\ngrep -i feature-gates *\n\nIf any \"--feature-gates\" setting is available and contains the \"DynamicAuditing\" flag, remove the flag or set it to false.\n\nOn the each Control Plane and Worker Node, run the command:\nps -ef | grep kubelet\n\nRemove the \"--feature-gates option\" if present.\n\nNote the path to the config file (identified by: --config).\n\nEdit the Kubernetes Kubelet config file: \nIf the \"featureGates\" setting is present, remove the \"DynamicAuditing\" flag or set the flag to false.\n\nRestart the kubelet service using the following command:\nservice kubelet restart", "iacontrols": null, "id": "V-242398", "ruleID": "SV-242398r960792_rule", "severity": "medium", "title": "Kubernetes DynamicAuditing must not be enabled.", "version": "TBD"}, "V-242399": {"checkid": "C-45674r918162_chk", "checktext": "This check is only applicable for Kubernetes versions 1.25 and older.  \n\nOn the Control Plane, change to the manifests' directory at /etc/kubernetes/manifests and run the command:\ngrep -i feature-gates *\n\nIn each manifest file, if the feature-gates does not exist, or does not contain the \"DynamicKubeletConfig\" flag, or sets the flag to \"true\", this is a finding.\n\nOn each Control Plane and Worker node, run the command:\nps -ef | grep kubelet\n\nVerify the \"feature-gates\" option is not present.\n\nNote the path to the config file (identified by --config).\n\nInspect the content of the config file:\nIf the \"featureGates\" setting is not present, or does not contain the \"DynamicKubeletConfig\", or sets the flag to \"true\", this is a finding.", "description": "Kubernetes allows a user to configure kubelets with dynamic configurations. When dynamic configuration is used, the kubelet will watch for changes to the configuration file. When changes are made, the kubelet will automatically restart. Allowing this capability bypasses access restrictions and authorizations. Using this capability, an attacker can lower the security posture of the kubelet, which includes allowing the ability to run arbitrary commands in any container running on that node.", "fixid": "F-45632r918163_fix", "fixtext": "This fix is only applicable to Kubernetes version 1.25 and older.\n\nOn the Control Plane, change to the manifests' directory at /etc/kubernetes/manifests and run the command:\ngrep -i feature-gates *\n\nEdit the manifest files so that every manifest has a \"--feature-gates\" setting with \"DynamicKubeletConfig=false\".\n\nOn each Control Plane and Worker Node, run the command:\nps -ef | grep kubelet\n\nRemove the \"feature-gates\" option if present.\n\nNote the path to the config file (identified by --config).\n\nEdit the config file: \nAdd a \"featureGates\" setting if one does not yet exist. Add the feature gate \"DynamicKubeletConfig=false\".\n\nRestart the kubelet service using the following command:\nsystemctl daemon-reload && systemctl restart kubelet", "iacontrols": null, "id": "V-242399", "ruleID": "SV-242399r960792_rule", "severity": "medium", "title": "Kubernetes DynamicKubeletConfig must not be enabled.", "version": "TBD"}, "V-242400": {"checkid": "C-45675r927094_chk", "checktext": "On the Control Plane, change to the manifests' directory at /etc/kubernetes/manifests and run the command:\ngrep -i feature-gates *\n\nReview the \"--feature-gates\" setting, if one is returned.\n\nIf the \"--feature-gate\"s setting is available and contains the \"AllAlpha\" flag set to \"true\", this is a finding.", "description": "Kubernetes allows alpha API calls within the API server. The alpha features are disabled by default since they are not ready for production and likely to change without notice. These features may also contain security issues that are rectified as the feature matures. To keep the Kubernetes cluster secure and stable, these alpha features must not be used.", "fixid": "F-45633r927095_fix", "fixtext": "Edit any manifest file that contains the \"--feature-gates\" setting with \"AllAlpha\" set to \"true\".\n\nSet the value of \"AllAlpha\" to \"false\" or remove the setting completely. (AllAlpha - default=false)", "iacontrols": null, "id": "V-242400", "ruleID": "SV-242400r960792_rule", "severity": "medium", "title": "The Kubernetes API server must have Alpha APIs disabled.", "version": "TBD"}, "V-242402": {"checkid": "C-45677r927100_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\ngrep -i audit-log-path * \n\nIf the \"--audit-log-path\" is not set, this is a finding.", "description": "When Kubernetes is started, components and user services are started for auditing startup events, and events for components and services, it is important that auditing begin on startup. Within Kubernetes, audit data for all components is generated by the API server. To enable auditing to begin, an audit policy must be defined for the events and the information to be stored with each event. It is also necessary to give a secure location where the audit logs are to be stored. If an audit log path is not specified, all audit data is sent to studio.", "fixid": "F-45635r927101_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--audit-log-path\" to a secure location for the audit logs to be written.\n\nNote: If the API server is running as a Pod, then the manifest will also need to be updated to mount the host system filesystem where the audit log file is to be written.", "iacontrols": null, "id": "V-242402", "ruleID": "SV-242402r960888_rule", "severity": "medium", "title": "The Kubernetes API Server must have an audit log path set.", "version": "TBD"}, "V-242403": {"checkid": "C-45678r863807_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\ngrep -i audit-policy-file \n\nIf the audit-policy-file is not set, this is a finding. \n\nThe file given is the policy file and defines what is audited and what information is included with each event.\n\nThe policy file must look like this:\n\n# Log all requests at the RequestResponse level.\napiVersion: audit.k8s.io/vX (Where X is the latest apiVersion)\nkind: Policy\nrules:\n- level: RequestResponse\n\nIf the audit policy file does not look like above, this is a finding.", "description": "Within Kubernetes, audit data for all components is generated by the API server. This audit data is important when there are issues, to include security incidents that must be investigated. To make the audit data worthwhile for the investigation of events, it is necessary to have the appropriate and required data logged. To fully understand the event, it is important to identify any users associated with the event. \n\nThe API server policy file allows for the following levels of auditing:\n      None - Do not log events that match the rule.\n      Metadata - Log request metadata (requesting user, timestamp, resource, verb, etc.) but not request or response body.\n      Request - Log event metadata and request body but not response body.\n      RequestResponse - Log event metadata, request, and response bodies.\n\nSatisfies: SRGID:SRG-APP-000092-CTR-000165, SRG-APP-000026-CTR-000070, SRG-APP-000027-CTR-000075, SRG-APP-000028-CTR-000080, SRG-APP-000101-CTR-000205, SRG-APP-000100-CTR-000200, SRG-APP-000100-CTR-000195, SRG-APP-000099-CTR-000190, SRG-APP-000098-CTR-000185, SRG-APP-000095-CTR-000170, SRG-APP-000096-CTR-000175, SRG-APP-000097-CTR-000180, SRG-APP-000507-CTR-001295, SRG-APP-000504-CTR-001280, SRG-APP-000503-CTR-001275, SRG-APP-000501-CTR-001265, SRG-APP-000500-CTR-001260, SRG-APP-000497-CTR-001245, SRG-APP-000496-CTR-001240, SRG-APP-000493-CTR-001225, SRG-APP-000492-CTR-001220, SRG-APP-000343-CTR-000780, SRG-APP-000381-CTR-000905", "fixid": "F-45636r927103_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of  \"--audit-policy-file\" to the path of a file with the following content:\n    \n    # Log all requests at the RequestResponse level.\n    apiVersion: audit.k8s.io/vX (Where X is the latest apiVersion)\n    kind: Policy\n    rules:\n    - level: RequestResponse\n\nNote: If the API server is running as a Pod, then the manifest will also need to be updated to mount the host system filesystem where the audit policy file resides.", "iacontrols": null, "id": "V-242403", "ruleID": "SV-242403r986135_rule", "severity": "medium", "title": "Kubernetes API Server must generate audit records that identify what type of event has occurred, identify the source of the event, contain the event results, identify any users, and identify any containers associated with the event.", "version": "TBD"}, "V-242404": {"checkid": "C-45679r918165_chk", "checktext": "On the Control Plane and Worker nodes, run the command:\nps -ef | grep kubelet\n\nIf the option \"--hostname-override\" is present, this is a finding.", "description": "Kubernetes allows for the overriding of hostnames. Allowing this feature to be implemented within the kubelets may break the TLS setup between the kubelet service and the API server. This setting also can make it difficult to associate logs with nodes if security analytics needs to take place. The better practice is to setup nodes with resolvable FQDNs and avoid overriding the hostnames.", "fixid": "F-45637r918166_fix", "fixtext": "Run the command:  \nsystemctl status kubelet.  \nNote the path to the drop-in file.\n\nDetermine the path to the environment file(s) with the command: \ngrep -i EnvironmentFile <path_to_drop_in_file>.\n\nRemove the \"--hostname-override\" option from any environment file where it is present.  \n\nRestart the kubelet service using the following command:\nsystemctl daemon-reload && systemctl restart kubelet", "iacontrols": null, "id": "V-242404", "ruleID": "SV-242404r960960_rule", "severity": "medium", "title": "Kubernetes Kubelet must deny hostname override.", "version": "TBD"}, "V-242405": {"checkid": "C-45680r863812_chk", "checktext": "On the Control Plane, change to the /etc/kubernetes/manifest directory. Run the command:\nls -l *\n\nEach manifest file must be owned by root:root.\n\nIf any manifest file is not owned by root:root, this is a finding.", "description": "The manifest files contain the runtime configuration of the API server, proxy, scheduler, controller, and etcd. If an attacker can gain access to these files, changes can be made to open vulnerabilities and bypass user authorizations inherit within Kubernetes with RBAC implemented.", "fixid": "F-45638r863813_fix", "fixtext": "On the Control Plane, change to the /etc/kubernetes/manifest directory. Run the command:\nchown root:root *\n\nTo verify the change took place, run the command:\nls -l *\n\nAll the manifest files should be owned by root:root.", "iacontrols": null, "id": "V-242405", "ruleID": "SV-242405r960960_rule", "severity": "medium", "title": "The Kubernetes manifests must be owned by root.", "version": "TBD"}, "V-242406": {"checkid": "C-45681r863815_chk", "checktext": "On the Kubernetes Control Plane and Worker nodes, run the command:\nps -ef | grep kubelet\n\nCheck the config file (path identified by: --config):\n\nChange to the directory identified by --config (example /etc/sysconfig/) run the command:\nls -l kubelet\n\nEach kubelet configuration file must be owned by root:root.\n\nIf any manifest file is not owned by root:root, this is a finding.", "description": "The kubelet configuration file contains the runtime configuration of the kubelet service. If an attacker can gain access to this file, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented.", "fixid": "F-45639r863816_fix", "fixtext": "On the Control Plane and Worker nodes, change to the --config directory. Run the command:\nchown root:root kubelet\n\nTo verify the change took place, run the command:\nls -l kubelet\n\nThe kubelet file should now be owned by root:root.", "iacontrols": null, "id": "V-242406", "ruleID": "SV-242406r960960_rule", "severity": "medium", "title": "The Kubernetes KubeletConfiguration file must be owned by root.", "version": "TBD"}, "V-242407": {"checkid": "C-45682r918169_chk", "checktext": " On the Kubernetes Control Plane and Worker nodes, run the command:\nps -ef | grep kubelet\n\nCheck the config file (path identified by: --config):\n\nChange to the directory identified by --config (example /etc/sysconfig/) and run the command:\nls -l kubelet\n\nEach KubeletConfiguration file must have permissions of \"644\" or more restrictive.\n\nIf any KubeletConfiguration file is less restrictive than \"644\", this is a finding.", "description": "The kubelet configuration file contains the runtime configuration of the kubelet service. If an attacker can gain access to this file, changes can be made to open vulnerabilities and bypass user authorizations inherit within Kubernetes with RBAC implemented.", "fixid": "F-45640r918170_fix", "fixtext": "On the Kubernetes Control Plane and Worker nodes, run the command:\nps -ef | grep kubelet\n\nCheck the config file (path identified by: --config):\n\nChange to the directory identified by --config (example /etc/sysconfig/) and run the command:\nchmod 644 kubelet\n\nTo verify the change took place, run the command:\nls -l kubelet\n\nThe kubelet file should now have the permissions of \"644\".", "iacontrols": null, "id": "V-242407", "ruleID": "SV-242407r960960_rule", "severity": "medium", "title": "The Kubernetes KubeletConfiguration files must have file permissions set to 644 or more restrictive.", "version": "TBD"}, "V-242408": {"checkid": "C-45683r918172_chk", "checktext": "On both Control Plane and Worker Nodes, change to the /etc/kubernetes/manifest directory. Run the command:\nls -l *\n\nEach manifest file must have permissions \"644\" or more restrictive.\n\nIf any manifest file is less restrictive than \"644\", this is a finding.", "description": "The manifest files contain the runtime configuration of the API server, scheduler, controller, and etcd. If an attacker can gain access to these files, changes can be made to open vulnerabilities and bypass user authorizations inherent within Kubernetes with RBAC implemented.\n\nSatisfies: SRG-APP-000133-CTR-000310, SRG-APP-000133-CTR-000295, SRG-APP-000516-CTR-001335", "fixid": "F-45641r918173_fix", "fixtext": "On both Control Plane and Worker Nodes, change to the /etc/kubernetes/manifest directory. Run the command:\nchmod 644 *\n\nTo verify the change took place, run the command:\nls -l *\n\nAll the manifest files should now have privileges of \"644\".", "iacontrols": null, "id": "V-242408", "ruleID": "SV-242408r960960_rule", "severity": "medium", "title": "The Kubernetes manifest files must have least privileges.", "version": "TBD"}, "V-242409": {"checkid": "C-45684r863824_chk", "checktext": "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i profiling * \n\nIf the setting \"profiling\" is not configured in the Kubernetes Controller Manager manifest file or it is set to \"True\", this is a finding.", "description": "Kubernetes profiling provides the ability to analyze and troubleshoot Controller Manager events over a web interface on a host port. Enabling this service can expose details about the Kubernetes architecture. This service must not be enabled unless deemed necessary.", "fixid": "F-45642r863825_fix", "fixtext": "Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument \"--profiling value\" to \"false\".", "iacontrols": null, "id": "V-242409", "ruleID": "SV-242409r960963_rule", "severity": "medium", "title": "Kubernetes Controller Manager must disable profiling.", "version": "TBD"}, "V-242410": {"checkid": "C-45685r1007472_chk", "checktext": "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep kube-apiserver.manifest -I -secure-port *\ngrep kube-apiserver.manifest -I -etcd-servers *\n-edit manifest file:\nVIM <Manifest Name>\nReview livenessProbe:\nHttpGet:\nPort:\nReview ports:\n- containerPort:\nhostPort:\n- containerPort:\nhostPort:\n\nRun command: \nkubectl describe services --all-namespaces \nSearch labels for any apiserver namespaces.\nPort:\n\nAny manifest and namespace PPS or services configuration not in compliance with PPSM CAL is a finding.\n\nReview the information systems documentation and interview the team, gain an understanding of the API Server architecture, and determine applicable PPS. If there are any PPS in the system documentation not in compliance with the CAL PPSM, this is a finding. Any PPS not set in the system documentation is a finding.\n\nReview findings against the most recent PPSM CAL:\nhttps://cyber.mil/ppsm/cal/\n\nVerify API Server network boundary with the PPS associated with the CAL Assurance Categories. Any PPS not in compliance with the CAL Assurance Category requirements is a finding.", "description": "Kubernetes API Server PPSs must be controlled and conform to the PPSM CAL. Those PPS that fall outside the PPSM CAL must be blocked. Instructions on the PPSM can be found in DoD Instruction 8551.01 Policy.", "fixid": "F-45643r1007473_fix", "fixtext": "Amend any system documentation requiring revision to comply with PPSM CAL. \n\nUpdate Kubernetes API Server manifest and namespace PPS configuration to comply with PPSM CAL.", "iacontrols": null, "id": "V-242410", "ruleID": "SV-242410r1043177_rule", "severity": "medium", "title": "The Kubernetes API Server must enforce ports, protocols, and services (PPS) that adhere to the Ports, Protocols, and Services Management Category Assurance List (PPSM CAL).", "version": "TBD"}, "V-242411": {"checkid": "C-45686r1007475_chk", "checktext": "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep kube-scheduler.manifest -I -insecure-port\n                grep kube-scheduler.manifest -I -secure-port\n-edit manifest file:\nVIM <Manifest Name>\nReview  livenessProbe:\nHttpGet:\nPort:\nReview ports:\n- containerPort:\n       hostPort:\n- containerPort:\n       hostPort:\n\nRun Command: \nkubectl describe services --all-namespaces \nSearch labels for any scheduler namespaces.\nPort:\n\nAny manifest and namespace PPS configuration not in compliance with PPSM CAL is a finding.\n\nReview the information systems documentation and interview the team, gain an understanding of the Scheduler architecture, and determine applicable PPS. Any PPS in the system documentation not in compliance with the CAL PPSM is a finding. Any PPSs not set in the system documentation is a finding.\n\nReview findings against the most recent PPSM CAL:\nhttps://cyber.mil/ppsm/cal/\n\nVerify Scheduler network boundary with the PPS associated with the CAL Assurance Categories. Any PPS not in compliance with the CAL Assurance Category requirements is a finding.", "description": "Kubernetes Scheduler PPS must be controlled and conform to the PPSM CAL. Those ports, protocols, and services that fall outside the PPSM CAL must be blocked. Instructions on the PPSM can be found in DoD Instruction 8551.01 Policy.", "fixid": "F-45644r1007476_fix", "fixtext": "Amend any system documentation requiring revision to comply with the PPSM CAL. \n\nUpdate Kubernetes Scheduler manifest and namespace PPS configuration to comply with the PPSM CAL.", "iacontrols": null, "id": "V-242411", "ruleID": "SV-242411r1043177_rule", "severity": "medium", "title": "The Kubernetes Scheduler must enforce ports, protocols, and services (PPS) that adhere to the Ports, Protocols, and Services Management Category Assurance List (PPSM CAL).", "version": "TBD"}, "V-242412": {"checkid": "C-45687r1007478_chk", "checktext": "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command: \ngrep kube-conntroller-manager.manifest -I -secure-port\n\n-Review manifest file by executing the following:\nVIM <Manifest Name>:\nReview  livenessProbe:\nHttpGet:\nPort:\nReview ports:\n- containerPort:\n       hostPort:\n- containerPort:\n       hostPort:\n\nRun Command: \nkubectl describe services --all-namespaces \nSearch labels for any controller namespaces.\n\nAny manifest and namespace PPS or services configuration not in compliance with PPSM CAL is a finding.\n\nReview the information systems documentation and interview the team, gain an understanding of the Controller architecture, and determine applicable PPS. Any PPS in the system documentation not in compliance with the CAL PPSM is a finding. Any PPS not set in the system documentation is a finding.\n\nReview findings against the most recent PPSM CAL:\nhttps://cyber.mil/ppsm/cal/\n\nVerify Controller network boundary with the PPS associated with the Controller for Assurance Categories. Any PPS not in compliance with the CAL Assurance Category requirements is a finding.", "description": "Kubernetes Controller ports, protocols, and services must be controlled and conform to the PPSM CAL. Those PPS that fall outside the PPSM CAL must be blocked. Instructions on the PPSM can be found in DoD Instruction 8551.01 Policy.", "fixid": "F-45645r1007479_fix", "fixtext": "Amend any system documentation requiring revision to comply with the PPSM CAL. \n\nUpdate Kubernetes Controller manifest and namespace PPS configuration to comply with PPSM CAL.", "iacontrols": null, "id": "V-242412", "ruleID": "SV-242412r1043177_rule", "severity": "medium", "title": "The Kubernetes Controllers must enforce ports, protocols, and services (PPS) that adhere to the Ports, Protocols, and Services Management Category Assurance List (PPSM CAL).", "version": "TBD"}, "V-242413": {"checkid": "C-45688r863833_chk", "checktext": "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep kube-apiserver.manifest -I -etcd-servers *\n-edit etcd-main.manifest file:\nVIM <Manifest Name:\nReview  livenessProbe:\nHttpGet:\nPort:\nReview ports:\n- containerPort:\n       hostPort:\n- containerPort:\n       hostPort:\nRun Command:\nkubectl describe services –all-namespace\nSearch labels for any apiserver names spaces.\nPort:\n\nAny manifest and namespace PPS configuration not in compliance with PPSM CAL is a finding.\n\nReview the information systems documentation and interview the team, gain an understanding of the etcd architecture, and determine applicable PPS. Any PPS in the system documentation not in compliance with the CAL PPSM is a finding. Any PPS not set in the system documentation is a finding.\n\nReview findings against the most recent PPSM CAL:\nhttps://cyber.mil/ppsm/cal/\n\nVerify etcd network boundary with the PPS associated with the CAL Assurance Categories. Any PPS not in compliance with the CAL Assurance Category requirements is a finding.", "description": "Kubernetes etcd PPS must be controlled and conform to the PPSM CAL. Those PPS that fall outside the PPSM CAL must be blocked. Instructions on the PPSM can be found in DoD Instruction 8551.01 Policy.", "fixid": "F-45646r712594_fix", "fixtext": "Amend any system documentation requiring revision. Update Kubernetes etcd manifest and namespace PPS configuration to comply with PPSM CAL.", "iacontrols": null, "id": "V-242413", "ruleID": "SV-242413r1043177_rule", "severity": "medium", "title": "The Kubernetes etcd must enforce ports, protocols, and services (PPS) that adhere to the Ports, Protocols, and Services Management Category Assurance List (PPSM CAL).", "version": "TBD"}, "V-242414": {"checkid": "C-45689r863835_chk", "checktext": "On the Control Plane, run the command:\nkube<PERSON>l get pods --all-namespaces\n\nThe list returned is all pods running within the Kubernetes cluster. For those pods running within the user namespaces (System namespaces are kube-system, kube-node-lease and kube-public), run the command:\nkubectl get pod podname -o yaml | grep -i port\n\nNote: In the above command, \"podname\" is the name of the pod. For the command to work correctly, the current context must be changed to the namespace for the pod. The command to do this is:\n\nkubectl config set-context --current --namespace=namespace-name\n(Note: \"namespace-name\" is the name of the namespace.)\n\nReview the ports that are returned for the pod.\n\nIf any host-privileged ports are returned for any of the pods, this is a finding.", "description": "Privileged ports are those ports below 1024 and that require system privileges for their use. If containers can use these ports, the container must be run as a privileged user. Kubernetes must stop containers that try to map to these ports directly. Allowing non-privileged ports to be mapped to the container-privileged port is the allowable method when a certain port is needed. An example is mapping port 8080 externally to port 80 in the container.", "fixid": "F-45647r717032_fix", "fixtext": "For any of the pods that are using host-privileged ports, reconfigure the pod to use a service to map a host non-privileged port to the pod port or reconfigure the image to use non-privileged ports.", "iacontrols": null, "id": "V-242414", "ruleID": "SV-242414r1043177_rule", "severity": "medium", "title": "The Kubernetes cluster must use non-privileged host ports for user pods.", "version": "TBD"}, "V-242415": {"checkid": "C-45690r1069465_chk", "checktext": "Follow these steps to check, from the Kubernetes control plane, if secrets are stored as environment variables.\n\n1. Find All Pods Using Secrets in Environment Variables.\n\nTo list all pods using secrets as environment variables, execute:\n\nkubectl get pods --all-namespaces -o yaml | grep -A5 \"secretKeyRef\"\n\nIf any of the values returned reference environment variables, this is a finding.\n\n2. Check Environment Variables in a Specific Pod.\n\nTo check if a specific pod is using secrets as environment variables, execute:\n\nkubectl get pods -n <namespace>\n(Replace <namespace> with the actual namespace, or omit -n <namespace> to check in the default namespace.)\nkubectl describe pod <pod-name> -n <namespace> | grep -A5 \"Environment:\"\n\nIf secrets are used, output like the following will be displayed:\n\nEnvironment:\n  SECRET_USERNAME:   <set from secret: my-secret key: username>\n  SECRET_PASSWORD:   <set from secret: my-secret key: password>\n\nIf the output is similar to this, the pod is using Kubernetes secrets as environment variables, and this is a finding.\n\n3. Check the Pod YAML for Secret Usage.\n\nTo check the full YAML definition for environment variables, execute:\n\nkubectl get pod <pod-name> -n <namespace> -o yaml | grep -A5 \"env:\"\n\nExample output:\nyaml\nCopyEdit\nenv:\n  - name: SECRET_USERNAME\n    valueFrom:\n      secretKeyRef:\n        name: my-secret\n        key: username\n\nThis means the pod is pulling the secret named my-secret and setting SECRET_USERNAME from its username key.\n\nIf the pod is pulling a secret and setting an environment variable in the \"env:\", this is a finding.\n\n4. Check Secrets in a Deployment, StatefulSet, or DaemonSet.\n\nIf the pod is managed by a Deployment, StatefulSet, or DaemonSet, check their configurations:\n\nkubectl get deployment <deployment-name> -n <namespace> -o yaml | grep -A5 \"env:\"\n\nor\n\nFor all Deployments in all namespaces:\n\nkubectl get deployments --all-namespaces -o yaml | grep -A5 \"env:\"\n\nIf the pod is pulling a secret and setting an environment variable in the \"env:\", this is a finding.\n\n5. Check Environment Variables Inside a Running Pod.\n\nIf needed, check the environment variables inside a running pod:\n\nkubectl exec -it <pod-name> -n <namespace> -- env | grep SECRET\n\nIf any of the values returned reference environment variables, this is a finding.", "description": "Secrets, such as passwords, keys, tokens, and certificates must not be stored as environment variables. These environment variables are accessible inside Kubernetes by the \"Get Pod\" API call, and by any system, such as CI/CD pipeline, which has access to the definition file of the container. Secrets must be mounted from files or stored within password vaults.", "fixid": "F-45648r712600_fix", "fixtext": "Any secrets stored as environment variables must be moved to the secret files with the proper protections and enforcements or placed within a password vault.", "iacontrols": null, "id": "V-242415", "ruleID": "SV-242415r1069466_rule", "severity": "high", "title": "Secrets in Kubernetes must not be stored as environment variables.", "version": "TBD"}, "V-242417": {"checkid": "C-45692r863840_chk", "checktext": "On the Control Plane, run the command:\nkubectl get pods --all-namespaces\n\nReview the namespaces and pods that are returned. Kubernetes system namespaces are kube-node-lease, kube-public, and kube-system.\n\nIf any user pods are present in the Kubernetes system namespaces, this is a finding.", "description": "Separating user functionality from management functionality is a requirement for all the components within the Kubernetes Control Plane. Without the separation, users may have access to management functions that can degrade the Kubernetes architecture and the services being offered, and can offer a method to bypass testing and validation of functions before introduced into a production environment.", "fixid": "F-45650r712606_fix", "fixtext": "Move any user pods that are present in the Kubernetes system namespaces to user specific namespaces.", "iacontrols": null, "id": "V-242417", "ruleID": "SV-242417r961095_rule", "severity": "medium", "title": "Kubernetes must separate user functionality.", "version": "TBD"}, "V-242418": {"checkid": "C-45693r863842_chk", "checktext": "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i tls-cipher-suites *\n\nIf the setting feature tls-cipher-suites is not set in the Kubernetes API server manifest file or contains no value or does not contain TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384, this is a finding.", "description": "The Kubernetes API server communicates to the kubelet service on the nodes to deploy, update, and delete resources. If an attacker were able to get between this communication and modify the request, the Kubernetes cluster could be compromised. Using approved cypher suites for the communication ensures the protection of the transmitted information, confidentiality, and integrity so that the attacker cannot read or alter this communication.", "fixid": "F-45651r927105_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--tls-cipher-suites\" to:\n\"TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384\"", "iacontrols": null, "id": "V-242418", "ruleID": "SV-242418r1043178_rule", "severity": "medium", "title": "The Kubernetes API server must use approved cipher suites.", "version": "TBD"}, "V-242419": {"checkid": "C-45694r863845_chk", "checktext": "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i client-ca-file *\n\nIf the setting feature client-ca-file is not set in the Kubernetes API server manifest file or contains no value, this is a finding.", "description": "Kubernetes control plane and external communication are managed by API Server. The main implementation of the API Server is to manage hardware resources for pods and containers using horizontal or vertical scaling. Anyone who can access the API Server can effectively control the Kubernetes architecture. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols such as TLS. TLS provides the Kubernetes API Server with a means to authenticate sessions and encrypt traffic. \n\nTo enable encrypted communication for API Server, the parameter client-ca-file must be set. This parameter gives the location of the SSL Certificate Authority file used to secure API Server communication.", "fixid": "F-45652r918175_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--client-ca-file\" to path containing Approved Organizational Certificate.", "iacontrols": null, "id": "V-242419", "ruleID": "SV-242419r1043178_rule", "severity": "medium", "title": "Kubernetes API Server must have the SSL Certificate Authority set.", "version": "TBD"}, "V-242420": {"checkid": "C-45695r918177_chk", "checktext": "On the Control Plane, run the command:\nps -ef | grep kubelet\n\nIf the \"--client-ca-file\" option exists, this is a finding.\n\nNote the path to the config file (identified by --config).\n\nRun the command:\ngrep -i clientCAFile <path_to_config_file>\n\nIf the setting \"clientCAFile\" is not set or contains no value, this is a finding.", "description": "Kubernetes container and pod configuration are maintained by Kubelet. Kubelet agents register nodes with the API Server, mount volume storage, and perform health checks for containers and pods. Anyone who gains access to Kubelet agents can effectively control applications within the pods and containers. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols such as TLS. TLS provides the Kubernetes API Server with a means to authenticate sessions and encrypt traffic.\n\nTo enable encrypted communication for Kubelet, the clientCAFile must be set. This parameter gives the location of the SSL Certificate Authority file used to secure Kubelet communication.", "fixid": "F-45653r918178_fix", "fixtext": "On the Control Plane, run the command:\nps -ef | grep kubelet\n\nRemove the \"--client-ca-file\" option if present.\n\nNote the path to the config file (identified by --config).\n\nEdit the Kubernetes Kubelet config file: \nSet the value of \"clientCAFile\" to a path containing an Approved Organizational Certificate. \n\nRestart the kubelet service using the following command:\nsystemctl daemon-reload && systemctl restart kubelet", "iacontrols": null, "id": "V-242420", "ruleID": "SV-242420r1043178_rule", "severity": "medium", "title": "Kubernetes Kubelet must have the SSL Certificate Authority set.", "version": "TBD"}, "V-242421": {"checkid": "C-45696r927107_chk", "checktext": "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i root-ca-file *\n\nIf the setting \"--root-ca-file\" is not set in the Kubernetes Controller Manager manifest file or contains no value, this is a finding.", "description": "The Kubernetes Controller Manager is responsible for creating service accounts and tokens for the API Server, maintaining the correct number of pods for every replication controller and provides notifications when nodes are offline.  \n\nAnyone who gains access to the Controller Manager can generate backdoor accounts, take possession of, or diminish system performance without detection by disabling system notification. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes Controller Manager with a means to be able to authenticate sessions and encrypt traffic.", "fixid": "F-45654r927108_fix", "fixtext": "Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--root-ca-file\" to path containing Approved Organizational Certificate.", "iacontrols": null, "id": "V-242421", "ruleID": "SV-242421r1043178_rule", "severity": "medium", "title": "Kubernetes Controller Manager must have the SSL Certificate Authority set.", "version": "TBD"}, "V-242422": {"checkid": "C-45697r863854_chk", "checktext": "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i tls-cert-file *\ngrep -i tls-private-key-file *\n\nIf the setting tls-cert-file and private-key-file is not set in the Kubernetes API server manifest file or contains no value, this is a finding.", "description": "Kubernetes control plane and external communication is managed by API Server. The main implementation of the API Server is to manage hardware resources for pods and container using horizontal or vertical scaling. Anyone who can access the API Server can effectively control the Kubernetes architecture. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server with a means to be able to authenticate sessions and encrypt traffic. \n\nTo enable encrypted communication for API Server, the parameter etcd-cafile must be set. This parameter gives the location of the SSL Certificate Authority file used to secure API Server communication.", "fixid": "F-45655r863855_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of tls-cert-file and tls-private-key-file to path containing Approved Organizational Certificate.", "iacontrols": null, "id": "V-242422", "ruleID": "SV-242422r1043178_rule", "severity": "medium", "title": "Kubernetes API Server must have a certificate for communication.", "version": "TBD"}, "V-242423": {"checkid": "C-45698r863857_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i client-cert-auth * \n\nIf the setting client-cert-auth is not configured in the Kubernetes etcd manifest file or set to \"false\", this is a finding.", "description": "Kubernetes container and pod configuration are maintained by Kubelet. Kubelet agents register nodes with the API Server, mount volume storage, and perform health checks for containers and pods. Anyone who gains access to Kubelet agents can effectively control applications within the pods and containers. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server with a means to be able to authenticate sessions and encrypt traffic.\n\nTo enable encrypted communication for Kubelet, the parameter client-cert-auth must be set. This parameter gives the location of the SSL Certificate Authority file used to secure Kubelet communication.", "fixid": "F-45656r863858_fix", "fixtext": "Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--client-cert-auth\" to \"true\" for the etcd.", "iacontrols": null, "id": "V-242423", "ruleID": "SV-242423r1043178_rule", "severity": "medium", "title": "Kubernetes etcd must enable client authentication to secure service.", "version": "TBD"}, "V-242424": {"checkid": "C-45699r918180_chk", "checktext": "On the Control Plane, run the command:\nps -ef | grep kubelet\n\nIf the \"--tls-private-key-file\" option exists, this is a finding. \n\nNote the path to the config file (identified by --config).\n\nRun the command:\ngrep -i tlsPrivateKeyFile <path_to_config_file>\n\nIf the setting \"tlsPrivateKeyFile\" is not set or contains no value, this is a finding.", "description": "Kubernetes container and pod configuration are maintained by Kubelet. Kubelet agents register nodes with the API Server, mount volume storage, and perform health checks for containers and pods. Anyone who gains access to Kubelet agents can effectively control applications within the pods and containers. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols such as TLS. TLS provides the Kubernetes API Server with a means to authenticate sessions and encrypt traffic.\n\nTo enable encrypted communication for Kubelet, the tlsPrivateKeyFile must be set. This parameter gives the location of the SSL Certificate Authority file used to secure Kubelet communication.", "fixid": "F-45657r918181_fix", "fixtext": "On the Control Plane, run the command:\nps -ef | grep kubelet\n\nRemove the \"--tls-private-key-file\" option if present.\n\nNote the path to the config file (identified by --config).\n\nEdit the Kubernetes Kubelet config file: \nSet \"tlsPrivateKeyFile\" to  a path containing the appropriate private key. \n\nRestart the kubelet service using the following command:\nsystemctl daemon-reload && systemctl restart kubelet", "iacontrols": null, "id": "V-242424", "ruleID": "SV-242424r1043178_rule", "severity": "medium", "title": "Kubernetes Kubelet must enable tlsPrivateKeyFile for client authentication to secure service.", "version": "TBD"}, "V-242425": {"checkid": "C-45700r918183_chk", "checktext": "On the Control Plane, run the command:\nps -ef | grep kubelet\n\nIf the argument for \"--tls-cert-file\" option exists, this is a finding. \n\nNote the path to the config file (identified by --config).\n\nRun the command:\ngrep -i tlsCertFile <path_to_config_file>\n\nIf the setting \"tlsCertFile\" is not set or contains no value, this is a finding.", "description": "Kubernetes container and pod configuration are maintained by Kubelet. Kubelet agents register nodes with the API Server, mount volume storage, and perform health checks for containers and pods. Anyone who gains access to Kubelet agents can effectively control applications within the pods and containers. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols such as TLS. TLS provides the Kubernetes API Server with a means to authenticate sessions and encrypt traffic.\n\nTo enable encrypted communication for Kubelet, the parameter tlsCertFile must be set. This parameter gives the location of the SSL Certificate Authority file used to secure Kubelet communication.", "fixid": "F-45658r918184_fix", "fixtext": "On the Control Plane, run the command:\nps -ef | grep kubelet\n\nRemove the \"--tls-cert-file\" option if present.\n\nNote the path to the config file (identified by --config).\n\nEdit the Kubernetes Kubelet config file: \nSet \"tlsCertFile\" to a path containing an Approved Organization Certificate. \n\nRestart the kubelet service using the following command:\nsystemctl daemon-reload && systemctl restart kubelet", "iacontrols": null, "id": "V-242425", "ruleID": "SV-242425r1043178_rule", "severity": "medium", "title": "Kubernetes Kubelet must enable tlsCertFile for client authentication to secure service.", "version": "TBD"}, "V-242426": {"checkid": "C-45701r927110_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\ngrep -i peer-client-cert-auth * \n\nIf the setting \"--peer-client-cert-auth\" is not configured in the Kubernetes etcd manifest file or set to \"false\", this is a finding.", "description": "Kubernetes container and pod configuration are maintained by Kubelet. Kubelet agents register nodes with the API Server, mount volume storage, and perform health checks for containers and pods. Anyone who gains access to Kubelet agents can effectively control applications within the pods and containers. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server with a means to be able to authenticate sessions and encrypt traffic.\n\nEtcd is a highly-available key value store used by Kubernetes deployments for persistent storage of all of its REST API objects. These objects are sensitive and should be accessible only by authenticated etcd peers in the etcd cluster. The parameter \"--peer-client-cert-auth\" must be set for etcd to check all incoming peer requests from the cluster for valid client certificates.", "fixid": "F-45659r927111_fix", "fixtext": "Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.\n\nSet the value of \"--peer-client-cert-auth\" to \"true\" for the etcd.", "iacontrols": null, "id": "V-242426", "ruleID": "SV-242426r1043178_rule", "severity": "medium", "title": "Kubernetes etcd must enable client authentication to secure service.", "version": "TBD"}, "V-242427": {"checkid": "C-45702r863869_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nRun the command:\n    grep -i key-file *\n\nIf the setting \"key-file\" is not configured in the etcd manifest  file, this is a finding.", "description": "Kubernetes stores configuration and state information in a distributed key-value store called etcd. Anyone who can write to etcd can effectively control the Kubernetes cluster. Even just reading the contents of etcd could easily provide helpful hints to a would-be attacker. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server and etcd with a means to be able to authenticate sessions and encrypt traffic. \n\nTo enable encrypted communication for etcd, the parameter key-file must be set. This parameter gives the location of the key file used to secure etcd communication.", "fixid": "F-45660r863870_fix", "fixtext": "Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--key-file\" to the Approved Organizational Certificate.", "iacontrols": null, "id": "V-242427", "ruleID": "SV-242427r1043178_rule", "severity": "medium", "title": "Kubernetes etcd must have a key file for secure communication.", "version": "TBD"}, "V-242428": {"checkid": "C-45703r863872_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\ngrep -i cert-file * \n\nIf the setting \"cert-file\" is not configured in the Kubernetes etcd manifest file, this is a finding.", "description": "Kubernetes stores configuration and state information in a distributed key-value store called etcd. Anyone who can write to etcd can effectively control a Kubernetes cluster. Even just reading the contents of etcd could easily provide helpful hints to a would-be attacker. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server and etcd with a means to be able to authenticate sessions and encrypt traffic. \n\nTo enable encrypted communication for etcd, the parameter cert-file must be set. This parameter gives the location of the SSL certification file used to secure etcd communication.", "fixid": "F-45661r863873_fix", "fixtext": "Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--cert-file\" to the Approved Organizational Certificate.", "iacontrols": null, "id": "V-242428", "ruleID": "SV-242428r1043178_rule", "severity": "medium", "title": "Kubernetes etcd must have a certificate for communication.", "version": "TBD"}, "V-242429": {"checkid": "C-45704r927113_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\ngrep -i etcd-cafile * \n\nIf the setting \"--etcd-cafile\" is not configured in the Kubernetes API Server manifest file, this is a finding.", "description": "Kubernetes stores configuration and state information in a distributed key-value store called etcd. Anyone who can write to etcd can effectively control a Kubernetes cluster. Even just reading the contents of etcd could easily provide helpful hints to a would-be attacker. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server and etcd with a means to be able to authenticate sessions and encrypt traffic. \n\nTo enable encrypted communication for etcd, the parameter \"--etcd-cafile\" must be set. This parameter gives the location of the SSL Certificate Authority file used to secure etcd communication.", "fixid": "F-45662r927114_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--etcd-cafile\" to the Certificate Authority for etcd.", "iacontrols": null, "id": "V-242429", "ruleID": "SV-242429r1043178_rule", "severity": "medium", "title": "Kubernetes etcd must have the SSL Certificate Authority set.", "version": "TBD"}, "V-242430": {"checkid": "C-45705r927116_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\ngrep -i etcd-certfile * \n\nIf the setting \"--etcd-certfile\" is not set in the Kubernetes API Server manifest file, this is a finding.", "description": "Kubernetes stores configuration and state information in a distributed key-value store called etcd. Anyone who can write to etcd can effectively control the Kubernetes cluster. Even just reading the contents of etcd could easily provide helpful hints to a would-be attacker. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server and etcd with a means to be able to authenticate sessions and encrypt traffic. \n\nTo enable encrypted communication for etcd, the parameter \"--etcd-certfile\" must be set. This parameter gives the location of the SSL certification file used to secure etcd communication.", "fixid": "F-45663r927117_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--etcd-certfile\" to the certificate to be used for communication with etcd.", "iacontrols": null, "id": "V-242430", "ruleID": "SV-242430r1043178_rule", "severity": "medium", "title": "Kubernetes etcd must have a certificate for communication.", "version": "TBD"}, "V-242431": {"checkid": "C-45706r927119_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\ngrep -i etcd-keyfile * \n\nIf the setting \"--etcd-keyfile\" is not configured in the Kubernetes API Server manifest file, this is a finding.", "description": "Kubernetes stores configuration and state information in a distributed key-value store called etcd. Anyone who can write to etcd can effectively control a Kubernetes cluster. Even just reading the contents of etcd could easily provide helpful hints to a would-be attacker. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server and etcd with a means to be able to authenticate sessions and encrypt traffic. \n\nTo enable encrypted communication for etcd, the parameter \"--etcd-keyfile\" must be set. This parameter gives the location of the key file used to secure etcd communication.", "fixid": "F-45664r927120_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--etcd-keyfile\" to the certificate to be used for communication with etcd.", "iacontrols": null, "id": "V-242431", "ruleID": "SV-242431r1043178_rule", "severity": "medium", "title": "Kubernetes etcd must have a key file for secure communication.", "version": "TBD"}, "V-242432": {"checkid": "C-45707r863884_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\ngrep -i peer-cert-file * \n\nIf the setting \"peer-cert-file\" is not configured in the Kubernetes etcd manifest file, this is a finding.", "description": "Kubernetes stores configuration and state information in a distributed key-value store called etcd. Anyone who can write to etcd can effectively control the Kubernetes cluster. Even just reading the contents of etcd could easily provide helpful hints to a would-be attacker. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server and etcd with a means to be able to authenticate sessions and encrypt traffic. \n\nTo enable encrypted communication for etcd, the parameter peer-cert-file must be set. This parameter gives the location of the SSL certification file used to secure etcd communication.", "fixid": "F-45665r863885_fix", "fixtext": "Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--peer-cert-file\" to the certificate to be used for communication with etcd.", "iacontrols": null, "id": "V-242432", "ruleID": "SV-242432r1043178_rule", "severity": "medium", "title": "Kubernetes etcd must have peer-cert-file set for secure communication.", "version": "TBD"}, "V-242433": {"checkid": "C-45708r863887_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\ngrep -i peer-key-file *\n\nIf the setting \"peer-key-file\" is not set in the Kubernetes etcd manifest file, this is a finding.", "description": "Kubernetes stores configuration and state information in a distributed key-value store called etcd. Anyone who can write to etcd can effectively control a Kubernetes cluster. Even just reading the contents of etcd could easily provide helpful hints to a would-be attacker. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server and etcd with a means to be able to authenticate sessions and encrypt traffic. \n\nTo enable encrypted communication for etcd, the parameter peer-key-file must be set. This parameter gives the location of the SSL certification file used to secure etcd communication.", "fixid": "F-45666r863888_fix", "fixtext": "Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--peer-key-file\" to the certificate to be used for communication with etcd.", "iacontrols": null, "id": "V-242433", "ruleID": "SV-242433r1043178_rule", "severity": "medium", "title": "Kubernetes etcd must have a peer-key-file set for secure communication.", "version": "TBD"}, "V-242434": {"checkid": "C-45709r918186_chk", "checktext": "On the Control Plane, run the command:\nps -ef | grep kubelet\n\nIf the \"--protect-kernel-defaults\" option exists, this is a finding.\n\nNote the path to the config file (identified by --config).\n\nRun the command:\ngrep -i protectKernelDefaults <path_to_config_file>\n\nIf the setting \"protectKernelDefaults\" is not set or is set to false, this is a finding.", "description": "System kernel is responsible for memory, disk, and task management. The kernel provides a gateway between the system hardware and software. Kubernetes requires kernel access to allocate resources to the Control Plane. Threat actors that penetrate the system kernel can inject malicious code or hijack the Kubernetes architecture. It is vital to implement protections through Kubernetes components to reduce the attack surface.", "fixid": "F-45667r918187_fix", "fixtext": "On the Control Plane, run the command:\nps -ef | grep kubelet\n\nRemove the \"--protect-kernel-defaults\" option if present.\n\nNote the path to the Kubernetes Kubelet config file (identified by --config).\n\nEdit the Kubernetes Kubelet config file: \nSet \"protectKernelDefaults\" to \"true\". \n\nRestart the kubelet service using the following command:\nsystemctl daemon-reload && systemctl restart kubelet", "iacontrols": null, "id": "V-242434", "ruleID": "SV-242434r961131_rule", "severity": "high", "title": "Kubernetes Kubelet must enable kernel protection.", "version": "TBD"}, "V-242436": {"checkid": "C-45711r863896_chk", "checktext": "Prior to version 1.21, to enforce security policiesPod Security Policies (psp) were used. Those are now deprecated and will be removed from version 1.25.\n\nMigrate from PSP to PSA:\nhttps://kubernetes.io/docs/tasks/configure-pod-container/migrate-from-psp/ \n\nPre-version 1.25 Check:\nChange to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\ngrep -i ValidatingAdmissionWebhook * \n\nIf a line is not returned that includes enable-admission-plugins and ValidatingAdmissionWebhook, this is a finding.", "description": "Enabling the admissions webhook allows for Kubernetes to apply policies against objects that are to be created, read, updated, or deleted. By applying a pod security policy, control can be given to not allow images to be instantiated that run as the root user. If pods run as the root user, the pod then has root privileges to the host system and all the resources it has. An attacker can use this to attack the Kubernetes cluster. By implementing a policy that does not allow root or privileged pods, the pod users are limited in what the pod can do and access.", "fixid": "F-45669r863897_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument \"--enable-admission-plugins\" to include \"ValidatingAdmissionWebhook\".  Each enabled plugin is separated by commas.\n\nNote: It is best to implement policies first and then enable the webhook, otherwise a denial of service may occur.", "iacontrols": null, "id": "V-242436", "ruleID": "SV-242436r961359_rule", "severity": "high", "title": "The Kubernetes API server must have the ValidatingAdmissionWebhook enabled.", "version": "TBD"}, "V-242437": {"checkid": "C-45712r863899_chk", "checktext": "Prior to version 1.21, to enforce security policiesPod Security Policies (psp) were used. Those are now deprecated and will be removed from version 1.25.\n\nMigrate from PSP to PSA:\nhttps://kubernetes.io/docs/tasks/configure-pod-container/migrate-from-psp/ \n\nPre-version 1.25 Check:\nOn the Control Plane, run the command:\nkubectl get podsecuritypolicy\n\nIf there is no pod security policy configured, this is a finding. \n\nFor any pod security policies listed, edit the policy with the command:\nkubectl edit podsecuritypolicy policyname\n(Note: \"policyname\" is the name of the policy.)\n\nReview the runAsUser, supplementalGroups and fsGroup sections of the policy.\n\nIf any of these sections are missing, this is a finding.\n\nIf the rule within the runAsUser section is not set to \"MustRunAsNonRoot\", this is a finding.\n\nIf the ranges within the supplementalGroups section has min set to \"0\" or min is missing, this is a finding.\n\nIf the ranges within the fsGroup section has a min set to \"0\" or the min is missing, this is a finding.", "description": "Enabling the admissions webhook allows for Kubernetes to apply policies against objects that are to be created, read, updated, or deleted. By applying a pod security policy, control can be given to not allow images to be instantiated that run as the root user. If pods run as the root user, the pod then has root privileges to the host system and all the resources it has. An attacker can use this to attack the Kubernetes cluster. By implementing a policy that does not allow root or privileged pods, the pod users are limited in what the pod can do and access.", "fixid": "F-45670r863900_fix", "fixtext": "From the Control Plane, save the following policy to a file called restricted.yml.\n\napiVersion: policy/v1beta1\nkind: PodSecurityPolicy\nmetadata:\nname: restricted\nannotations:\napparmor.security.beta.kubernetes.io/allowedProfileNames: 'runtime/default',\nseccomp.security.alpha.kubernetes.io/defaultProfileName: 'runtime/default',\napparmor.security.beta.kubernetes.io/defaultProfileName: 'runtime/default'\nspec:\nprivileged: false\n# Required to prevent escalations to root.\nallowPrivilegeEscalation: false\n# This is redundant with non-root + disallow privilege escalation,\n# but we can provide it for defense in depth.\nrequiredDropCapabilities:\n- ALL\n# Allow core volume types.\nvolumes:\n- 'configMap'\n- 'emptyDir'\n- 'projected'\n- 'secret'\n- 'downwardAPI'\n# Assume that persistentVolumes set up by the cluster admin are safe to use.\n- 'persistentVolumeClaim'\nhostNetwork: false\nhostIPC: false\nhostPID: false\nrunAsUser:\n# Require the container to run without root privileges.\nrule: 'MustRunAsNonRoot'\nseLinux:\n# This policy assumes the nodes are using AppArmor rather than SELinux.\nrule: 'RunAsAny'\nsupplementalGroups:\nrule: 'MustRunAs'\nranges:\n# Forbid adding the root group.\n- min: 1\nmax: 65535\nfsGroup:\nrule: 'MustRunAs'\nranges:\n# Forbid adding the root group.\n- min: 1\nmax: 65535\nreadOnlyRootFilesystem: false\n\nTo implement the policy, run the command:\nkubectl create -f restricted.yml", "iacontrols": null, "id": "V-242437", "ruleID": "SV-242437r961359_rule", "severity": "high", "title": "Kubernetes must have a pod security policy set.", "version": "TBD"}, "V-242438": {"checkid": "C-45713r927126_chk", "checktext": "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -I request-timeout * \n\nIf Kubernetes API Server manifest file does not exist, this is a finding. \n\nIf the setting \"--request-timeout\" is set to \"0\" in the Kubernetes API Server manifest file, or is not configured this is a finding.", "description": "Kubernetes API Server request timeouts sets the duration a request stays open before timing out. Since the API Server is the central component in the Kubernetes Control Plane, it is vital to protect this service. If request timeouts were not set, malicious attacks or unwanted activities might affect multiple deployments across different applications or environments. This might deplete all resources from the Kubernetes infrastructure causing the information system to go offline. The \"--request-timeout\" value must never be set to \"0\". This disables the request-timeout feature. (By default, the \"--request-timeout\" is set to \"1 minute\".)", "fixid": "F-45671r927127_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--request-timeout\" greater than \"0\".", "iacontrols": null, "id": "V-242438", "ruleID": "SV-242438r961620_rule", "severity": "medium", "title": "Kubernetes API Server must configure timeouts to limit attack surface.", "version": "TBD"}, "V-242442": {"checkid": "C-45717r863905_chk", "checktext": "To view all pods and the images used to create the pods, from the Control Plane, run the following command:\nkubectl get pods --all-namespaces -o jsonpath=\"{..image}\" | \\\ntr -s '[[:space:]]' '\\n' | \\\nsort | \\\nuniq -c\n\nReview the images used for pods running within Kubernetes.\n\nIf there are multiple versions of the same image, this is a finding.", "description": "Previous versions of Kubernetes components that are not removed after updates have been installed may be exploited by adversaries by allowing the vulnerabilities to still exist within the cluster. It is important for Kubernetes to remove old pods when newer pods are created using new images to always be at the desired security state.", "fixid": "F-45675r863906_fix", "fixtext": "Remove any old pods that are using older images. On the Control Plane, run the command:\nkubectl delete pod podname\n(Note: \"podname\" is the name of the pod to delete.)", "iacontrols": null, "id": "V-242442", "ruleID": "SV-242442r961677_rule", "severity": "medium", "title": "Kubernetes must remove old components after updated versions have been installed.", "version": "TBD"}, "V-242443": {"checkid": "C-45718r863908_chk", "checktext": "Authenticate on the Kubernetes Control Plane. Run the command:\nkubectl version --short\n\nIf kubectl version has a setting not supporting Kubernetes skew policy, this is a finding.\n\nNote: Kubernetes Skew Policy can be found at: https://kubernetes.io/docs/setup/release/version-skew-policy/#supported-versions", "description": "Kubernetes software must stay up to date with the latest patches, service packs, and hot fixes. Not updating the Kubernetes control plane will expose the organization to vulnerabilities.\n\nFlaws discovered during security assessments, continuous monitoring, incident response activities, or information system error handling must also be addressed expeditiously. \n\nOrganization-defined time periods for updating security-relevant container platform components may vary based on a variety of factors including, for example, the security category of the information system or the criticality of the update (i.e., severity of the vulnerability related to the discovered flaw). \n\nThis requirement will apply to software patch management solutions that are used to install patches across the enclave and also to applications themselves that are not part of that patch management solution. For example, many browsers today provide the capability to install their own patch software. Patch criticality, as well as system criticality will vary. Therefore, the tactical situations regarding the patch management process will also vary. This means that the time period utilized must be a configurable parameter. Time frames for application of security-relevant software updates may be dependent upon the IAVM process.\n\nThe container platform components will be configured to check for and install security-relevant software updates within an identified time period from the availability of the update. The container platform registry will ensure the images are current. The specific time period will be defined by an authoritative source (e.g., IAVM, CTOs, DTMs, and STIGs).", "fixid": "F-45676r712684_fix", "fixtext": "Upgrade Kubernetes to the supported version. Institute and adhere to the policies and procedures to ensure that patches are consistently applied within the time allowed.", "iacontrols": null, "id": "V-242443", "ruleID": "SV-242443r961683_rule", "severity": "medium", "title": "Kubernetes must contain the latest updates as authorized by IAVMs, CTOs, DTMs, and STIGs.", "version": "TBD"}, "V-242444": {"checkid": "C-45719r712686_chk", "checktext": "Review the ownership of the Kubernetes manifests files by using the command:\n\nstat -c %U:%G /etc/kubernetes/manifests/* | grep -v root:root\n\nIf the command returns any non root:root file permissions, this is a finding.", "description": "The Kubernetes manifests are those files that contain the arguments and settings for the Control Plane services. These services are etcd, the api server, controller, proxy, and scheduler. If these files can be changed, the scheduler will be implementing the changes immediately. Many of the security settings within the document are implemented through these manifests.", "fixid": "F-45677r712687_fix", "fixtext": "Change the ownership of the manifest files to root: root by executing the command:\n\nchown root:root /etc/kubernetes/manifests/*", "iacontrols": null, "id": "V-242444", "ruleID": "SV-242444r961863_rule", "severity": "medium", "title": "The Kubernetes component manifests must be owned by root.", "version": "TBD"}, "V-242445": {"checkid": "C-45720r712689_chk", "checktext": "Review the ownership of the Kubernetes etcd files by using the command:\n\nstat -c %U:%G /var/lib/etcd/* | grep -v etcd:etcd\n\nIf the command returns any non etcd:etcd file permissions, this is a finding.", "description": "The Kubernetes etcd key-value store provides a way to store data to the Control Plane. If these files can be changed, data to API object and the Control Plane would be compromised. The scheduler will implement the changes immediately. Many of the security settings within the document are implemented through this file.", "fixid": "F-45678r712690_fix", "fixtext": "Change the ownership of the manifest files to etcd:etcd by executing the command:\n\nchown etcd:etcd /var/lib/etcd/*", "iacontrols": null, "id": "V-242445", "ruleID": "SV-242445r961863_rule", "severity": "medium", "title": "The Kubernetes component etcd must be owned by etcd.", "version": "TBD"}, "V-242446": {"checkid": "C-45721r712692_chk", "checktext": "Review the Kubernetes conf files by using the command:\n\nstat -c %U:%G /etc/kubernetes/admin.conf | grep -v root:root\nstat -c %U:%G /etc/kubernetes/scheduler.conf | grep -v root:root\nstat -c %U:%G /etc/kubernetes/controller-manager.conf | grep -v root:root\n\nIf the command returns any non root:root file permissions, this is a finding.", "description": "The Kubernetes conf files contain the arguments and settings for the Control Plane services. These services are controller and scheduler. If these files can be changed, the scheduler will be implementing the changes immediately. Many of the security settings within the document are implemented through this file.", "fixid": "F-45679r712693_fix", "fixtext": "Change the ownership of the conf files to root: root by executing the command:\n\nchown root:root /etc/kubernetes/admin.conf \nchown root:root /etc/kubernetes/scheduler.conf \nchown root:root /etc/kubernetes/controller-manager.conf", "iacontrols": null, "id": "V-242446", "ruleID": "SV-242446r961863_rule", "severity": "medium", "title": "The Kubernetes conf files must be owned by root.", "version": "TBD"}, "V-242447": {"checkid": "C-45722r712695_chk", "checktext": "Check if Kube-Proxy is running and obtain --kubeconfig parameter use the following command:\nps -ef | grep kube-proxy\n\nIf Kube-Proxy exists:\nReview the permissions of the Kubernetes Kube Proxy by using the command:\nstat -c %a <location from --kubeconfig>\n\nIf the file has permissions more permissive than \"644\", this is a finding.", "description": "The Kubernetes Kube Proxy kubeconfig contain the argument and setting for the Control Planes. These settings contain network rules for restricting network communication between pods, clusters, and networks. If these files can be changed, data traversing between the Kubernetes Control Panel components would be compromised. Many of the security settings within the document are implemented through this file.", "fixid": "F-45680r821611_fix", "fixtext": "Change the permissions of the Kube Proxy to \"644\" by executing the command:\n\nchmod 644 <location from kubeconfig>.", "iacontrols": null, "id": "V-242447", "ruleID": "SV-242447r961863_rule", "severity": "medium", "title": "The Kubernetes Kube Proxy kubeconfig must have file permissions set to 644 or more restrictive.", "version": "TBD"}, "V-242448": {"checkid": "C-45723r712698_chk", "checktext": "Check if Kube-Proxy is running use the following command:\nps -ef | grep kube-proxy\n\nIf Kube-Proxy exists:\nReview the permissions of the Kubernetes Kube Proxy by using the command:\nstat -c   %U:%G <location from --kubeconfig>| grep -v root:root\n\nIf the command returns any non root:root file permissions, this is a finding.", "description": "The Kubernetes Kube Proxy kubeconfig contain the argument and setting for the Control Planes. These settings contain network rules for restricting network communication between pods, clusters, and networks. If these files can be changed, data traversing between the Kubernetes Control Panel components would be compromised. Many of the security settings within the document are implemented through this file.", "fixid": "F-45681r712699_fix", "fixtext": "Change the ownership of the Kube Proxy to root:root by executing the command:\n\nchown root:root <location from kubeconfig>.", "iacontrols": null, "id": "V-242448", "ruleID": "SV-242448r961863_rule", "severity": "medium", "title": "The Kubernetes Kube Proxy kubeconfig must be owned by root.", "version": "TBD"}, "V-242449": {"checkid": "C-45724r919321_chk", "checktext": "On the Control Plane, run the command:\nps -ef | grep kubelet\n\nIf the \"--client-ca-file\" option exists, this is a finding. \n\nNote the path to the config file (identified by --config).\n\nRun the command:\ngrep -i clientCAFile <path_to_config_file>\n\nNote the path to the client ca file.\n\nRun the command:\nstat -c %a <path_to_client_ca_file>\n\nIf the client ca file has permissions more permissive than \"644\", this is a finding.", "description": "The Kubernetes kubelet certificate authority file contains settings for the Kubernetes Node TLS certificate authority. Any request presenting a client certificate signed by one of the authorities in the client-ca-file is authenticated with an identity corresponding to the CommonName of the client certificate. If this file can be changed, the Kubernetes architecture could be compromised. The scheduler will implement the changes immediately. Many of the security settings within the document are implemented through this file.", "fixid": "F-45682r919324_fix", "fixtext": "On the Control Plane, run the command:\nps -ef | grep kubelet\n\nRemove the \"--client-ca-file\" option.\n\nNote the path to the config file (identified by --config).\n\nRun the command:\ngrep -i clientCAFile <path_to_config_file>\n\nNote the path to the client ca file.\n\nRun the command:\nchmod 644 <path_to_client_ca_file>", "iacontrols": null, "id": "V-242449", "ruleID": "SV-242449r961863_rule", "severity": "medium", "title": "The Kubernetes Kubelet certificate authority file must have file permissions set to 644 or more restrictive.", "version": "TBD"}, "V-242450": {"checkid": "C-45725r918194_chk", "checktext": "On the Control Plane, run the command:\nps -ef | grep kubelet\n\nIf the \"client-ca-file\" option exists, this is a finding. \n\nNote the path to the config file (identified by --config).\n\nRun the command:\ngrep -i clientCAFile <path_to_config_file>\n\nNote the path to the client ca file.\n\nRun the command:\nstat -c %U:%G <path_to_client_ca_file>\n\nIf the command returns any non root:root file permissions, this is a finding.", "description": "The Kubernetes kube proxy kubeconfig contain the argument and setting for the Control Planes. These settings contain network rules for restricting network communication between pods, clusters, and networks. If these files can be changed, data traversing between the Kubernetes Control Panel components would be compromised. Many of the security settings within the document are implemented through this file.", "fixid": "F-45683r918195_fix", "fixtext": "On the Control Plane, run the command:\nps -ef | grep kubelet\n\nRemove the \"client-ca-file\" option.\n\nNote the path to the config file (identified by --config).\n\nRun the command:\ngrep -i clientCAFile <path_to_config_file>\n\nNote the path to the client ca file.\n\nRun the command:\nchown root:root <path_to_client_ca_file>", "iacontrols": null, "id": "V-242450", "ruleID": "SV-242450r961863_rule", "severity": "medium", "title": "The Kubernetes Kubelet certificate authority must be owned by root.", "version": "TBD"}, "V-242451": {"checkid": "C-45726r712707_chk", "checktext": "Review the PKI files in Kubernetes by using the command:\n\nls -laR /etc/kubernetes/pki/\n\nIf the command returns any non root:root file permissions, this is a finding.", "description": "The Kubernetes PKI directory contains all certificates (.crt files) supporting secure network communications in the Kubernetes Control Plane. If these files can be modified, data traversing within the architecture components would become unsecure and compromised. Many of the security settings within the document are implemented through this file.", "fixid": "F-45684r712708_fix", "fixtext": "Change the ownership of the PKI to root: root by executing the command:\n\nchown -R root:root /etc/kubernetes/pki/", "iacontrols": null, "id": "V-242451", "ruleID": "SV-242451r961863_rule", "severity": "medium", "title": "The Kubernetes component PKI must be owned by root.", "version": "TBD"}, "V-242452": {"checkid": "C-45727r712710_chk", "checktext": "Review the permissions of the Kubernetes Kubelet conf by using the command:\n\nstat -c %a  /etc/kubernetes/kubelet.conf\n\nIf any of the files are have permissions more permissive than \"644\", this is a finding.", "description": "The Kubernetes kubelet agent registers nodes with the API Server, mounts volume storage for pods, and performs health checks to containers within pods. If these files can be modified, the information system would be unaware of pod or container degradation. Many of the security settings within the document are implemented through this file.", "fixid": "F-45685r821615_fix", "fixtext": "Change the permissions of the Kubelet to \"644\" by executing the command:\n\nchmod 644 /etc/kubernetes/kubelet.conf", "iacontrols": null, "id": "V-242452", "ruleID": "SV-242452r961863_rule", "severity": "medium", "title": "The Kubernetes kubelet KubeConfig must have file permissions set to 644 or more restrictive.", "version": "TBD"}, "V-242453": {"checkid": "C-45728r712713_chk", "checktext": "Review the Kubernetes Kubelet conf files by using the command:\n\nstat -c %U:%G /etc/kubernetes/kubelet.conf| grep -v root:root\n\nIf the command returns any non root:root file permissions, this is a finding.", "description": "The Kubernetes kubelet agent registers nodes with the API server and performs health checks to containers within pods. If these files can be modified, the information system would be unaware of pod or container degradation. Many of the security settings within the document are implemented through this file.", "fixid": "F-45686r712714_fix", "fixtext": "Change the ownership of the kubelet.conf to root: root by executing the command:\n\nchown root:root /etc/kubernetes/kubelet.conf", "iacontrols": null, "id": "V-242453", "ruleID": "SV-242453r961863_rule", "severity": "medium", "title": "The Kubernetes kubelet KubeConfig file must be owned by root.", "version": "TBD"}, "V-242454": {"checkid": "C-45729r754817_chk", "checktext": "Review the Kubeadm.conf file :\n\nGet the path for Kubeadm.conf by running: \nsytstemctl status kubelet\n\nNote the configuration file installed by the kubeadm is written to \n(Default Location: /etc/systemd/system/kubelet.service.d/10-kubeadm.conf)\nstat -c %U:%G <kubeadm.conf path> | grep -v root:root\n\nIf the command returns any non root:root file permissions, this is a finding. ", "description": "The Kubernetes kubeeadm.conf contains sensitive information regarding the cluster nodes configuration. If this file can be modified, the Kubernetes Platform Plane would be degraded or compromised for malicious intent. Many of the security settings within the document are implemented through this file.", "fixid": "F-45687r754818_fix", "fixtext": "Change the ownership of the kubeadm.conf to root: root by executing the command:\n\nchown root:root <kubeadm.conf path>", "iacontrols": null, "id": "V-242454", "ruleID": "SV-242454r961863_rule", "severity": "medium", "title": "The Kubernetes kubeadm.conf must be owned by root.", "version": "TBD"}, "V-242455": {"checkid": "C-45730r754820_chk", "checktext": "Review the kubeadm.conf file :\n\nGet the path for kubeadm.conf by running:\nsystemctl status kubelet\n\nNote the configuration file installed by the kubeadm is written to\n(Default Location: /etc/systemd/system/kubelet.service.d/10-kubeadm.conf)\nstat -c %a  <kubeadm.conf path>\n\nIf the file has permissions more permissive than \"644\", this is a finding. ", "description": "The Kubernetes kubeadm.conf contains sensitive information regarding the cluster nodes configuration. If this file can be modified, the Kubernetes Platform Plane would be degraded or compromised for malicious intent. Many of the security settings within the document are implemented through this file.", "fixid": "F-45688r754821_fix", "fixtext": "Change the permissions of kubeadm.conf to \"644\" by executing the command:\n\nchmod 644 <kubeadm.conf path> ", "iacontrols": null, "id": "V-242455", "ruleID": "SV-242455r961863_rule", "severity": "medium", "title": "The Kubernetes kubeadm.conf must have file permissions set to 644 or more restrictive.", "version": "TBD"}, "V-242456": {"checkid": "C-45731r712722_chk", "checktext": "Review the permissions of the Kubernetes config.yaml by using the command:\n\nstat -c %a /var/lib/kubelet/config.yaml\n\nIf any of the files are have permissions more permissive than \"644\", this is a finding.", "description": "The Kubernetes kubelet agent registers nodes with the API server and performs health checks to containers within pods. If this file can be modified, the information system would be unaware of pod or container degradation.", "fixid": "F-45689r821617_fix", "fixtext": "Change the permissions of the config.yaml to \"644\" by executing the command:\n\nchmod 644 /var/lib/kubelet/config.yaml", "iacontrols": null, "id": "V-242456", "ruleID": "SV-242456r961863_rule", "severity": "medium", "title": "The Kubernetes kubelet config must have file permissions set to 644 or more restrictive.", "version": "TBD"}, "V-242457": {"checkid": "C-45732r712725_chk", "checktext": "Review the Kubernetes Kubeadm kubelet conf file by using the command:\n\nstat -c %U:%G /var/lib/kubelet/config.yaml| grep -v root:root\n\nIf the command returns any non root:root file permissions, this is a finding.", "description": "The Kubernetes kubelet agent registers nodes with the API Server and performs health checks to containers within pods. If this file can be modified, the information system would be unaware of pod or container degradation.", "fixid": "F-45690r712726_fix", "fixtext": "Change the ownership of the kubelet config to \"root: root\" by executing the command:\n\nchown root:root /var/lib/kubelet/config.yaml", "iacontrols": null, "id": "V-242457", "ruleID": "SV-242457r961863_rule", "severity": "medium", "title": "The Kubernetes kubelet config must be owned by root.", "version": "TBD"}, "V-242459": {"checkid": "C-45734r918198_chk", "checktext": "Review the permissions of the Kubernetes etcd by using the command:\n\nls -AR /var/lib/etcd/*\n\nIf any of the files have permissions more permissive than \"644\", this is a finding.", "description": "The Kubernetes etcd key-value store provides a way to store data to the Control Plane. If these files can be changed, data to API object and Control Plane would be compromised.", "fixid": "F-45692r918199_fix", "fixtext": "Change the permissions of the manifest files to \"644\" by executing the command:\n\nchmod -R 644 /var/lib/etcd/*", "iacontrols": null, "id": "V-242459", "ruleID": "SV-242459r961863_rule", "severity": "medium", "title": "The Kubernetes etcd must have file permissions set to 644 or more restrictive.", "version": "TBD"}, "V-242460": {"checkid": "C-45735r712734_chk", "checktext": "Review the permissions of the Kubernetes config files by using the command:\n\nstat -c %a /etc/kubernetes/admin.conf\nstat -c %a /etc/kubernetes/scheduler.conf\nstat -c %a /etc/kubernetes/controller-manager.conf\n\nIf any of the files are have permissions more permissive than \"644\", this is a finding.", "description": "The Kubernetes admin kubeconfig files contain the arguments and settings for the Control Plane services. These services are controller and scheduler. If these files can be changed, the scheduler will be implementing the changes immediately.", "fixid": "F-45693r712735_fix", "fixtext": "Change the permissions of the conf files to \"644\" by executing the command:\n\nchmod 644 /etc/kubernetes/admin.conf \nchmod 644 /etc/kubernetes/scheduler.conf\nchmod 644 /etc/kubernetes/controller-manager.conf", "iacontrols": null, "id": "V-242460", "ruleID": "SV-242460r961863_rule", "severity": "medium", "title": "The Kubernetes admin kubeconfig must have file permissions set to 644 or more restrictive.", "version": "TBD"}, "V-242461": {"checkid": "C-45736r863922_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\ngrep -i audit-policy-file * \n\nIf the setting \"audit-policy-file\" is not set or is found in the Kubernetes API manifest file without valid content, this is a finding.", "description": "Kubernetes API Server validates and configures pods and services for the API object. The REST operation provides frontend functionality to the cluster share state. Enabling audit logs provides a way to monitor and identify security risk events or misuse of information. Audit logs are necessary to provide evidence in the case the Kubernetes API Server is compromised requiring a Cyber Security Investigation.", "fixid": "F-45694r863923_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument \"--audit-policy-file\" to \"log file directory\".", "iacontrols": null, "id": "V-242461", "ruleID": "SV-242461r961863_rule", "severity": "medium", "title": "Kubernetes API Server audit logs must be enabled.", "version": "TBD"}, "V-242462": {"checkid": "C-45737r927135_chk", "checktext": "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i audit-log-maxsize * \n\nIf the setting \"--audit-log-maxsize\" is not set in the Kubernetes API Server manifest file or it is set to less than \"100\", this is a finding.", "description": "The Kubernetes API Server must be set for enough storage to retain log information over the period required. When audit logs are large in size, the monitoring service for events becomes degraded. The function of the maximum log file size is to set these limits.", "fixid": "F-45695r927136_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--audit-log-maxsize\" to a minimum of \"100\".", "iacontrols": null, "id": "V-242462", "ruleID": "SV-242462r961863_rule", "severity": "medium", "title": "The Kubernetes API Server must be set to audit log max size.", "version": "TBD"}, "V-242463": {"checkid": "C-45738r863928_chk", "checktext": "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i audit-log-maxbackup * \n\nIf the setting \"audit-log-maxbackup\" is not set in the Kubernetes API Server manifest file or it is set less than \"10\", this is a finding.", "description": "The Kubernetes API Server must set enough storage to retain logs for monitoring suspicious activity and system misconfiguration, and provide evidence for Cyber Security Investigations.", "fixid": "F-45696r863929_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of \"--audit-log-maxbackup\" to a minimum of \"10\".", "iacontrols": null, "id": "V-242463", "ruleID": "SV-242463r961863_rule", "severity": "medium", "title": "The Kubernetes API Server must be set to audit log maximum backup.", "version": "TBD"}, "V-242464": {"checkid": "C-45739r863931_chk", "checktext": "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i audit-log-maxage * \n\nIf the setting \"audit-log-maxage\" is not set in the Kubernetes API Server manifest file or it is set less than \"30\", this is a finding.", "description": "The Kubernetes API Server must set enough storage to retain logs for monitoring suspicious activity and system misconfiguration, and provide evidence for Cyber Security Investigations.", "fixid": "F-45697r863932_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of \"--audit-log-maxage\" to a minimum of \"30\".", "iacontrols": null, "id": "V-242464", "ruleID": "SV-242464r961863_rule", "severity": "medium", "title": "The Kubernetes API Server audit log retention must be set.", "version": "TBD"}, "V-242465": {"checkid": "C-45740r863934_chk", "checktext": "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i audit-log-path * \n\nIf the setting audit-log-path is not set in the Kubernetes API Server manifest file or it is not set to a valid path, this is a finding.", "description": "Kubernetes API Server validates and configures pods and services for the API object. The REST operation provides frontend functionality to the cluster share state. Audit logs are necessary to provide evidence in the case the Kubernetes API Server is compromised requiring Cyber Security Investigation. To record events in the audit log the log path value must be set.", "fixid": "F-45698r863935_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of \"--audit-log-path\" to valid location.", "iacontrols": null, "id": "V-242465", "ruleID": "SV-242465r961863_rule", "severity": "medium", "title": "The Kubernetes API Server audit log path must be set.", "version": "TBD"}, "V-242466": {"checkid": "C-45741r927138_chk", "checktext": "Review the permissions of the Kubernetes PKI cert files by using the command:\n\nsudo find /etc/kubernetes/pki/* -name \"*.crt\" | xargs stat -c '%n %a'\n\nIf any of the files have permissions more permissive than \"644\", this is a finding.", "description": "The Kubernetes PKI directory contains all certificates (.crt files) supporting secure network communications in the Kubernetes Control Plane. If these files can be modified, data traversing within the architecture components would become unsecure and compromised.", "fixid": "F-45699r918202_fix", "fixtext": "Change the ownership of the cert files to \"644\" by executing the command: \n\nfind /etc/kubernetes/pki -name \"*.crt\" | xargs chmod 644", "iacontrols": null, "id": "V-242466", "ruleID": "SV-242466r961863_rule", "severity": "medium", "title": "The Kubernetes PKI CRT must have file permissions set to 644 or more restrictive.", "version": "TBD"}, "V-242467": {"checkid": "C-45742r918205_chk", "checktext": "Review the permissions of the Kubernetes PKI key files by using the command:\n\nsudo find /etc/kubernetes/pki -name \"*.key\" | xargs stat -c '%n %a'\n\nIf any of the files have permissions more permissive than \"600\", this is a finding.", "description": "The Kubernetes PKI directory contains all certificate key files supporting secure network communications in the Kubernetes Control Plane. If these files can be modified, data traversing within the architecture components would become unsecure and compromised.", "fixid": "F-45700r918206_fix", "fixtext": "Change the ownership of the key files to \"600\" by executing the command: \n\nfind /etc/kubernetes/pki -name \"*.key\" | xargs chmod 600", "iacontrols": null, "id": "V-242467", "ruleID": "SV-242467r961863_rule", "severity": "medium", "title": "The Kubernetes PKI keys must have file permissions set to 600 or more restrictive.", "version": "TBD"}, "V-245541": {"checkid": "C-48816r1069467_chk", "checktext": "Follow these steps to check streaming-connection-idle-timeout:\n\n1. On the Control Plane, run the command:\n\nps -ef | grep kubelet\n\nIf the \"--streaming-connection-idle-timeout\" option exists, this is a finding.\n\nNote the path to the config file (identified by --config).\n\n2. Run the command:\n\ngrep -i streamingConnectionIdleTimeout <path_to_config_file>\n\nIf the setting \"streamingConnectionIdleTimeout\" is set to less than \"5m\" or is not configured, this is a finding.", "description": "Idle connections from the Kubelet can be used by unauthorized users to perform malicious activity to the nodes, pods, containers, and cluster within the Kubernetes Control Plane. Setting the streamingConnectionIdleTimeout defines the maximum time an idle session is permitted prior to disconnect. Setting the value to \"0\" never disconnects any idle sessions. Idle timeouts must never be set to \"0\" and should be defined at \"5m\" (the default is 4hr).", "fixid": "F-48771r1069468_fix", "fixtext": "Follow these steps to configure streaming-connection-idle-timeout:\n1. On the Control Plane, run the command:\nps -ef | grep kubelet\n\nRemove the \"--streaming-connection-idle-timeout\" option if present.\n\nNote the path to the config file (identified by --config).\n\n2. Edit the Kubernetes Kubelet file in the --config directory on the Kubernetes Control Plane:\n\nSet the argument \"streamingConnectionIdleTimeout\" to a value of \"5m\".", "iacontrols": null, "id": "V-245541", "ruleID": "SV-245541r1069469_rule", "severity": "medium", "title": "Kuber<PERSON><PERSON> Ku<PERSON>et must not disable timeouts.", "version": "TBD"}, "V-245542": {"checkid": "C-48817r863943_chk", "checktext": "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i basic-auth-file * \n\nIf \"basic-auth-file\" is set in the Kubernetes API server manifest file this is a finding.", "description": "Kubernetes basic authentication sends and receives request containing username, uid, groups, and other fields over a clear text HTTP communication. Basic authentication does not provide any security mechanisms using encryption standards. PKI certificate-based authentication must be set over a secure channel to ensure confidentiality and integrity. Basic authentication must not be set in the manifest file.", "fixid": "F-48772r863944_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Remove the setting \"--basic-auth-file\".", "iacontrols": null, "id": "V-245542", "ruleID": "SV-245542r961632_rule", "severity": "high", "title": "Kubernetes API Server must disable basic authentication to protect information in transit.", "version": "TBD"}, "V-245543": {"checkid": "C-48818r927129_chk", "checktext": "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i token-auth-file * \n\nIf \"--token-auth-file\" is set in the Kubernetes API server manifest file, this is a finding.", "description": "Kubernetes token authentication uses password known as secrets in a plaintext file. This file contains sensitive information such as token, username and user uid. This token is used by service accounts within pods to authenticate with the API Server. This information is very valuable for attackers with malicious intent if the service account is privileged having access to the token. With this token a threat actor can impersonate the service account gaining access to the Rest API service.", "fixid": "F-48773r927130_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nRemove the setting \"--token-auth-file\".", "iacontrols": null, "id": "V-245543", "ruleID": "SV-245543r961632_rule", "severity": "high", "title": "Kubernetes API Server must disable token authentication to protect information in transit.", "version": "TBD"}, "V-245544": {"checkid": "C-48819r863949_chk", "checktext": "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i kubelet-client-certificate *\ngrep -I kubelet-client-key * \n\nIf the setting \"--kubelet-client-certificate\" is not configured in the Kubernetes API server manifest file or contains no value, this is a finding.\n\nIf the setting \"--kubelet-client-key\" is not configured in the Kubernetes API server manifest file or contains no value, this is a finding.", "description": "Kubernetes control plane and external communication is managed by API Server. The main implementation of the API Server is to manage hardware resources for pods and container using horizontal or vertical scaling. Anyone who can gain access to the API Server can effectively control your Kubernetes architecture. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server with a means to be able to authenticate sessions and encrypt traffic.\n\nBy default, the API Server does not authenticate to the kubelet HTTPs endpoint. To enable secure communication for API Server, the parameter -kubelet-client-certificate and kubelet-client-key must be set. This parameter gives the location of the certificate and key pair used to secure API Server communication.", "fixid": "F-48774r863950_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of \"--kubelet-client-certificate\" and \"--kubelet-client-key\" to an Approved Organizational Certificate and key pair.", "iacontrols": null, "id": "V-245544", "ruleID": "SV-245544r961632_rule", "severity": "high", "title": "Kubernetes endpoints must use approved organizational certificate and key pair to protect information in transit.", "version": "TBD"}, "V-254800": {"checkid": "C-58411r927123_chk", "checktext": "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\n\"grep -i admission-control-config-file *\"\n\nIf the setting \"--admission-control-config-file\" is not configured in the Kubernetes API Server manifest file, this is a finding.\n\nInspect the .yaml file defined by the --admission-control-config-file. Verify PodSecurity is properly configured. \nIf least privilege is not represented, this is a finding.", "description": "An admission controller intercepts and processes requests to the Kubernetes API prior to persistence of the object, but after the request is authenticated and authorized.\n\nKubernetes (> v1.23)offers a built-in Pod Security admission controller to enforce the Pod Security Standards. Pod security restrictions are applied at the namespace level when pods are created. \n\nThe Kubernetes Pod Security Standards define different isolation levels for Pods. These standards define how to restrict the behavior of pods in a clear, consistent fashion.", "fixid": "F-58357r927124_fix", "fixtext": "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--admission-control-config-file\" to a valid path for the file.\n\nCreate an admission controller config file:\nExample File:\n```yaml\napiVersion: apiserver.config.k8s.io/v1\nkind: AdmissionConfiguration\nplugins:\n- name: PodSecurity\n  configuration:\n    apiVersion: pod-security.admission.config.k8s.io/v1beta1\n    kind: PodSecurityConfiguration\n    # Defaults applied when a mode label is not set.\n    defaults:\n      enforce: \"privileged\"\n      enforce-version: \"latest\"\n    exemptions:\n      # Don't forget to exempt namespaces or users that are responsible for deploying\n      # cluster components, because they need to run privileged containers\n      usernames: [\"admin\"] \n      namespaces: [\"kube-system\"]\n\nSee for more details:\nMigrate from PSP to PSA:\nhttps://kubernetes.io/docs/tasks/configure-pod-container/migrate-from-psp/\n\nBest Practice: https://kubernetes.io/docs/concepts/security/pod-security-policy/#recommended-practice.", "iacontrols": null, "id": "V-254800", "ruleID": "SV-254800r961359_rule", "severity": "high", "title": "Kubernetes must have a Pod Security Admission control file configured.", "version": "TBD"}, "V-254801": {"checkid": "C-58412r918278_chk", "checktext": "On the Control Plane, change to the manifests' directory at /etc/kubernetes/manifests and run the command:\ngrep -i feature-gates *\n\nFor each manifest file, if the \"--feature-gates\" setting does not exist, does not contain the \"--PodSecurity\" flag, or sets the flag to \"false\", this is a finding.\n\nOn each Control Plane and Worker Node, run the command:\nps -ef | grep kubelet\n\nIf the \"--feature-gates\" option exists, this is a finding. \n\nNote the path to the config file (identified by --config).\n\nInspect the content of the config file:\nIf the \"featureGates\" setting is not present, does not contain the \"PodSecurity\" flag, or sets the flag to \"false\", this is a finding.", "description": "PodSecurity admission controller is a component that validates and enforces security policies for pods running within a Kubernetes cluster. It is responsible for evaluating the security context and configuration of pods against defined policies. \n\nTo enable PodSecurity admission controller on Static Pods (kube-apiserver, kube-controller-manager, or kube-schedule), the argument \"--feature-gates=PodSecurity=true\" must be set.\n\nTo enable PodSecurity admission controller on Kubelets, the featureGates PodSecurity=true argument must be set.\n\n(Note: The PodSecurity feature gate is GA as of  v1.25.)", "fixid": "F-58358r918213_fix", "fixtext": "On the Control Plane, change to the manifests' directory at /etc/kubernetes/manifests and run the command:\ngrep -i feature-gates *\n\nEnsure the argument \"--feature-gates=PodSecurity=true\" is present in each manifest file.\n\nOn each Control Plane and Worker Node, run the command:\nps -ef | grep kubelet\n\nRemove the \"--feature-gates\" option if present.\n\nNote the path to the config file (identified by --config).\n\nEdit the Kubernetes Kubelet config file: \nAdd a \"featureGates\" setting if one does not yet exist. Add the feature gate \"PodSecurity=true\".\n\nRestart the kubelet service using the following command:\nsystemctl daemon-reload && systemctl restart kubelet", "iacontrols": null, "id": "V-254801", "ruleID": "SV-254801r961359_rule", "severity": "high", "title": "Kubernetes must enable PodSecurity admission controller on static pods and Kubelets.", "version": "TBD"}}, "slug": "kubernetes", "title": "Kubernetes Security Technical Implementation Guide", "version": "2"}}