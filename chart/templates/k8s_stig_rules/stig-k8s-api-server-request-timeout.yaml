apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-request-timeout
  namespace: compliance-system
spec:
  id: "V-242438"
  title: "Kubernetes API Server must configure timeouts to limit attack surface."
  description: "Kubernetes API Server request timeouts sets the duration a request stays open before timing out. Since the API Server is the central component in the Kubernetes Control Plane, it is vital to protect this service. If request timeouts were not set, malicious attacks or unwanted activities might affect multiple deployments across different applications or environments. This might deplete all resources from the Kubernetes infrastructure causing the information system to go offline. The \"--request-timeout\" value must never be set to \"0\". This disables the request-timeout feature. (By default, the \"--request-timeout\" is set to \"1 minute\".)"
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -I request-timeout * \n\nIf Kubernetes API Server manifest file does not exist, this is a finding. \n\nIf the setting \"--request-timeout\" is set to \"0\" in the Kubernetes API Server manifest file, or is not configured this is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--request-timeout\" greater than \"0\"."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting API Server request-timeout configuration check (V-242438)"
    
    manifests_dir="/host/etc/kubernetes/manifests"
    
    echo "Checking for API Server manifest files in: $manifests_dir"
    
    # Check if manifests directory exists
    if [[ ! -d "$manifests_dir" ]]; then
        echo "FAIL: Kubernetes manifests directory not found: $manifests_dir"
        exit 1
    fi
    
    # Find API Server manifest files
    api_files=$(grep -lis 'kube-apiserver' "$manifests_dir"/* 2>/dev/null)
    
    if [[ -z "$api_files" ]]; then
        echo "FAIL: No API Server manifest found in $manifests_dir"
        echo "Expected to find files containing 'kube-apiserver'"
        exit 1
    fi
    
    echo "Found API Server manifest files:"
    echo "$api_files"
    
    # Check each API Server manifest file
    for file in $api_files; do
        echo "Analyzing manifest file: $file"
        
        # Search for request-timeout configuration
        timeout_line=$(grep -i -- "--request-timeout" "$file" 2>/dev/null)
        
        if [[ -z "$timeout_line" ]]; then
            echo "No explicit --request-timeout configuration found in $file"
            echo "Using default request-timeout (1 minute) - this is acceptable"
        else
            echo "Found request-timeout configuration: $timeout_line"
            
            # Check if timeout is set to 0 (which is forbidden)
            if echo "$timeout_line" | grep -q "=0" || echo "$timeout_line" | grep -q "= 0"; then
                echo "FAIL: request-timeout is set to 0 in $file"
                echo "This disables the timeout feature and creates security risk"
                echo "Configuration found: $timeout_line"
                exit 1
            else
                echo "request-timeout is properly configured (not set to 0)"
                echo "Configuration: $timeout_line"
            fi
        fi
    done
    
    echo "PASS: API Server request-timeout is properly configured"
    exit 0 