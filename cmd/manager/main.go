package main

import (
	"context"
	"flag"
	"os"

	"github.com/go-logr/logr"
	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/tools/leaderelection/resourcelock"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/healthz"
	"sigs.k8s.io/controller-runtime/pkg/log/zap"
	"sigs.k8s.io/controller-runtime/pkg/metrics/server"

	// "sigs.k8s.io/controller-runtime/pkg/webhook" // Temporarily disabled

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
	"github.com/alauda/compliance-operator/pkg/controller/compliancesuite"
	"github.com/alauda/compliance-operator/pkg/controller/profilebundle"
	"github.com/alauda/compliance-operator/pkg/controller/reportservice"
	"github.com/alauda/compliance-operator/pkg/controller/scan"
)

var (
	scheme   = runtime.NewScheme()
	setupLog = ctrl.Log.WithName("setup")
)

func init() {
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	utilruntime.Must(complianceapi.AddToScheme(scheme))
}

func main() {
	var metricsAddr string
	var enableLeaderElection bool
	var probeAddr string
	var storageClass string
	var storageSize string

	flag.StringVar(&metricsAddr, "metrics-bind-address", ":8080", "The address the metric endpoint binds to.")
	flag.StringVar(&storageClass, "report-storage-class", "", "StorageClass name for OpenSCAP report persistent storage. Leave empty to use emptyDir.")
	flag.StringVar(&storageSize, "report-storage-size", "10Gi", "Storage size for OpenSCAP report persistent storage (e.g. 10Gi, 100Gi). Only used when storage class is set.")
	flag.StringVar(&probeAddr, "health-probe-bind-address", ":8081", "The address the probe endpoint binds to.")
	flag.BoolVar(&enableLeaderElection, "leader-elect", false,
		"Enable leader election for controller manager. "+
			"Enabling this will ensure there is only one active controller manager.")

	opts := zap.Options{
		Development: true,
	}
	opts.BindFlags(flag.CommandLine)
	flag.Parse()

	ctrl.SetLogger(zap.New(zap.UseFlagOptions(&opts)))

	// Log image registry configuration
	imageRegistry := os.Getenv("IMAGE_REGISTRY")
	if imageRegistry == "" {
		imageRegistry = "build-harbor.alauda.cn/"
		setupLog.Info("Using default image registry", "registry", imageRegistry)
	} else {
		setupLog.Info("Using configured image registry", "registry", imageRegistry)
	}

	// Log storage class configuration
	if storageClass == "" {
		setupLog.Info("No storage class specified, reports will use emptyDir")
	} else {
		setupLog.Info("Using storage class for report persistence", "storageClass", storageClass)
	}

	// Setup signal handler once
	ctx := ctrl.SetupSignalHandler()

	mgr, err := ctrl.NewManager(ctrl.GetConfigOrDie(), ctrl.Options{
		Scheme:  scheme,
		Metrics: server.Options{BindAddress: metricsAddr},
		// WebhookServer:              webhook.NewServer(webhook.Options{Port: 9443}), // Temporarily disabled
		HealthProbeBindAddress:     probeAddr,
		LeaderElection:             enableLeaderElection,
		LeaderElectionID:           "compliance-operator-leader-election",
		LeaderElectionResourceLock: resourcelock.LeasesResourceLock,
	})
	if err != nil {
		setupLog.Error(err, "unable to start manager")
		os.Exit(1)
	}

	// Setup ProfileBundle controller
	if err = (&profilebundle.ProfileBundleReconciler{
		Client: mgr.GetClient(),
		Log:    ctrl.Log.WithName("controllers").WithName("ProfileBundle"),
		Scheme: mgr.GetScheme(),
	}).SetupWithManager(mgr); err != nil {
		setupLog.Error(err, "unable to create controller", "controller", "ProfileBundle")
		os.Exit(1)
	}

	// Setup Scan controller
	if err = (&scan.ScanReconciler{
		Client: mgr.GetClient(),
		Log:    ctrl.Log.WithName("controllers").WithName("Scan"),
		Scheme: mgr.GetScheme(),
	}).SetupWithManager(mgr); err != nil {
		setupLog.Error(err, "unable to create controller", "controller", "Scan")
		os.Exit(1)
	}

	// Setup ComplianceSuite controller
	if err = (&compliancesuite.ComplianceSuiteReconciler{
		Client: mgr.GetClient(),
		Log:    ctrl.Log.WithName("controllers").WithName("ComplianceSuite"),
		Scheme: mgr.GetScheme(),
	}).SetupWithManager(mgr); err != nil {
		setupLog.Error(err, "unable to create controller", "controller", "ComplianceSuite")
		os.Exit(1)
	}

	// Add health check endpoints
	if err := mgr.AddHealthzCheck("healthz", healthz.Ping); err != nil {
		setupLog.Error(err, "unable to set up health check")
		os.Exit(1)
	}
	if err := mgr.AddReadyzCheck("readyz", healthz.Ping); err != nil {
		setupLog.Error(err, "unable to set up ready check")
		os.Exit(1)
	}

	// Add a runnable to initialize report service after manager starts
	err = mgr.Add(&reportServiceRunnable{
		manager:          mgr,
		imageRegistry:    imageRegistry,
		setupLog:         setupLog,
		storageClassName: storageClass,
		storageSize:      storageSize,
	})
	if err != nil {
		setupLog.Error(err, "unable to add report service runnable")
		os.Exit(1)
	}

	setupLog.Info("starting manager")
	if err := mgr.Start(ctx); err != nil {
		setupLog.Error(err, "problem running manager")
		os.Exit(1)
	}
}

// reportServiceRunnable implements manager.Runnable to initialize report service after manager starts
type reportServiceRunnable struct {
	manager          ctrl.Manager
	imageRegistry    string
	storageClassName string
	storageSize      string
	setupLog         logr.Logger
}

func (r *reportServiceRunnable) Start(ctx context.Context) error {
	// Wait for manager to be ready
	select {
	case <-r.manager.Elected():
		// Manager is ready, now initialize report service
		reportServiceManager := reportservice.NewReportServiceManager(r.manager.GetClient(), r.imageRegistry, r.storageClassName, r.storageSize)
		if err := reportServiceManager.EnsureReportService(ctx); err != nil {
			r.setupLog.Error(err, "unable to initialize OpenSCAP report service")
			// Don't return error - report service is optional
			r.setupLog.Info("continuing without report service")
		} else {
			r.setupLog.Info("OpenSCAP report service initialized successfully")
		}
	case <-ctx.Done():
		return ctx.Err()
	}

	// Keep running until context is cancelled
	<-ctx.Done()
	return nil
}
