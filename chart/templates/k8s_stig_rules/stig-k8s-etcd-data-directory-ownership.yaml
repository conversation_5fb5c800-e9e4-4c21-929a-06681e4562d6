apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-etcd-data-directory-ownership
  namespace: compliance-system
spec:
  id: "V-242445"
  title: "Etcd data directory ownership verification"
  description: "The Kubernetes etcd key-value store provides a way to store data to the Control Plane. If these files can be changed, data to API object and Control Plane would be compromised."
  checkText: "Review the ownership of the Kubernetes etcd data directory by using the command:\n\nfind /var/lib/etcd -type f | xargs stat -c '%n %U:%G' | grep -v etcd:etcd\n\nIf the command returns any non etcd:etcd file permissions, this is a finding."
  fixText: "Change the ownership of the etcd data directory to etcd:etcd by executing the command:\n\nchown -R etcd:etcd /var/lib/etcd"
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash

    echo "Starting etcd data directory ownership verification (V-242445)"
    
    # Define etcd data directory (preserving original logic)
    etcd_dir="/host/var/lib/etcd"

    echo "Checking etcd data directory: $etcd_dir"

    # Check if etcd directory exists (preserving original logic)
    if [[ -d "$etcd_dir" ]]; then
        echo "Found etcd data directory"
        echo "Checking file ownership for all files in etcd directory..."
        
        invalid_files_found=false
        
        # Check ownership of all files (preserving original logic)
        while IFS= read -r file; do
            ownership=$(stat -c '%U:%G' "$file" 2>/dev/null | tr -d '\r')
            
            if [[ "$ownership" != "etcd:etcd" ]]; then
                echo "FAIL: Invalid ownership found"
                echo "File: $file"
                echo "Current ownership: $ownership"
                echo "Expected ownership: etcd:etcd"
                invalid_files_found=true
            fi
        done < <(find "$etcd_dir" -type f 2>/dev/null)
        
        # Final validation (preserving original logic)
        if [[ "$invalid_files_found" == "true" ]]; then
            echo "FAIL: etcd data directory has files with incorrect ownership"
            echo "All etcd files must be owned by etcd:etcd for security"
            exit 1
        else
            echo "PASS: All etcd files have proper ownership"
            exit 0
        fi
    else
        echo "FAIL: Etcd directory missing"
        echo "Expected to find etcd data directory at $etcd_dir"
        exit 1
    fi 