apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-etcd-peer-client-cert-auth
  namespace: compliance-system
spec:
  id: "V-242426"
  title: "Kubernetes etcd must enable client authentication to secure service."
  description: "Kubernetes container and pod configuration are maintained by Kubelet. Kubelet agents register nodes with the API Server, mount volume storage, and perform health checks for containers and pods. Anyone who gains access to Kubelet agents can effectively control applications within the pods and containers. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server with a means to be able to authenticate sessions and encrypt traffic.\n\nEtcd is a highly-available key value store used by Kubernetes deployments for persistent storage of all of its REST API objects. These objects are sensitive and should be accessible only by authenticated etcd peers in the etcd cluster. The parameter \"--peer-client-cert-auth\" must be set for etcd to check all incoming peer requests from the cluster for valid client certificates."
  checkText: "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\ngrep -i peer-client-cert-auth * \n\nIf the setting \"--peer-client-cert-auth\" is not configured in the Kubernetes etcd manifest file or set to \"false\", this is a finding."
  fixText: "Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane.\n\nSet the value of \"--peer-client-cert-auth\" to \"true\" for the etcd."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting etcd peer client certificate authentication check (V-242426)"
    
    # Define manifest path and required parameter (preserving original logic)
    manifest="/host/etc/kubernetes/manifests/etcd.yaml"
    required_param="peer-client-cert-auth=true"

    echo "Checking etcd manifest file: $manifest"

    # Check if manifest file exists (preserving original logic)
    if [[ -f "$manifest" ]]; then
        echo "Found etcd manifest file"
        echo "Checking for --peer-client-cert-auth=true parameter..."
        
        # Check if peer-client-cert-auth=true parameter exists (preserving original logic)
        if ! grep -q "$required_param" "$manifest" 2>/dev/null; then
            echo "FAIL: Missing --peer-client-cert-auth=true configuration"
            echo "etcd must have --peer-client-cert-auth set to true"
            echo "This ensures peer authentication for secure etcd cluster communication"
            exit 1
        else
            echo "Found --peer-client-cert-auth=true in configuration"
            echo "PASS: Etcd peer client certificate authentication properly configured"
            exit 0
        fi
    else
        echo "FAIL: Etcd manifest missing"
        echo "Expected to find etcd.yaml manifest file"
        exit 1
    fi 