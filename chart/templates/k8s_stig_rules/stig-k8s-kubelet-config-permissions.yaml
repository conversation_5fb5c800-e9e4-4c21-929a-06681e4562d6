apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-config-permissions
  namespace: compliance-system
spec:
  id: "V-242407"
  title: "The Kubernetes KubeletConfiguration files must have file permissions set to 644 or more restrictive."
  description: "Kubernetes KubeletConfiguration files must have restrictive file permissions to prevent unauthorized access."
  fixText: "On the Kubernetes Control Plane and Worker nodes, run the command:\nps -ef | grep kubelet\n\nCheck the config file (path identified by: --config):\n\nChange to the directory identified by --config (example /etc/sysconfig/) and run the command:\nchmod 644 kubelet\n\nTo verify the change took place, run the command:\nls -l kubelet\n\nThe kubelet file should now have the permissions of \"644\"."
  checkText: "On the Kubernetes Control Plane and Worker nodes, run the command:\nps -ef | grep kubelet\n\nCheck the config file (path identified by: --config):\n\nChange to the directory identified by --config (example /etc/sysconfig/) and run the command:\nls -l kubelet\n\nEach KubeletConfiguration file must have permissions of \"644\" or more restrictive.\n\nIf any KubeletConfiguration file is less restrictive than \"644\", this is a finding."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting kubelet configuration file permissions check (V-242407)"
    
    # Check kubelet process arguments (preserving original logic)
    echo "Checking kubelet process arguments..."
    kubelet_cmd=$(chroot /host ps -ef | grep kubelet | grep -v grep)
    
    if [[ -z "$kubelet_cmd" ]]; then
        echo "FAIL: Kubelet process not found"
        echo "Unable to locate running kubelet process"
        exit 1
    fi
    
    echo "Found kubelet process"
    
    # Get kubelet config path (preserving original logic)
    extracted_config=$(echo "$kubelet_cmd" | grep -o -- "--config=[^ ]*" | cut -d= -f2)
    
    if [[ -z "$extracted_config" ]]; then
        config_file="/host/var/lib/kubelet/config.yaml"
        echo "Using default config file: $config_file"
    else
        # Ensure config file has /host prefix if extracted from process
        if [[ ! "$extracted_config" =~ ^/host ]]; then
            config_file="/host$extracted_config"
        else
            config_file="$extracted_config"
        fi
        echo "Found config file from --config argument: $config_file"
    fi
    
    echo "Checking kubelet configuration file: $config_file"
    
    # Check if config file exists (preserving original logic)
    if [[ ! -f "$config_file" ]]; then
        echo "FAIL: Kubelet config file not found"
        echo "Configuration file does not exist at: $config_file"
        echo "Expected kubelet configuration file must exist for permissions verification"
        exit 1
    fi
    
    echo "Found kubelet configuration file"
    echo "Checking file permissions..."
    
    # Check file permissions (preserving original logic)
    perms=$(stat -c %a "$config_file" 2>/dev/null)
    
    if [[ -z "$perms" ]]; then
        echo "FAIL: Unable to read file permissions"
        echo "Cannot determine permissions for: $config_file"
        exit 1
    fi
    
    echo "Current file permissions: $perms"
    echo "Maximum allowed permissions: 644"
    
    # Validate permissions (preserving original logic)
    if [[ "$perms" -gt 644 ]]; then
        echo "FAIL: Invalid permissions"
        echo "File: $config_file"
        echo "Current permissions: $perms"
        echo "Maximum allowed permissions: 644"
        echo "Kubelet config file permissions must be 644 or more restrictive"
        exit 1
    else
        echo "PASS: Kubelet config has correct permissions"
        echo "File: $config_file"
        echo "Permissions: $perms (compliant)"
        exit 0
    fi 