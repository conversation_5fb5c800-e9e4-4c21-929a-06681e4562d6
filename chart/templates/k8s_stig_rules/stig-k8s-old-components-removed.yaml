apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-old-components-removed
  namespace: compliance-system
spec:
  id: "V-242442"
  title: "Kubernetes must remove old components after updated versions have been installed."
  description: "Previous versions of Kubernetes components that are not removed after updates have been installed may be exploited by adversaries by allowing the vulnerabilities to still exist within the cluster. It is important for Kubernetes to remove old pods when newer pods are created using new images to always be at the desired security state."
  checkText: "To view all pods and the images used to create the pods, from the Control Plane, run the following command:\nkubectl get pods --all-namespaces -o jsonpath=\"{..image}\" | \\\ntr -s '[[:space:]]' '\\n' | \\\nsort | \\\nuniq -c\n\nReview the images used for pods running within Kubernetes.\n\nIf there are multiple versions of the same image, this is a finding."
  fixText: "Remove any old pods that are using older images. On the Control Plane, run the command:\nkubectl delete pod podname\n(Note: \"podname\" is the name of the pod to delete.)"
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting V-242442: Kubernetes old components removal check"
    echo ""
    echo "MANUAL: This rule requires manual verification and cannot be automated"
    echo ""
    echo "Manual Verification Required:"
    echo "This check requires manual inspection of container images to identify duplicate versions"
    echo ""
    echo "Manual Verification Steps:"
    echo ""
    echo "1. List all container images in use:"
    echo "   kubectl get pods --all-namespaces -o jsonpath=\"{..image}\" | \\"
    echo "   tr -s '[[:space:]]' '\\n' | \\"
    echo "   sort | \\"
    echo "   uniq -c"
    echo ""
    echo "2. Review the output for multiple versions of the same image:"
    echo "   - Look for images with different version tags (e.g., app:v1.0, app:v1.1)"
    echo "   - Identify images with 'latest' and specific version tags"
    echo "   - Check for deprecated or old image versions"
    echo ""
    echo "3. Verification Criteria:"
    echo "   - Each application should use only one version of its container image"
    echo "   - Old/unused images should be removed from the cluster"
    echo "   - No pods should be running deprecated image versions"
    echo ""
    echo "4. Remediation (if needed):"
    echo "   - Delete pods using old images: kubectl delete pod <podname> -n <namespace>"
    echo "   - Update deployments to use latest approved image versions"
    echo "   - Remove unused images from container registry"
    echo ""
    echo "Finding Criteria:"
    echo "If multiple versions of the same image are found running simultaneously, this is a finding"
    echo ""
    echo "This check requires manual verification by authorized personnel"
    
    exit 2