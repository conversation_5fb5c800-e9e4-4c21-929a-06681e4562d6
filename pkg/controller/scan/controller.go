package scan

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"sort"
	"time"

	batchv1 "k8s.io/api/batch/v1"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/runtime"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
	"github.com/go-logr/logr"
	"github.com/robfig/cron/v3"
)

// ScanReconciler reconciles a Scan object
type ScanReconciler struct {
	client.Client
	Log                   logr.Logger
	Scheme                *runtime.Scheme
	reportDownloadService *ReportDownloadService
}

// +kubebuilder:rbac:groups=compliance-operator.alauda.io,resources=scans,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=compliance-operator.alauda.io,resources=scans/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=compliance-operator.alauda.io,resources=checkresults,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=batch,resources=jobs,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups="",resources=pods,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups="",resources=configmaps,verbs=get;list;watch;create;update;patch;delete

// Reconcile reads the state of the cluster for a Scan object and makes changes based on it
func (r *ScanReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	log := r.Log.WithValues("scan", req.NamespacedName)

	// Fetch the Scan instance
	var scan complianceapi.Scan
	if err := r.Get(ctx, req.NamespacedName, &scan); err != nil {
		if errors.IsNotFound(err) {
			// Object not found, return
			return ctrl.Result{}, nil
		}
		// Error reading the object - requeue the request
		return ctrl.Result{}, err
	}

	// Initialize scan status if needed
	if scan.Status.Phase == "" {
		// 使用带重试机制的辅助方法更新状态
		if _, err := r.updateScanStatus(ctx, &scan, "Pending", "", "Scan initialization"); err != nil {
			log.Error(err, "Failed to update Scan status")
			return ctrl.Result{}, err
		}
		return ctrl.Result{Requeue: true}, nil
	}

	// Check if this is a new scan execution (either initial or triggered by schedule)
	needsNewScanID := false

	// 检查是否需要生成新的 scanID
	if scan.Status.Phase == "Pending" {
		needsNewScanID = true
	} else if scan.Annotations != nil && scan.Annotations["compliance-operator.alauda.io/force-scan"] == "true" {
		// 强制扫描具有最高优先级，优先于定时任务检查
		needsNewScanID = true
		// 移除强制扫描注解
		scan.Annotations["compliance-operator.alauda.io/force-scan"] = "false"
		if err := r.Update(ctx, &scan); err != nil {
			log.Error(err, "Failed to update Scan annotations")
			return ctrl.Result{}, err
		}
		log.Info("Force scan triggered", "scan", scan.Name)
	} else if scan.Status.Phase == "Done" && scan.Spec.Schedule != "" {
		// 对于定时任务，检查是否到了下一次执行时间
		if scan.Status.EndTime != nil {
			lastExecTime := scan.Status.EndTime.Time
			schedule, err := cron.ParseStandard(scan.Spec.Schedule)
			if err != nil {
				log.Error(err, "Failed to parse schedule", "schedule", scan.Spec.Schedule)
			} else {
				nextExecTime := schedule.Next(lastExecTime)
				if time.Now().After(nextExecTime) {
					log.Info("Scheduled execution time reached", "lastExec", lastExecTime, "nextExec", nextExecTime)
					needsNewScanID = true
				}
			}
		}
	}

	// 如果需要新的 scanID，生成并存储
	if needsNewScanID {
		scanID := generateScanID(&scan)
		log.Info("Generated new scanID for scan execution", "scanID", scanID)

		// 将 scanID 存储在注解中
		if scan.Annotations == nil {
			scan.Annotations = make(map[string]string)
		}
		scan.Annotations["compliance-operator.alauda.io/current-scan-id"] = scanID

		// 更新 Scan 对象
		if err := r.Update(ctx, &scan); err != nil {
			log.Error(err, "Failed to update Scan with scanID")
			return ctrl.Result{}, err
		}

		// 重置扫描状态为 Launching，使用带重试机制的辅助方法
		if _, err := r.updateScanStatusWithStartTime(ctx, &scan, "Launching", "", "Starting scan execution"); err != nil {
			log.Error(err, "Failed to update Scan status")
			return ctrl.Result{}, err
		}

		return ctrl.Result{Requeue: true}, nil
	}

	// Handle different phases of the scan
	switch scan.Status.Phase {
	case "Launching":
		return r.initializeScan(ctx, &scan)
	case "Running":
		return r.checkScanProgress(ctx, &scan)
	case "Done", "Error":
		// If scan has a schedule, check if it's time to run again
		if scan.Spec.Schedule != "" {
			// Check if it's time to run again based on schedule
			if scan.Status.EndTime != nil {
				schedule, err := cron.ParseStandard(scan.Spec.Schedule)
				if err != nil {
					log.Error(err, "Failed to parse schedule", "schedule", scan.Spec.Schedule)
					return ctrl.Result{}, nil
				}

				nextRun := schedule.Next(scan.Status.EndTime.Time)
				now := time.Now()

				if now.After(nextRun) {
					// Time to run again
					log.Info("Scheduled scan time reached, restarting scan", "lastRun", scan.Status.EndTime.Time, "nextRun", nextRun)

					// Reset scan status to trigger a new run using helper method
					if _, err := r.updateScanStatus(ctx, &scan, "Pending", "", "Scheduled scan execution"); err != nil {
						log.Error(err, "Failed to update Scan status for scheduled run")
						return ctrl.Result{}, err
					}
					return ctrl.Result{Requeue: true}, nil
				} else {
					// Calculate delay until next run
					delay := nextRun.Sub(now)
					log.Info("Scheduling next scan run", "delay", delay, "nextRun", nextRun)
					return ctrl.Result{RequeueAfter: delay}, nil
				}
			}
		}

		// Clean up old historical results if needed
		if err := r.cleanupHistoricalResults(ctx, &scan); err != nil {
			log.Error(err, "Failed to cleanup historical results")
			// Continue anyway
		}

		return ctrl.Result{}, nil
	default:
		log.Info("Unknown scan phase", "phase", scan.Status.Phase)
		return ctrl.Result{}, nil
	}
}

// scheduleNextScan handles scheduled scan rescheduling
func (r *ScanReconciler) scheduleNextScan(ctx context.Context, scan *complianceapi.Scan) (ctrl.Result, error) {
	// For now, just requeue after a day for scheduled scans
	// In a real implementation, you would parse the cron schedule
	return ctrl.Result{RequeueAfter: 24 * time.Hour}, nil
}

// SetupWithManager sets up the controller with the Manager.
func (r *ScanReconciler) SetupWithManager(mgr ctrl.Manager) error {
	// Initialize report download service
	r.reportDownloadService = NewReportDownloadService(mgr.GetClient(), "")

	// 在controller启动时执行一次全面清理
	go func() {
		// 等待一段时间让controller完全启动
		time.Sleep(30 * time.Second)
		ctx := context.Background()
		log := r.Log.WithValues("task", "startup-cleanup")

		log.Info("Starting comprehensive cleanup on controller startup")
		if err := r.performStartupCleanup(ctx); err != nil {
			log.Error(err, "Failed to perform startup cleanup")
		} else {
			log.Info("Completed startup cleanup successfully")
		}
	}()

	return ctrl.NewControllerManagedBy(mgr).
		For(&complianceapi.Scan{}).
		Owns(&complianceapi.CheckResult{}).
		Owns(&batchv1.Job{}).
		Complete(r)
}

// GetLatestReportZip returns a zip file containing the latest scan reports
func (r *ScanReconciler) GetLatestReportZip(ctx context.Context, scanName, namespace string) ([]byte, error) {
	if r.reportDownloadService == nil {
		return nil, fmt.Errorf("report download service not initialized")
	}
	return r.reportDownloadService.GetLatestReportZip(ctx, scanName, namespace)
}

// performStartupCleanup 在controller启动时执行全面清理
func (r *ScanReconciler) performStartupCleanup(ctx context.Context) error {
	log := r.Log.WithValues("task", "startup-cleanup")

	// 获取所有的Scan资源
	var allScans complianceapi.ScanList
	if err := r.List(ctx, &allScans); err != nil {
		return fmt.Errorf("failed to list all scans: %v", err)
	}

	log.Info("Found scans for startup cleanup", "count", len(allScans.Items))

	// 对每个scan执行清理
	for i := range allScans.Items {
		scan := &allScans.Items[i]
		log.Info("Performing startup cleanup for scan", "scan", scan.Name, "namespace", scan.Namespace)

		if err := r.cleanupHistoricalResults(ctx, scan); err != nil {
			log.Error(err, "Failed to cleanup historical results for scan", "scan", scan.Name, "namespace", scan.Namespace)
			// 继续处理其他scan，不中断整个清理过程
		}

		// 清理可能存在的失败Job
		if err := r.cleanupFailedJobs(ctx, scan); err != nil {
			log.Error(err, "Failed to cleanup failed jobs for scan", "scan", scan.Name, "namespace", scan.Namespace)
		}

		// 短暂延迟，避免对API服务器造成过大压力
		time.Sleep(1 * time.Second)
	}

	return nil
}

// cleanupFailedJobs 清理失败或完成的扫描Jobs
func (r *ScanReconciler) cleanupFailedJobs(ctx context.Context, scan *complianceapi.Scan) error {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace)

	var jobs batchv1.JobList
	if err := r.List(ctx, &jobs, client.InNamespace(scan.Namespace), client.MatchingLabels{
		"compliance-operator.alauda.io/scan": scan.Name,
	}); err != nil {
		return fmt.Errorf("failed to list jobs: %v", err)
	}

	for i := range jobs.Items {
		job := &jobs.Items[i]

		// 清理超过24小时的已完成或失败的Job
		if time.Since(job.CreationTimestamp.Time) > 24*time.Hour {
			if (job.Status.Succeeded > 0) || (job.Status.Failed > 0) {
				log.Info("Deleting old completed/failed job", "job", job.Name, "age", time.Since(job.CreationTimestamp.Time))
				if err := r.Delete(ctx, job); err != nil && !errors.IsNotFound(err) {
					log.Error(err, "Failed to delete old job", "job", job.Name)
				}
			}
		}
	}

	return nil
}

// cleanupHistoricalResults 清理过期的历史扫描结果
func (r *ScanReconciler) cleanupHistoricalResults(ctx context.Context, scan *complianceapi.Scan) error {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace)

	// 如果未设置历史结果保留数量，则使用默认值
	maxHistoricalResults := 5
	if scan.Spec.MaxHistoricalResults > 0 {
		maxHistoricalResults = int(scan.Spec.MaxHistoricalResults)
	}

	log.Info("Starting comprehensive historical results cleanup", "maxHistoricalResults", maxHistoricalResults)

	if err := r.cleanupBasedOnActualResources(ctx, scan, maxHistoricalResults); err != nil {
		log.Error(err, "Failed to cleanup based on actual resources")
		// 继续执行其他清理方法
	}

	if err := r.cleanupBasedOnStatusHistory(ctx, scan, maxHistoricalResults); err != nil {
		log.Error(err, "Failed to cleanup based on status history")
		// 继续执行其他清理方法
	}

	if err := r.cleanupOrphanedResources(ctx, scan); err != nil {
		log.Error(err, "Failed to cleanup orphaned resources")
		// 不返回错误，因为这不应该影响主要功能
	}

	return nil
}

// cleanupBasedOnActualResources 基于实际Kubernetes资源进行清理（主要清理方法）
func (r *ScanReconciler) cleanupBasedOnActualResources(ctx context.Context, scan *complianceapi.Scan, maxHistoricalResults int) error {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace)

	// 1. 收集所有相关的CheckResult，按时间戳排序
	var allCheckResults complianceapi.CheckResultList
	if err := r.List(ctx, &allCheckResults, client.InNamespace(scan.Namespace), client.MatchingLabels{
		"compliance-operator.alauda.io/scan": scan.Name,
	}); err != nil {
		return fmt.Errorf("failed to list CheckResults: %v", err)
	}

	// 按创建时间排序，最新的在前
	sort.Slice(allCheckResults.Items, func(i, j int) bool {
		return allCheckResults.Items[i].CreationTimestamp.After(allCheckResults.Items[j].CreationTimestamp.Time)
	})

	// 如果CheckResult数量超过限制，删除最老的
	if len(allCheckResults.Items) > maxHistoricalResults {
		toDeleteCheckResults := allCheckResults.Items[maxHistoricalResults:]
		log.Info("Cleaning up excess CheckResults based on actual resources",
			"total", len(allCheckResults.Items), "toDelete", len(toDeleteCheckResults), "maxAllowed", maxHistoricalResults)

		for i := range toDeleteCheckResults {
			checkResult := &toDeleteCheckResults[i]
			scanID := checkResult.Labels["compliance-operator.alauda.io/scan-id"]
			log.Info("Deleting excess CheckResult", "checkResult", checkResult.Name, "scanID", scanID)
			if err := r.Delete(ctx, checkResult); err != nil && !errors.IsNotFound(err) {
				log.Error(err, "Failed to delete excess CheckResult", "checkResult", checkResult.Name)
			}
		}
	}

	// 2. 收集所有相关的报告ConfigMap，按时间戳排序
	var allReportCMs v1.ConfigMapList
	if err := r.List(ctx, &allReportCMs, client.InNamespace(scan.Namespace), client.MatchingLabels{
		"compliance-operator.alauda.io/scan":          scan.Name,
		"compliance-operator.alauda.io/resource-type": "report",
	}); err != nil {
		return fmt.Errorf("failed to list report ConfigMaps: %v", err)
	}

	// 按创建时间排序，最新的在前
	sort.Slice(allReportCMs.Items, func(i, j int) bool {
		return allReportCMs.Items[i].CreationTimestamp.After(allReportCMs.Items[j].CreationTimestamp.Time)
	})

	// 如果报告ConfigMap数量超过限制，删除最老的
	if len(allReportCMs.Items) > maxHistoricalResults {
		toDeleteReportCMs := allReportCMs.Items[maxHistoricalResults:]
		log.Info("Cleaning up excess report ConfigMaps based on actual resources",
			"total", len(allReportCMs.Items), "toDelete", len(toDeleteReportCMs), "maxAllowed", maxHistoricalResults)

		for i := range toDeleteReportCMs {
			configMap := &toDeleteReportCMs[i]
			scanID := configMap.Labels["compliance-operator.alauda.io/scan-id"]
			log.Info("Deleting excess report ConfigMap", "configMap", configMap.Name, "scanID", scanID)
			if err := r.Delete(ctx, configMap); err != nil && !errors.IsNotFound(err) {
				log.Error(err, "Failed to delete excess report ConfigMap", "configMap", configMap.Name)
			}

			// 同时清理对应的OpenSCAP报告
			if scanID != "" {
				if err := r.cleanupOpenSCAPReports(ctx, scan, scanID); err != nil {
					log.Error(err, "Failed to cleanup OpenSCAP reports for scanID", "scanID", scanID)
				}
			}
		}
	}

	// 3. 清理临时ConfigMap（这些通常应该立即清理）
	var allTempCMs v1.ConfigMapList
	if err := r.List(ctx, &allTempCMs, client.InNamespace(scan.Namespace), client.MatchingLabels{
		"compliance-operator.alauda.io/scan":      scan.Name,
		"compliance-operator.alauda.io/temporary": "true",
	}); err != nil {
		return fmt.Errorf("failed to list temporary ConfigMaps: %v", err)
	}

	// 临时ConfigMap应该被积极清理
	for i := range allTempCMs.Items {
		configMap := &allTempCMs.Items[i]
		// 只保留最近1小时内创建的临时ConfigMap
		if time.Since(configMap.CreationTimestamp.Time) > time.Hour {
			scanID := configMap.Labels["compliance-operator.alauda.io/scan-id"]
			log.Info("Deleting old temporary ConfigMap", "configMap", configMap.Name, "scanID", scanID, "age", time.Since(configMap.CreationTimestamp.Time))
			if err := r.Delete(ctx, configMap); err != nil && !errors.IsNotFound(err) {
				log.Error(err, "Failed to delete temporary ConfigMap", "configMap", configMap.Name)
			}
		}
	}

	return nil
}

// cleanupBasedOnStatusHistory 基于Status.HistoricalResults进行清理（备份清理方法）
func (r *ScanReconciler) cleanupBasedOnStatusHistory(ctx context.Context, scan *complianceapi.Scan, maxHistoricalResults int) error {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace)

	if scan.Status.HistoricalResults == nil || len(scan.Status.HistoricalResults) <= maxHistoricalResults {
		return nil
	}

	// 按时间排序历史记录，保留最新的
	historicalResults := make([]complianceapi.HistoricalResultRef, len(scan.Status.HistoricalResults))
	copy(historicalResults, scan.Status.HistoricalResults)

	sort.Slice(historicalResults, func(i, j int) bool {
		return historicalResults[i].Timestamp.After(historicalResults[j].Timestamp.Time)
	})

	// 需要删除的旧记录
	toDelete := historicalResults[maxHistoricalResults:]
	log.Info("Cleaning up based on status history", "total", len(historicalResults), "toDelete", len(toDelete))

	// 清理每个过期的扫描结果
	for _, historyRef := range toDelete {
		if err := r.cleanupScanResults(ctx, scan, historyRef.ScanID); err != nil {
			log.Error(err, "Failed to cleanup scan results from status history", "scanID", historyRef.ScanID)
			// 继续处理其他结果，不中断整个清理过程
		}
	}

	return nil
}

// cleanupScanResults 清理特定 scanID 的所有相关资源
func (r *ScanReconciler) cleanupScanResults(ctx context.Context, scan *complianceapi.Scan, scanID string) error {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace, "scanID", scanID)
	log.Info("Cleaning up scan results")

	// 1. 清理 CheckResult
	if err := r.cleanupCheckResultsByScanID(ctx, scan, scanID); err != nil {
		log.Error(err, "Failed to cleanup CheckResults")
		// 继续处理其他资源
	}

	// 2. 清理报告 ConfigMap
	if err := r.cleanupReportConfigMaps(ctx, scan, scanID); err != nil {
		log.Error(err, "Failed to cleanup report ConfigMaps")
		// 继续处理其他资源
	}

	// 3. 清理临时 ConfigMap
	if err := r.cleanupTemporaryConfigMaps(ctx, scan, scanID); err != nil {
		log.Error(err, "Failed to cleanup temporary ConfigMaps")
		// 继续处理其他资源
	}

	// 4. 清理 openscap-report-service 中的报告文件
	if err := r.cleanupOpenSCAPReports(ctx, scan, scanID); err != nil {
		log.Error(err, "Failed to cleanup OpenSCAP reports")
		// 继续处理其他资源
	}

	// 5. 清理 PVC 中的报告文件（如果使用 PVC 存储）
	if err := r.cleanupPVCReports(ctx, scan, scanID); err != nil {
		log.Error(err, "Failed to cleanup PVC reports")
		// 继续处理其他资源
	}

	log.Info("Completed cleanup for scanID", "scanID", scanID)
	return nil
}

// cleanupCheckResultsByScanID 清理特定 scanID 的 CheckResult
func (r *ScanReconciler) cleanupCheckResultsByScanID(ctx context.Context, scan *complianceapi.Scan, scanID string) error {
	var results complianceapi.CheckResultList
	if err := r.List(ctx, &results, client.InNamespace(scan.Namespace), client.MatchingLabels{
		"compliance-operator.alauda.io/scan":    scan.Name,
		"compliance-operator.alauda.io/scan-id": scanID,
	}); err != nil {
		return fmt.Errorf("failed to list CheckResults: %v", err)
	}

	for i := range results.Items {
		result := &results.Items[i]
		r.Log.Info("Deleting CheckResult", "checkResult", result.Name, "scanID", scanID)
		if err := r.Delete(ctx, result); err != nil && !errors.IsNotFound(err) {
			r.Log.Error(err, "Failed to delete CheckResult", "checkResult", result.Name)
		}
	}

	return nil
}

// cleanupReportConfigMaps 清理特定 scanID 的报告 ConfigMap
func (r *ScanReconciler) cleanupReportConfigMaps(ctx context.Context, scan *complianceapi.Scan, scanID string) error {
	var configMaps v1.ConfigMapList
	if err := r.List(ctx, &configMaps, client.InNamespace(scan.Namespace), client.MatchingLabels{
		"compliance-operator.alauda.io/scan":          scan.Name,
		"compliance-operator.alauda.io/scan-id":       scanID,
		"compliance-operator.alauda.io/resource-type": "report",
	}); err != nil {
		return fmt.Errorf("failed to list report ConfigMaps: %v", err)
	}

	for i := range configMaps.Items {
		configMap := &configMaps.Items[i]
		r.Log.Info("Deleting report ConfigMap", "configMap", configMap.Name, "scanID", scanID)
		if err := r.Delete(ctx, configMap); err != nil && !errors.IsNotFound(err) {
			r.Log.Error(err, "Failed to delete report ConfigMap", "configMap", configMap.Name)
		}
	}

	return nil
}

// cleanupTemporaryConfigMaps 清理特定 scanID 的临时 ConfigMap
func (r *ScanReconciler) cleanupTemporaryConfigMaps(ctx context.Context, scan *complianceapi.Scan, scanID string) error {
	var configMaps v1.ConfigMapList
	if err := r.List(ctx, &configMaps, client.InNamespace(scan.Namespace), client.MatchingLabels{
		"compliance-operator.alauda.io/scan":      scan.Name,
		"compliance-operator.alauda.io/scan-id":   scanID,
		"compliance-operator.alauda.io/temporary": "true",
	}); err != nil {
		return fmt.Errorf("failed to list temporary ConfigMaps: %v", err)
	}

	for i := range configMaps.Items {
		configMap := &configMaps.Items[i]
		r.Log.Info("Deleting temporary ConfigMap", "configMap", configMap.Name, "scanID", scanID)
		if err := r.Delete(ctx, configMap); err != nil && !errors.IsNotFound(err) {
			r.Log.Error(err, "Failed to delete temporary ConfigMap", "configMap", configMap.Name)
		}
	}

	return nil
}

// cleanupOpenSCAPReports 清理 openscap-report-service 中的报告文件
func (r *ScanReconciler) cleanupOpenSCAPReports(ctx context.Context, scan *complianceapi.Scan, scanID string) error {
	// 构建 openscap-report-service 的服务地址
	serviceURL := "http://openscap-report-service.compliance-system.svc.cluster.local:8080"

	// 检查是否有环境变量覆盖
	if url := os.Getenv("OPENSCAP_REPORT_SERVICE_URL"); url != "" {
		serviceURL = url
	}

	deleteURL := fmt.Sprintf("%s/delete/%s", serviceURL, scanID)

	// 创建 HTTP DELETE 请求
	req, err := http.NewRequestWithContext(ctx, "DELETE", deleteURL, nil)
	if err != nil {
		return fmt.Errorf("failed to create delete request: %v", err)
	}

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to delete reports from service: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		// 报告不存在，这是正常的
		r.Log.V(1).Info("Reports not found in service", "scanID", scanID)
		return nil
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to delete reports, status: %d", resp.StatusCode)
	}

	r.Log.Info("Successfully deleted reports from openscap-report-service", "scanID", scanID)
	return nil
}

// cleanupPVCReports 清理 PVC 中的报告文件
func (r *ScanReconciler) cleanupPVCReports(ctx context.Context, scan *complianceapi.Scan, scanID string) error {
	// TODO: 实现 PVC 报告文件清理
	// 这需要创建一个 Job 来挂载 PVC 并删除对应的报告文件
	r.Log.V(1).Info("PVC report cleanup not implemented yet", "scanID", scanID)
	return nil
}

// cleanupOrphanedResources 清理可能遗漏的孤立资源
func (r *ScanReconciler) cleanupOrphanedResources(ctx context.Context, scan *complianceapi.Scan) error {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace)

	// 获取当前扫描的有效 scanID 列表
	validScanIDs := make(map[string]bool)
	if scan.Annotations != nil && scan.Annotations["compliance-operator.alauda.io/current-scan-id"] != "" {
		validScanIDs[scan.Annotations["compliance-operator.alauda.io/current-scan-id"]] = true
	}
	if scan.Status.HistoricalResults != nil {
		for _, historyRef := range scan.Status.HistoricalResults {
			validScanIDs[historyRef.ScanID] = true
		}
	}

	// 查找所有与此扫描相关的资源
	var allConfigMaps v1.ConfigMapList
	if err := r.List(ctx, &allConfigMaps, client.InNamespace(scan.Namespace), client.MatchingLabels{
		"compliance-operator.alauda.io/scan": scan.Name,
	}); err != nil {
		return fmt.Errorf("failed to list all ConfigMaps: %v", err)
	}

	// 删除没有有效 scanID 的孤立 ConfigMap
	for i := range allConfigMaps.Items {
		configMap := &allConfigMaps.Items[i]
		scanID := configMap.Labels["compliance-operator.alauda.io/scan-id"]

		if scanID != "" && !validScanIDs[scanID] {
			log.Info("Deleting orphaned ConfigMap", "configMap", configMap.Name, "scanID", scanID)
			if err := r.Delete(ctx, configMap); err != nil && !errors.IsNotFound(err) {
				log.Error(err, "Failed to delete orphaned ConfigMap", "configMap", configMap.Name)
			}
		}
	}

	return nil
}
