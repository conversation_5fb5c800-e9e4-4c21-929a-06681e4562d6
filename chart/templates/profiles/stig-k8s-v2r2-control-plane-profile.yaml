---
# STIG Kubernetes V2R2 Control Plane Profile
# Contains controller manager, scheduler, and etcd security checks
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: stig-k8s-v2r2-control-plane
  namespace: {{ .Values.global.complianceNamespace }}
spec:
  id: "STIG-K8S-V2R2-CONTROL-PLANE"
  title: "STIG Kubernetes V2R2 Control Plane Security Profile"
  description: |
    This profile contains control plane security checks based on the 
    Security Technical Implementation Guide (STIG) for Kubernetes V2R2.
    Includes controller manager, scheduler, and etcd configurations.
  version: "1.0"
  rules:
    - name: stig-k8s-controller-manager-pps
    - name: stig-k8s-controller-manager-profiling-disabled
    - name: stig-k8s-controller-manager-root-ca-file
    - name: stig-k8s-controller-manager-secure-binding
    - name: stig-k8s-controller-manager-tls-min-version
    - name: stig-k8s-controller-manager-use-service-account-credentials
    - name: stig-k8s-scheduler-pps
    - name: stig-k8s-scheduler-secure-binding
    - name: stig-k8s-scheduler-tls-min-version
    - name: stig-k8s-etcd-auto-tls-disabled
    - name: stig-k8s-etcd-cert-file
    - name: stig-k8s-etcd-client-cert-auth
    - name: stig-k8s-etcd-data-directory-ownership
    - name: stig-k8s-etcd-file-permissions
    - name: stig-k8s-etcd-key-file
    - name: stig-k8s-etcd-peer-auto-tls-disabled
    - name: stig-k8s-etcd-peer-cert-file
    - name: stig-k8s-etcd-peer-client-cert-auth
    - name: stig-k8s-etcd-peer-key-file
    - name: stig-k8s-etcd-pps 