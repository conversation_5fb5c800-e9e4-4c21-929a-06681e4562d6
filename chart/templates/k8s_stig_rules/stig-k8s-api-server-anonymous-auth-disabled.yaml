apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-anonymous-auth-disabled
  namespace: compliance-system
spec:
  id: "V-242390"
  title: "The Kubernetes API server must have anonymous authentication disabled."
  description: "The Kubernetes API Server controls Kubernetes via an API interface. A user who has access to the API essentially has root access to the entire Kubernetes cluster. To control access, users must be authenticated and authorized. By allowing anonymous connections, the controls put in place to secure the API can be bypassed.\n\nSetting \"--anonymous-auth\" to \"false\" also disables unauthenticated requests from kubelets.\n\nWhile there are instances where anonymous connections may be needed (e.g., health checks) and Role-Based Access Controls (RBACs) are in place to limit the anonymous access, this access should be disabled, and only enabled when necessary."
  checkText: "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i anonymous-auth * \n\nIf the setting \"--anonymous-auth\" is set to \"true\" in the Kubernetes API Server manifest file, this is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of  \"--anonymous-auth\" to \"false\"."
  severity: "high"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Checking API Server anonymous authentication configuration..."
    
    # Initialize variables
    result="PASSED"
    details=""
    manifest_file="/host/etc/kubernetes/manifests/kube-apiserver.yaml"

    echo "Checking API Server manifest: $manifest_file"
    
    if [[ ! -f "$manifest_file" ]]; then
        result="FAILED"
        details="API Server manifest missing"
        echo "FAIL: $details"
    else
        echo "Found API Server manifest file"
        
        # Search for anonymous-auth parameter in the manifest
        auth_line=$(grep -E -- "anonymous-auth(=[^[:space:]]+| +[^[:space:]]+)" "$manifest_file" 2>/dev/null)
        
        if [[ -n "$auth_line" ]]; then
            echo "Found anonymous-auth configuration: $auth_line"
            auth_value=$(echo "$auth_line" | awk -F'[= ]+' '{print $2}')
            echo "Anonymous auth value: $auth_value"
            
            if [[ "$auth_value" == "true" ]]; then
                result="FAILED"
                details="Anonymous authentication enabled (--anonymous-auth=true)"
                echo "VIOLATION: Anonymous authentication is enabled"
            else
                echo "OK: Anonymous authentication is disabled"
        fi
      else
            result="FAILED"
            details="Anonymous auth parameter not explicitly configured"
            echo "VIOLATION: Anonymous auth parameter not explicitly configured"
        fi
    fi

    if [[ "$result" == "PASSED" ]]; then
        details="Anonymous authentication properly disabled"
        echo "PASS: $details"
        exit 0
    else
        echo "FAIL: $details"
        exit 1
    fi
    