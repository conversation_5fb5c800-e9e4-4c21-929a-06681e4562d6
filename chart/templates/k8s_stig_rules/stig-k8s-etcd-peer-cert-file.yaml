apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-etcd-peer-cert-file
  namespace: compliance-system
spec:
  id: "V-242432"
  title: "Etcd must configure peer-cert-file"
  description: "The Kubernetes etcd key-value store provides a way to store data to the Control Plane. If these files can be changed, data to API object and Control Plane would be compromised."
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i peer-cert-file etcd.yaml \n\nIf the setting \"peer-cert-file\" is not set in the Kubernetes etcd manifest file, this is a finding."
  fixText: "Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument \"--peer-cert-file\" to the appropriate certificate file."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting etcd peer-cert-file configuration check (V-242432)"
    
    # Use /host prefix for host filesystem access
    manifest="/host/etc/kubernetes/manifests/etcd.yaml"
    required_param="\-\-peer-cert-file"

    echo "Checking etcd manifest file: $manifest"

    if [[ -f "$manifest" ]]; then
        echo "Found etcd manifest file"
        echo "Checking for --peer-cert-file parameter..."
        
        if ! grep -q "$required_param" "$manifest"; then
            echo "FAIL: Missing --peer-cert-file configuration"
            echo "etcd must have --peer-cert-file parameter configured"
            exit 1
        else
            echo "Found --peer-cert-file parameter in manifest"
            cert_path=$(grep -oP "$required_param=\K[^ ]+" "$manifest")
            
            if [[ -n "$cert_path" ]]; then
                echo "Certificate file path: $cert_path"
                # Add /host prefix for host file validation
                if [[ ! -f "/host$cert_path" ]]; then
                    echo "FAIL: Certificate file not found: $cert_path"
                    echo "The specified certificate file does not exist on the filesystem"
                    exit 1
                else
                    echo "Found certificate file at: $cert_path"
                fi
            fi
            
            echo "PASS: Etcd peer-cert-file properly configured"
            echo "etcd peer-cert-file parameter is configured and certificate file exists"
            exit 0
        fi
    else
        echo "FAIL: Etcd manifest missing"
        echo "Expected to find etcd.yaml manifest file"
        exit 1
    fi