apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-cipher-suites
  namespace: compliance-system
spec:
  id: "V-242418"
  title: "The Kubernetes API server must use approved cipher suites."
  description: "The Kubernetes API server communicates to the kubelet service on the nodes to deploy, update, and delete resources. If an attacker were able to get between this communication and modify the request, the Kubernetes cluster could be compromised. Using approved cypher suites for the communication ensures the protection of the transmitted information, confidentiality, and integrity so that the attacker cannot read or alter this communication."
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i tls-cipher-suites *\n\nIf the setting feature tls-cipher-suites is not set in the Kubernetes API server manifest file or contains no value or does not contain TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384, this is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--tls-cipher-suites\" to:\n\"TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384\""
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Checking API Server TLS cipher suites configuration..."
    manifests_dir="/host/etc/kubernetes/manifests"
    required_ciphers="TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384"
    echo "Searching for API Server manifest files in: $manifests_dir"
    
    # Check if manifests directory exists
    if [[ ! -d "$manifests_dir" ]]; then
        echo "ERROR: Kubernetes manifests directory not found: $manifests_dir"
        echo "FAIL: Manifests directory missing"
        exit 1
    fi
    
    echo "Found manifests directory"
    echo "Required cipher suites: $required_ciphers"
    
    # Find API Server manifest files
    api_files=$(grep -lis 'kube-apiserver' "$manifests_dir"/* 2>/dev/null)
    
    if [[ -z "$api_files" ]]; then
        echo "ERROR: No API Server manifest files found in $manifests_dir"
        echo "FAIL: No API Server manifest found"
        exit 1
    fi
    
    echo "Found API Server manifest files: $api_files"
    
    # Check each API Server manifest file for cipher suites
    all_configured=true
    violation_files=""
    
    for file in $api_files; do
        echo "Checking file: $(basename "$file")"
        
        # Get TLS cipher suites configuration
        cipher_line=$(grep -i -- "--tls-cipher-suites" "$file" 2>/dev/null)
        
        if [[ -z "$cipher_line" ]]; then
            echo "VIOLATION: tls-cipher-suites not configured in $(basename "$file")"
            all_configured=false
            if [[ -n "$violation_files" ]]; then
                violation_files="${violation_files}, $(basename "$file")"
            else
                violation_files="$(basename "$file")"
            fi
        else
            echo "Found TLS cipher suites configuration:"
            echo "  $cipher_line"
            
            # Extract cipher suites value
            current_ciphers=$(echo "$cipher_line" | awk -F= '{print $2}' | tr -d '\r' | tr -d ' ')
            echo "Current cipher suites: $current_ciphers"
            
            # Check each required cipher
            missing_ciphers=""
            for cipher in ${required_ciphers//,/ }; do
                if [[ ! "$current_ciphers" =~ "$cipher" ]]; then
                    if [[ -n "$missing_ciphers" ]]; then
                        missing_ciphers="${missing_ciphers}, $cipher"
                    else
                        missing_ciphers="$cipher"
                    fi
                fi
            done
            
            if [[ -n "$missing_ciphers" ]]; then
                echo "VIOLATION: Missing required cipher suites in $(basename "$file")"
                echo "  Missing: $missing_ciphers"
                all_configured=false
                if [[ -n "$violation_files" ]]; then
                    violation_files="${violation_files}, $(basename "$file")"
                else
                    violation_files="$(basename "$file")"
                fi
            else
                echo "OK: All required cipher suites found in $(basename "$file")"
            fi
        fi
    done
    
    # Final result
    if [[ "$all_configured" == false ]]; then
        echo "FAIL: Invalid cipher suites configuration in: $violation_files"
        exit 1
    fi
    
    echo "OK: All API Server manifests have approved cipher suites configured"
    echo "PASS: Approved cipher suites configured"
    exit 0 