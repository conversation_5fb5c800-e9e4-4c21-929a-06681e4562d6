package scan

import (
	"bytes"
	"context"
	"fmt"
	"html/template"
	"sort"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

// ClusterInfo contains cluster information for the report
type ClusterInfo struct {
	Version    string
	NodeCount  int
	Nodes      []NodeInfo
	Runtime    string
	MasterNode string
}

// NodeInfo contains individual node information
type NodeInfo struct {
	Name          string
	IP            string
	Role          string
	Version       string
	Runtime       string
	OSImage       string
	KernelVersion string
}

// ReportData contains all data needed for report generation
type ReportData struct {
	Scan             *complianceapi.Scan
	Profile          *complianceapi.Profile
	AggregatedResult *complianceapi.CheckResult
	Stats            complianceapi.ScanStats
	GeneratedAt      time.Time
	ClusterInfo      ClusterInfo
	RuleDetails      map[string]RuleDetail
}

// RuleDetail contains detailed rule information
type RuleDetail struct {
	ID          string
	Title       string
	Description string
	CheckText   string
	FixText     string
	Severity    string
	CheckType   string
}

// generateReportHTML generates an HTML report for the scan
func (r *ScanReconciler) generateReportHTML(ctx context.Context, scan *complianceapi.Scan, stats complianceapi.ScanStats) (string, error) {
	r.Log.Info("Generating HTML report", "scan", scan.Name)

	// Get profile information
	var profile complianceapi.Profile
	if err := r.Get(ctx, client.ObjectKey{
		Name:      scan.Spec.Profile,
		Namespace: scan.Namespace,
	}, &profile); err != nil {
		r.Log.Error(err, "Failed to get profile for report")
		// Continue without profile info
	}

	// Get cluster information
	clusterInfo, err := r.getClusterInfo(ctx)
	if err != nil {
		r.Log.Error(err, "Failed to get cluster info, using defaults")
		clusterInfo = ClusterInfo{
			Version:   "Unknown",
			NodeCount: 0,
			Runtime:   "Unknown",
		}
	}

	// 获取最新的 CheckResult
	var aggregatedResult *complianceapi.CheckResult

	if scan.Status.LatestResult != nil && scan.Status.LatestResult.CheckResultName != "" {
		// 使用最新结果引用
		var result complianceapi.CheckResult
		if err := r.Get(ctx, types.NamespacedName{
			Name:      scan.Status.LatestResult.CheckResultName,
			Namespace: scan.Namespace,
		}, &result); err != nil {
			r.Log.Error(err, "Failed to get latest CheckResult by name, falling back to search",
				"checkResult", scan.Status.LatestResult.CheckResultName)
		} else {
			aggregatedResult = &result
			r.Log.Info("Found CheckResult by reference", "checkResult", result.Name)
		}
	}

	// 如果没有通过引用找到，尝试通过标签查找
	if aggregatedResult == nil {
		var checkResults complianceapi.CheckResultList
		if err := r.List(ctx, &checkResults, client.InNamespace(scan.Namespace),
			client.MatchingLabels{"compliance-operator.alauda.io/scan": scan.Name}); err != nil {
			return "", fmt.Errorf("failed to get check results: %v", err)
		}

		if len(checkResults.Items) > 0 {
			// 按时间排序，获取最新的
			sort.Slice(checkResults.Items, func(i, j int) bool {
				return checkResults.Items[i].Spec.Timestamp.After(checkResults.Items[j].Spec.Timestamp.Time)
			})
			aggregatedResult = &checkResults.Items[0]
			r.Log.Info("Found CheckResult by search", "checkResult", aggregatedResult.Name)
		} else {
			return "", fmt.Errorf("no check results found for scan %s", scan.Name)
		}
	}

	// Get rule details for all rules in the results
	ruleDetails, err := r.getRuleDetails(ctx, aggregatedResult.Spec.RuleResults, scan.Namespace)
	if err != nil {
		r.Log.Error(err, "Failed to get rule details")
		ruleDetails = make(map[string]RuleDetail)
	}

	// Sort rule results by severity and ID
	sort.Slice(aggregatedResult.Spec.RuleResults, func(i, j int) bool {
		severityOrder := map[string]int{"high": 0, "medium": 1, "low": 2}
		iSev := severityOrder[strings.ToLower(aggregatedResult.Spec.RuleResults[i].Severity)]
		jSev := severityOrder[strings.ToLower(aggregatedResult.Spec.RuleResults[j].Severity)]
		if iSev != jSev {
			return iSev < jSev
		}
		return aggregatedResult.Spec.RuleResults[i].RuleID < aggregatedResult.Spec.RuleResults[j].RuleID
	})

	// Prepare report data
	reportData := ReportData{
		Scan:             scan,
		Profile:          &profile,
		AggregatedResult: aggregatedResult,
		Stats:            stats,
		GeneratedAt:      time.Now(),
		ClusterInfo:      clusterInfo,
		RuleDetails:      ruleDetails,
	}

	// Generate HTML using template
	htmlContent, err := r.renderReportTemplate(reportData)
	if err != nil {
		return "", fmt.Errorf("failed to render report template: %v", err)
	}

	return htmlContent, nil
}

// getClusterInfo retrieves cluster information
func (r *ScanReconciler) getClusterInfo(ctx context.Context) (ClusterInfo, error) {
	var clusterInfo ClusterInfo

	// Get all nodes
	var nodeList corev1.NodeList
	if err := r.List(ctx, &nodeList); err != nil {
		return clusterInfo, fmt.Errorf("failed to list nodes: %v", err)
	}

	clusterInfo.NodeCount = len(nodeList.Items)
	clusterInfo.Nodes = make([]NodeInfo, 0, len(nodeList.Items))

	for _, node := range nodeList.Items {
		nodeInfo := NodeInfo{
			Name:          node.Name,
			Version:       node.Status.NodeInfo.KubeletVersion,
			Runtime:       node.Status.NodeInfo.ContainerRuntimeVersion,
			OSImage:       node.Status.NodeInfo.OSImage,
			KernelVersion: node.Status.NodeInfo.KernelVersion,
		}

		// Get node IP
		for _, addr := range node.Status.Addresses {
			if addr.Type == corev1.NodeInternalIP {
				nodeInfo.IP = addr.Address
				break
			}
		}

		// Determine node role
		if _, exists := node.Labels["node-role.kubernetes.io/control-plane"]; exists {
			nodeInfo.Role = "control-plane"
			if clusterInfo.MasterNode == "" {
				clusterInfo.MasterNode = node.Name
			}
		} else if _, exists := node.Labels["node-role.kubernetes.io/master"]; exists {
			nodeInfo.Role = "master"
			if clusterInfo.MasterNode == "" {
				clusterInfo.MasterNode = node.Name
			}
		} else {
			nodeInfo.Role = "worker"
		}

		clusterInfo.Nodes = append(clusterInfo.Nodes, nodeInfo)

		// Set cluster version from first node if not set
		if clusterInfo.Version == "" {
			clusterInfo.Version = node.Status.NodeInfo.KubeletVersion
		}

		// Set cluster runtime from first node if not set
		if clusterInfo.Runtime == "" {
			clusterInfo.Runtime = node.Status.NodeInfo.ContainerRuntimeVersion
		}
	}

	return clusterInfo, nil
}

// getRuleDetails retrieves detailed information for all rules
func (r *ScanReconciler) getRuleDetails(ctx context.Context, ruleResults []complianceapi.RuleResult, namespace string) (map[string]RuleDetail, error) {
	ruleDetails := make(map[string]RuleDetail)

	for _, ruleResult := range ruleResults {
		var rule complianceapi.Rule
		if err := r.Get(ctx, types.NamespacedName{
			Name:      ruleResult.RuleName,
			Namespace: namespace,
		}, &rule); err != nil {
			r.Log.Error(err, "Failed to get rule details", "rule", ruleResult.RuleName)
			// Create default rule detail
			ruleDetails[ruleResult.RuleName] = RuleDetail{
				ID:          ruleResult.RuleID,
				Title:       ruleResult.RuleName,
				Description: "Description not available",
				CheckText:   "Check text not available",
				FixText:     "Fix text not available",
				Severity:    ruleResult.Severity,
				CheckType:   ruleResult.CheckType,
			}
			continue
		}

		ruleDetails[ruleResult.RuleName] = RuleDetail{
			ID:          rule.Spec.ID, // Use STIG ID from spec.id field
			Title:       rule.Spec.Title,
			Description: rule.Spec.Description,
			CheckText:   rule.Spec.CheckText,
			FixText:     rule.Spec.FixText,
			Severity:    rule.Spec.Severity,
			CheckType:   rule.Spec.CheckType,
		}
	}

	return ruleDetails, nil
}

// renderReportTemplate renders the HTML report using Go templates
func (r *ScanReconciler) renderReportTemplate(data ReportData) (string, error) {
	tmpl := `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Compliance Scan Report - {{.Scan.Name}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            min-height: 100vh;
        }
        
        /* STIG-inspired header with gradient */
        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
        
        .header-content {
            position: relative;
            z-index: 1;
        }
        
        .header h1 {
            font-size: 3em;
            font-weight: 300;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            font-weight: 300;
        }
        
        /* Overview section */
        .overview {
            padding: 40px 30px;
            background: linear-gradient(to bottom, #fff 0%, #f8f9fa 100%);
        }
        
        .section-title {
            font-size: 2em;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 30px;
            border-bottom: 3px solid #4f46e5;
            padding-bottom: 10px;
        }
        
        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .overview-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .overview-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.1);
        }
        
        .overview-card h3 {
            color: #4f46e5;
            font-size: 1.3em;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .overview-card .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .overview-card .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 500;
            color: #6b7280;
        }
        
        .info-value {
            font-weight: 600;
            color: #1f2937;
        }
        
        /* Stats section */
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px 20px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }
        
        .stat-card.total::before { background: #6b7280; }
        .stat-card.pass::before { background: #10b981; }
        .stat-card.fail::before { background: #ef4444; }
        .stat-card.error::before { background: #f59e0b; }
        .stat-card.manual::before { background: #8b5cf6; }
        .stat-card.na::before { background: #6b7280; }
        
        .stat-card h4 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .stat-card.total h4 { color: #6b7280; }
        .stat-card.pass h4 { color: #10b981; }
        .stat-card.fail h4 { color: #ef4444; }
        .stat-card.error h4 { color: #f59e0b; }
        .stat-card.manual h4 { color: #8b5cf6; }
        .stat-card.na h4 { color: #6b7280; }
        
        .stat-card p {
            color: #6b7280;
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.9em;
            letter-spacing: 0.5px;
        }
        
        /* Findings section */
        .findings {
            padding: 40px 30px;
            background: #f8f9fa;
        }
        
        .finding-item {
            background: white;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            overflow: hidden;
            transition: box-shadow 0.3s ease;
        }
        
        .finding-item:hover {
            box-shadow: 0 4px 20px rgba(0,0,0,0.12);
        }
        
        .finding-header {
            padding: 20px 25px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e5e7eb 100%);
            border-bottom: 1px solid #e5e7eb;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .finding-header:hover {
            background: linear-gradient(135deg, #f3f4f6 0%, #d1d5db 100%);
        }
        
        .finding-title-section {
            flex: 1;
        }
        
        .finding-summary {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .finding-id {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background: #4f46e5;
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 0.9em;
        }
        
        .finding-title {
            font-size: 1.1em;
            font-weight: 600;
            color: #1f2937;
            flex: 1;
            min-width: 200px;
        }
        
        .finding-meta {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .severity-badge, .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .severity-high { background: #fecaca; color: #991b1b; }
        .severity-medium { background: #fed7aa; color: #9a3412; }
        .severity-low { background: #bbf7d0; color: #166534; }
        
        .severity-text {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        /* Severity tag colors */
        .severity-text.severity-high { 
            background: #fecaca; 
            color: #991b1b; 
        }
        .severity-text.severity-medium { 
            background: #fed7aa; 
            color: #9a3412; 
        }
        .severity-text.severity-low { 
            background: #bbf7d0; 
            color: #166534; 
        }
        .severity-text.severity-critical { 
            background: #fca5a5; 
            color: #7f1d1d; 
        }
        .severity-text.severity-info { 
            background: #dbeafe; 
            color: #1e40af; 
        }
        
        .status-pass { background: #dcfce7; color: #166534; }
        .status-fail { background: #fecaca; color: #991b1b; }
        .status-error { background: #fed7aa; color: #9a3412; }
        .status-manual { background: #e0e7ff; color: #3730a3; }
        .status-not-applicable { background: #f3f4f6; color: #374151; }
        
        .result-badge {
            background: none !important;
            border: none !important;
            padding: 0 !important;
            border-radius: 0 !important;
            font-size: 0.9em !important;
            font-weight: 700 !important;
            text-transform: uppercase !important;
            letter-spacing: 0.5px !important;
        }
        
        /* 确保颜色优先级最高 */
        span.result-badge.result-pass { 
            color: #16a34a !important; /* 绿色 */
        }
        span.result-badge.result-fail { 
            color: #dc2626 !important; /* 红色 */
        }
        span.result-badge.result-error { 
            color: #ea580c !important; /* 橙色 */
        }
        span.result-badge.result-manual { 
            color: #4338ca !important; /* 蓝色 */
        }
        span.result-badge.result-notapplicable { 
            color: #6b7280 !important; /* 灰色 */
        }
        
        .expand-icon {
            font-size: 1.2em;
            transition: transform 0.3s ease;
            color: #6b7280;
        }
        
        .finding-header.expanded .expand-icon {
            transform: rotate(180deg);
        }
        
        .finding-content {
            display: none;
            padding: 25px;
            background: white;
        }
        
        .finding-content.expanded {
            display: block;
        }
        
        .finding-details {
            display: grid;
            gap: 20px;
        }
        
        .detail-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #4f46e5;
        }
        
        .detail-section h4 {
            color: #4f46e5;
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .detail-section p {
            line-height: 1.6;
            color: #374151;
        }
        
        .detail-section pre, .evidence-text {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
            overflow-x: auto;
            font-size: 0.9em;
            line-height: 1.4;
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        /* Node results table */
        .node-results {
            margin-top: 20px;
        }
        
        .node-results-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .node-results-table th {
            background: #f8f9fa;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .node-results-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #f3f4f6;
            vertical-align: top;
        }
        
        .node-results-table tr:hover {
            background: #f9fafb;
        }
        
        .node-name {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-weight: 600;
            color: #4f46e5;
        }
        
        /* Footer */
        .footer {
            background: #1f2937;
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .footer p {
            opacity: 0.8;
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .container { margin: 0; }
            .header, .overview, .findings { padding: 20px; }
            .header h1 { font-size: 2em; }
            .overview-grid { grid-template-columns: 1fr; }
            .stats { grid-template-columns: repeat(2, 1fr); }
            .finding-meta { flex-direction: column; align-items: flex-start; gap: 10px; }
            .finding-summary { flex-direction: column; align-items: flex-start; gap: 10px; }
            .finding-title { min-width: auto; }
        }
        
        /* Print styles */
        @media print {
            .finding-content { display: block !important; }
            .expand-icon { display: none; }
            .finding-header { cursor: default; }
        }
    </style>
    <script>
        function toggleFinding(element) {
            const content = element.nextElementSibling;
            const icon = element.querySelector('.expand-icon');
            
            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                element.classList.remove('expanded');
            } else {
                content.classList.add('expanded');
                element.classList.add('expanded');
            }
        }
        
        // Auto-expand failed findings
        document.addEventListener('DOMContentLoaded', function() {
            const failedFindings = document.querySelectorAll('.finding-item');
            failedFindings.forEach(function(finding) {
                const statusBadge = finding.querySelector('.status-fail');
                if (statusBadge) {
                    const header = finding.querySelector('.finding-header');
                    const content = finding.querySelector('.finding-content');
                    content.classList.add('expanded');
                    header.classList.add('expanded');
                }
            });
        });
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <h1>Compliance Scan Report</h1>
                <div class="subtitle">Security Technical Implementation Guide (STIG) Assessment</div>
            </div>
        </div>

        <div class="overview">
            <h2 class="section-title">Scan Overview</h2>
            <div class="overview-grid">
                <div class="overview-card">
                    <h3>Scan Information</h3>
                    <div class="info-item">
                        <span class="info-label">Scan Name</span>
                        <span class="info-value">{{.Scan.Name}}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Profile</span>
                        <span class="info-value">{{.Scan.Spec.Profile}}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Scan ID</span>
                        <span class="info-value">{{if .Scan.Status.LatestResult}}{{.Scan.Status.LatestResult.ScanID}}{{else}}N/A{{end}}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Scan Time</span>
                        <span class="info-value">{{.GeneratedAt.Format "2006-01-02 15:04:05 UTC"}}</span>
                    </div>
                </div>
                
                <div class="overview-card">
                    <h3>Cluster Information</h3>
                    <div class="info-item">
                        <span class="info-label">Kubernetes Version</span>
                        <span class="info-value">{{.ClusterInfo.Version}}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Node Count</span>
                        <span class="info-value">{{.ClusterInfo.NodeCount}}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Container Runtime</span>
                        <span class="info-value">{{.ClusterInfo.Runtime}}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Master Node</span>
                        <span class="info-value">{{.ClusterInfo.MasterNode}}</span>
                    </div>
                </div>
                
                <div class="overview-card">
                    <h3>Node Details</h3>
                    {{range .ClusterInfo.Nodes}}
                    <div class="info-item">
                        <span class="info-label">{{.Name}} ({{.Role}})</span>
                        <span class="info-value">{{.IP}}</span>
                    </div>
                    {{end}}
                </div>
            </div>

            <div class="stats">
                <div class="stat-card total">
                    <h4>{{.Stats.Total}}</h4>
                    <p>Total</p>
                </div>
                <div class="stat-card pass">
                    <h4>{{.Stats.Pass}}</h4>
                    <p>Pass</p>
                </div>
                <div class="stat-card fail">
                    <h4>{{.Stats.Fail}}</h4>
                    <p>Fail</p>
                </div>
                <div class="stat-card error">
                    <h4>{{.Stats.Error}}</h4>
                    <p>Error</p>
                </div>
                <div class="stat-card manual">
                    <h4>{{.Stats.Manual}}</h4>
                    <p>Manual</p>
                </div>
                <div class="stat-card na">
                    <h4>{{.Stats.NotApplicable}}</h4>
                    <p>N/A</p>
                </div>
            </div>
        </div>

        <div class="findings">
            <h2 class="section-title">Findings - All ({{len .AggregatedResult.Spec.RuleResults}})</h2>
            {{if .AggregatedResult.Spec.RuleResults}}
                {{range .AggregatedResult.Spec.RuleResults}}
                {{$ruleDetail := index $.RuleDetails .RuleName}}
                <div class="finding-item">
                    <div class="finding-header" onclick="toggleFinding(this)">
                        <div class="finding-title-section">
                            <div class="finding-summary">
                                <span class="finding-id">{{$ruleDetail.ID}}</span>
                                <span class="finding-title">{{$ruleDetail.Title}}</span>
                                <span class="severity-text severity-{{.Severity | ToLower}}">{{.Severity}}</span>
                                <span class="result-badge result-{{.Status | ToLower | Replace "-" ""}}">{{.Status}}</span>
                            </div>
                        </div>
                        <div class="expand-icon">▼</div>
                    </div>
                    <div class="finding-content">
                        <div class="finding-details">
                            <div class="detail-section">
                                <h4>Description</h4>
                                <p>{{$ruleDetail.Description}}</p>
                            </div>
                            
                            {{if $ruleDetail.CheckText}}
                            <div class="detail-section">
                                <h4>Check Text</h4>
                                <pre>{{$ruleDetail.CheckText}}</pre>
                            </div>
                            {{end}}
                            
                            {{if $ruleDetail.FixText}}
                            <div class="detail-section">
                                <h4>Fix Text</h4>
                                <pre>{{$ruleDetail.FixText}}</pre>
                            </div>
                            {{end}}
                            
                            {{if .Message}}
                            <div class="detail-section">
                                <h4>Result Message</h4>
                                <p>{{.Message}}</p>
                            </div>
                            {{end}}
                            
                            {{if eq .CheckType "node"}}
                                {{if .NodeResults}}
                                <div class="detail-section">
                                    <h4>Node Results</h4>
                                    <table class="node-results-table">
                                        <thead>
                                            <tr>
                                                <th>Node Name</th>
                                                <th>Status</th>
                                                <th>Message</th>
                                                <th>Evidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {{range .NodeResults}}
                                            <tr>
                                                <td class="node-name">{{.NodeName}}</td>
                                                <td><span class="status-badge status-{{.Status | ToLower}}">{{.Status}}</span></td>
                                                <td>{{.Message}}</td>
                                                <td><pre class="evidence-text">{{if .Evidence}}{{.Evidence}}{{else}}N/A{{end}}</pre></td>
                                            </tr>
                                            {{end}}
                                        </tbody>
                                    </table>
                                </div>
                                {{end}}
                            {{end}}
                        </div>
                    </div>
                </div>
                {{end}}
            {{else}}
                <div style="text-align: center; padding: 60px; color: #6b7280;">
                    <h3>No findings available for this scan.</h3>
                </div>
            {{end}}
        </div>

        <div class="footer">
            <p>Generated by Compliance Operator on {{.GeneratedAt.Format "January 2, 2006 at 15:04:05 MST"}}</p>
            <p>Security Technical Implementation Guide (STIG) Compliance Assessment</p>
        </div>
    </div>
</body>
</html>
`

	// Create template with custom functions
	funcMap := template.FuncMap{
		"ToLower": strings.ToLower,
		"Replace": strings.ReplaceAll,
	}

	t, err := template.New("report").Funcs(funcMap).Parse(tmpl)
	if err != nil {
		return "", fmt.Errorf("failed to parse template: %v", err)
	}

	var buf bytes.Buffer
	if err := t.Execute(&buf, data); err != nil {
		return "", fmt.Errorf("failed to execute template: %v", err)
	}

	return buf.String(), nil
}

// createReportConfigMap creates a ConfigMap to store the HTML report
func (r *ScanReconciler) createReportConfigMap(ctx context.Context, scan *complianceapi.Scan, htmlContent string) (string, error) {
	// 如果 scan 已有 LatestResult，使用其 scanID
	var scanID string
	if scan.Status.LatestResult != nil && scan.Status.LatestResult.ScanID != "" {
		scanID = scan.Status.LatestResult.ScanID
	} else {
		// 生成新的 scanID
		scanID = generateScanID(scan)
	}

	// 使用 scanID 生成唯一的报告名称
	configMapName := generateReportConfigMapName(scanID)

	configMap := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      configMapName,
			Namespace: scan.Namespace,
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan":          scan.Name,
				"compliance-operator.alauda.io/profile":       scan.Spec.Profile,
				"compliance-operator.alauda.io/scan-id":       scanID,
				"compliance-operator.alauda.io/resource-type": "report",
				"compliance-operator.alauda.io/report":        "true",
			},
			OwnerReferences: []metav1.OwnerReference{
				{
					APIVersion: scan.APIVersion,
					Kind:       scan.Kind,
					Name:       scan.Name,
					UID:        scan.UID,
				},
			},
		},
		Data: map[string]string{
			"report.html": htmlContent,
			"generated":   time.Now().Format(time.RFC3339),
			"scan_id":     scanID,
		},
	}

	if err := r.Create(ctx, configMap); err != nil {
		if errors.IsAlreadyExists(err) {
			// ConfigMap already exists, update it instead
			r.Log.Info("Report ConfigMap already exists, updating", "configMap", configMapName)
			existingConfigMap := &corev1.ConfigMap{}
			if getErr := r.Get(ctx, types.NamespacedName{Name: configMapName, Namespace: scan.Namespace}, existingConfigMap); getErr != nil {
				return "", fmt.Errorf("failed to get existing report ConfigMap: %v", getErr)
			}
			existingConfigMap.Data = configMap.Data
			if updateErr := r.Update(ctx, existingConfigMap); updateErr != nil {
				return "", fmt.Errorf("failed to update report ConfigMap: %v", updateErr)
			}
			r.Log.Info("Updated existing report ConfigMap", "configMap", configMapName, "scan", scan.Name)
			return configMapName, nil
		}
		return "", fmt.Errorf("failed to create report ConfigMap: %v", err)
	}

	r.Log.Info("Created report ConfigMap", "configMap", configMapName, "scan", scan.Name, "scanID", scanID)
	return configMapName, nil
}

// generateScanReportHTML generates and stores the HTML report for a completed scan
func (r *ScanReconciler) generateScanReportHTML(ctx context.Context, scan *complianceapi.Scan, stats complianceapi.ScanStats) error {
	r.Log.Info("Generating scan report", "scan", scan.Name)

	// Generate HTML content
	htmlContent, err := r.generateReportHTML(ctx, scan, stats)
	if err != nil {
		return fmt.Errorf("failed to generate HTML report: %v", err)
	}

	// Create ConfigMap to store the report
	configMapName, err := r.createReportConfigMap(ctx, scan, htmlContent)
	if err != nil {
		return fmt.Errorf("failed to create report ConfigMap: %v", err)
	}

	// 如果 scan 有 LatestResult，更新报告引用
	if scan.Status.LatestResult != nil {
		scan.Status.LatestResult.ReportName = configMapName

		// 同时更新对应的历史记录
		for i, historicalRef := range scan.Status.HistoricalResults {
			if historicalRef.ScanID == scan.Status.LatestResult.ScanID {
				scan.Status.HistoricalResults[i].ReportName = configMapName
				break
			}
		}

		// 更新 Scan 状态
		if err := r.Status().Update(ctx, scan); err != nil {
			r.Log.Error(err, "Failed to update scan status with report reference", "scan", scan.Name)
			// 不中断主流程
		}
	}

	r.Log.Info("Scan report generated successfully", "scan", scan.Name, "configMap", configMapName)
	return nil
}

// generateHTMLReport 生成扫描结果的 HTML 报告（保留向后兼容性）
func (r *ScanReconciler) generateHTMLReport(ctx context.Context, scan *complianceapi.Scan, checkResult *complianceapi.CheckResult, stats complianceapi.ScanStats) (string, error) {
	// 调用新的报告生成方法
	return r.generateReportHTML(ctx, scan, stats)
}

// getRuleInfo retrieves rule title and description from Rule resource (保留向后兼容性)
func (r *ScanReconciler) getRuleInfo(ctx context.Context, ruleName, namespace string) (string, string) {
	var rule complianceapi.Rule
	if err := r.Get(ctx, types.NamespacedName{Name: ruleName, Namespace: namespace}, &rule); err != nil {
		r.Log.Error(err, "Failed to get rule information", "rule", ruleName)
		return ruleName, "Description not available"
	}

	title := rule.Spec.Title
	if title == "" {
		title = ruleName
	}

	description := rule.Spec.Description
	if description == "" {
		description = "Description not available"
	}

	return title, description
}
