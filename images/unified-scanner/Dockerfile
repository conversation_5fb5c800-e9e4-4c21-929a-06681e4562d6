# Unified compliance scanner image for both platform and node checks
FROM build-harbor.alauda.cn/ops/alpine:3.20.6-alauda-202502271510

# Add TARGETARCH argument for multi-arch support
ARG TARGETARCH

# Install all necessary tools for both platform and node scanning
RUN apk add --no-cache \
    bash \
    curl \
    jq \
    yq \
    openssl \
    ca-certificates \
    util-linux \
    procps-ng \
    coreutils \
    findutils \
    grep \
    sed \
    gawk \
    net-tools \
    iproute2 \
    shadow \

    # Download kubectl for the target architecture
    && curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/${TARGETARCH}/kubectl" \
    && chmod +x kubectl \
    && mv kubectl /usr/local/bin/ \
    # Download yq for the target architecture
    && curl -LO "https://github.com/mikefarah/yq/releases/latest/download/yq_linux_${TARGETARCH}" \
    && chmod +x yq_linux_${TARGETARCH} \
    && mv yq_linux_${TARGETARCH} /usr/local/bin/yq-static

# Create scanner user with appropriate permissions
RUN addgroup -g 1001 scanner && \
    adduser -D -u 1001 -G scanner scanner && \
    addgroup scanner wheel

# Copy unified scanner scripts
COPY scripts/unified-scanner.sh /usr/local/bin/

# Make scripts executable
RUN chmod +x /usr/local/bin/unified-scanner.sh

# Switch to scanner user
USER scanner

WORKDIR /tmp

# Use unified scanner as entrypoint
ENTRYPOINT ["/usr/local/bin/unified-scanner.sh"] 