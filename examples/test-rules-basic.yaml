---
# Test Rule: Control Plane Only
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: test-rule-control-plane-basic
  namespace: compliance-system
  labels:
    compliance-operator.alauda.io/rule-type: "test"
spec:
  id: "TEST-CP-001"
  title: "Test Control Plane Configuration"
  description: |
    This is a test rule that only applies to control plane nodes.
    It checks basic control plane configurations.
  checkText: |
    Verify control plane specific configurations are present.
  fixText: |
    Apply control plane specific configurations.
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"  # Only runs on control-plane nodes
  checkScript: |
    #!/bin/bash
    echo "=== Control Plane Test Rule ==="
    echo "Node: $(hostname)"
    echo "Checking control plane specific configurations..."
    
    # Check if this is a control plane node
    if [ -f "/host/etc/kubernetes/admin.conf" ] || [ -f "/host/etc/kubernetes/manifests/kube-apiserver.yaml" ]; then
      echo "✓ Control plane node detected"
      echo "✓ Control plane configurations found"
      echo "PASS: Control plane test completed successfully"
      exit 0
    else
      echo "⚠ This doesn't appear to be a control plane node"
      echo "PASS: Test completed (not a control plane node)"
      exit 0
    fi

---
# Test Rule: Worker Nodes Only
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: test-rule-worker-basic
  namespace: compliance-system
  labels:
    compliance-operator.alauda.io/rule-type: "test"
spec:
  id: "TEST-WK-001"
  title: "Test Worker Node Configuration"
  description: |
    This is a test rule that only applies to worker nodes.
    It checks basic worker node configurations.
  checkText: |
    Verify worker node specific configurations are present.
  fixText: |
    Apply worker node specific configurations.
  severity: "medium"
  checkType: "node"
  nodeScope: "worker"  # Only runs on worker nodes
  checkScript: |
    #!/bin/bash
    echo "=== Worker Node Test Rule ==="
    echo "Node: $(hostname)"
    echo "Checking worker node specific configurations..."
    
    # Check if kubelet is running (common on all nodes but focus on worker logic)
    if pgrep -f kubelet > /dev/null 2>&1; then
      echo "✓ Kubelet process found"
      echo "✓ Worker node configurations verified"
      echo "PASS: Worker node test completed successfully"
      exit 0
    else
      echo "⚠ Kubelet process not found"
      echo "PASS: Test completed (kubelet check)"
      exit 0
    fi

---
# Test Rule: All Nodes
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: test-rule-all-nodes-basic
  namespace: compliance-system
  labels:
    compliance-operator.alauda.io/rule-type: "test"
spec:
  id: "TEST-ALL-001"
  title: "Test All Nodes Configuration"
  description: |
    This is a test rule that applies to all nodes (both control-plane and worker).
    It checks configurations that should be present on all nodes.
  checkText: |
    Verify common configurations are present on all nodes.
  fixText: |
    Apply common configurations to all nodes.
  severity: "low"
  checkType: "node"
  nodeScope: "all"  # Runs on all nodes
  checkScript: |
    #!/bin/bash
    echo "=== All Nodes Test Rule ==="
    echo "Node: $(hostname)"
    echo "Checking configurations common to all nodes..."
    
    # Check basic system information
    echo "✓ Hostname: $(hostname)"
    echo "✓ OS: $(cat /host/etc/os-release | grep PRETTY_NAME | cut -d'=' -f2 | tr -d '\"' || echo 'Unknown')"
    echo "✓ Kernel: $(uname -r)"
    
    # Check if /host mount is available (should be in scanner pods)
    if [ -d "/host" ]; then
      echo "✓ Host filesystem mounted at /host"
    else
      echo "⚠ Host filesystem not mounted"
    fi
    
    echo "PASS: All nodes test completed successfully"
    exit 0

---
# Test Rule: Platform Level
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: test-rule-platform-basic
  namespace: compliance-system
  labels:
    compliance-operator.alauda.io/rule-type: "test"
spec:
  id: "TEST-PLT-001"
  title: "Test Platform Configuration"
  description: |
    This is a test rule that runs at the platform level.
    It checks cluster-wide configurations using Kubernetes API.
  checkText: |
    Verify platform-level configurations are correct.
  fixText: |
    Apply platform-level configurations.
  severity: "high"
  checkType: "platform"
  checkScript: |
    #!/bin/bash
    echo "=== Platform Test Rule ==="
    echo "Checking platform-level configurations..."
    
    # Check if kubectl is available
    if command -v kubectl > /dev/null 2>&1; then
      echo "✓ kubectl command available"
      
      # Check cluster info
      echo "✓ Cluster info:"
      kubectl cluster-info --request-timeout=10s 2>/dev/null || echo "  Could not retrieve cluster info"
      
      # Check nodes
      echo "✓ Cluster nodes:"
      kubectl get nodes --no-headers 2>/dev/null | wc -l | xargs echo "  Node count:" || echo "  Could not count nodes"
      
    else
      echo "⚠ kubectl not available"
    fi
    
    echo "PASS: Platform test completed successfully"
    exit 0
