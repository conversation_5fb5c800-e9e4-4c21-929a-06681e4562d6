package scan

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

func TestCreateReportConfigMap(t *testing.T) {
	// 创建一个带有 scanID 的 Scan
	scanID := "test-scan-id"
	scan := &complianceapi.Scan{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-scan",
			Namespace: "default",
			Annotations: map[string]string{
				"compliance-operator.alauda.io/current-scan-id": scanID,
			},
		},
		Spec: complianceapi.ScanSpec{
			Profile: "test-profile",
		},
	}

	// 设置 reconciler
	r := setupReconciler(t, scan)

	// 执行 createReportConfigMap
	htmlContent := "<html><body>Test Report</body></html>"
	reportName, err := r.createReportConfigMap(context.Background(), scan, htmlContent)
	assert.NoError(t, err)
	assert.NotEmpty(t, reportName)

	// 验证报告 ConfigMap 已创建
	var configMap corev1.ConfigMap
	err = r.Get(context.Background(), types.NamespacedName{Name: reportName, Namespace: "default"}, &configMap)
	assert.NoError(t, err)

	// 验证报告内容
	assert.NotEmpty(t, configMap.Data["report.html"])
	assert.Contains(t, configMap.Data["report.html"], "<html><body>Test Report</body></html>")
}

// func TestGenerateHTMLReport(t *testing.T) {
// 	// 创建测试数据
// 	scan := &complianceapi.Scan{
// 		ObjectMeta: metav1.ObjectMeta{
// 			Name:      "test-scan",
// 			Namespace: "default",
// 		},
// 		Spec: complianceapi.ScanSpec{
// 			Profile: "test-profile",
// 		},
// 	}

// 	checkResult := &complianceapi.CheckResult{
// 		ObjectMeta: metav1.ObjectMeta{
// 			Name:      "test-checkresult",
// 			Namespace: "default",
// 		},
// 		Spec: complianceapi.CheckResultSpec{
// 			ScanName:    "test-scan",
// 			ProfileName: "test-profile",
// 			RuleResults: []complianceapi.RuleResult{
// 				{
// 					RuleID:    "test-rule-1",
// 					RuleName:  "test-rule-1",
// 					Severity:  "high",
// 					CheckType: "platform",
// 					Status:    "PASS",
// 					Message:   "Test passed",
// 				},
// 				{
// 					RuleID:    "test-rule-2",
// 					RuleName:  "test-rule-2",
// 					Severity:  "medium",
// 					CheckType: "platform",
// 					Status:    "FAIL",
// 					Message:   "Test failed",
// 				},
// 			},
// 		},
// 	}

// 	// 执行 generateHTMLReport
// 	r := &ScanReconciler{}
// 	stats := complianceapi.ScanStats{
// 		Total: 2,
// 		Pass:  1,
// 		Fail:  1,
// 	}
// 	htmlContent, err := r.generateHTMLReport(context.Background(), scan, checkResult, stats)

// 	// 验证 HTML 报告内容
// 	assert.NoError(t, err)
// 	assert.NotEmpty(t, htmlContent)
// 	assert.Contains(t, htmlContent, "<html")
// 	assert.Contains(t, htmlContent, "</html>")
// 	assert.Contains(t, htmlContent, "test-scan")
// 	assert.Contains(t, htmlContent, "test-profile")
// 	assert.Contains(t, htmlContent, "Test Rule 1")
// 	assert.Contains(t, htmlContent, "Test Rule 2")
// 	assert.Contains(t, htmlContent, "PASS")
// 	assert.Contains(t, htmlContent, "FAIL")

// 	// 验证表格结构
// 	assert.Contains(t, htmlContent, "<table")
// 	assert.Contains(t, htmlContent, "</table>")
// 	assert.Contains(t, htmlContent, "<tr")
// 	assert.Contains(t, htmlContent, "</tr>")
// 	assert.Contains(t, htmlContent, "<th")
// 	assert.Contains(t, htmlContent, "</th>")
// 	assert.Contains(t, htmlContent, "<td")
// 	assert.Contains(t, htmlContent, "</td>")
// }

func TestGenerateScanReport(t *testing.T) {
	// 创建一个带有 scanID 的 Scan
	scanID := "test-scan-id"
	scan := &complianceapi.Scan{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-scan",
			Namespace: "default",
			Annotations: map[string]string{
				"compliance-operator.alauda.io/current-scan-id": scanID,
			},
		},
		Spec: complianceapi.ScanSpec{
			Profile: "test-profile",
		},
	}

	// 创建 CheckResult
	checkResult := &complianceapi.CheckResult{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "checkresult-" + scanID,
			Namespace: "default",
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan":    "test-scan",
				"compliance-operator.alauda.io/scan-id": scanID,
			},
		},
		Spec: complianceapi.CheckResultSpec{
			ScanName:    "test-scan",
			ProfileName: "test-profile",
			RuleResults: []complianceapi.RuleResult{
				{
					RuleID:    "test-rule-1",
					RuleName:  "test-rule-1",
					Severity:  "high",
					CheckType: "platform",
					Status:    "PASS",
					Message:   "Test passed",
				},
				{
					RuleID:    "test-rule-2",
					RuleName:  "test-rule-2",
					Severity:  "medium",
					CheckType: "platform",
					Status:    "FAIL",
					Message:   "Test failed",
				},
			},
		},
	}

	// 设置 reconciler
	r := setupReconciler(t, scan, checkResult)

	// 执行 generateScanReport
	stats := complianceapi.ScanStats{
		Total: 2,
		Pass:  1,
		Fail:  1,
	}
	err := r.generateScanReport(context.Background(), scan, scanID, stats)
	assert.NoError(t, err)

	// 由于 fake client 的限制，我们可能无法直接验证 ConfigMap 创建
	// 这里我们只验证 generateScanReport 是否成功执行
	// 如果需要验证 ConfigMap 创建，可以在 setupReconciler 中添加一个 mock 实现
}
