package scan

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

// TestNodeScopeStrategyIntegration 测试NodeScopeStrategy的完整集成功能
func TestNodeScopeStrategyIntegration(t *testing.T) {
	// 创建测试节点
	controlPlaneNode := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "control-plane-node",
			Labels: map[string]string{
				"node-role.kubernetes.io/control-plane": "",
				"kubernetes.io/os":                      "linux",
			},
		},
	}

	workerNode1 := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "worker-node-1",
			Labels: map[string]string{
				"node-role.kubernetes.io/worker": "",
				"kubernetes.io/os":               "linux",
			},
		},
	}

	workerNode2 := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "worker-node-2",
			Labels: map[string]string{
				"node-role.kubernetes.io/worker": "",
				"kubernetes.io/os":               "linux",
			},
		},
	}

	// 创建测试规则
	controlPlaneRule := complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{Name: "control-plane-rule", Namespace: "default"},
		Spec: complianceapi.RuleSpec{
			CheckType:   "node",
			NodeScope:   "control-plane",
			CheckScript: "echo 'control-plane check'",
		},
	}

	workerRule := complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{Name: "worker-rule", Namespace: "default"},
		Spec: complianceapi.RuleSpec{
			CheckType:   "node",
			NodeScope:   "worker",
			CheckScript: "echo 'worker check'",
		},
	}

	allNodesRule := complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{Name: "all-nodes-rule", Namespace: "default"},
		Spec: complianceapi.RuleSpec{
			CheckType:   "node",
			NodeScope:   "all",
			CheckScript: "echo 'all nodes check'",
		},
	}

	platformRule := complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{Name: "platform-rule", Namespace: "default"},
		Spec: complianceapi.RuleSpec{
			CheckType:   "platform",
			CheckScript: "echo 'platform check'",
		},
	}

	testCases := []struct {
		name                string
		nodeScopeStrategy   string
		targetNodeRoles     []string
		nodeSelector        map[string]string
		rules               []complianceapi.Rule
		expectError         bool
		expectedJobCreation bool
		description         string
	}{
		{
			name:                "Auto strategy - mixed rules",
			nodeScopeStrategy:   "auto",
			targetNodeRoles:     []string{"control-plane", "worker"},
			rules:               []complianceapi.Rule{controlPlaneRule, workerRule, allNodesRule, platformRule},
			expectError:         false,
			expectedJobCreation: true,
			description:         "Auto strategy should create separate jobs for different node scopes",
		},
		{
			name:                "Manual strategy - ignore node scopes",
			nodeScopeStrategy:   "manual",
			nodeSelector:        map[string]string{"kubernetes.io/os": "linux"},
			rules:               []complianceapi.Rule{controlPlaneRule, workerRule, allNodesRule},
			expectError:         false,
			expectedJobCreation: true,
			description:         "Manual strategy should use nodeSelector and ignore rule node scopes",
		},
		{
			name:                "Strict strategy - compatible rules",
			nodeScopeStrategy:   "strict",
			targetNodeRoles:     []string{"worker"},
			rules:               []complianceapi.Rule{workerRule, allNodesRule, platformRule},
			expectError:         false,
			expectedJobCreation: true,
			description:         "Strict strategy should pass with compatible rules",
		},
		{
			name:                "Strict strategy - incompatible rules",
			nodeScopeStrategy:   "strict",
			targetNodeRoles:     []string{"worker"},
			rules:               []complianceapi.Rule{controlPlaneRule}, // control-plane rule with worker target
			expectError:         true,
			expectedJobCreation: false,
			description:         "Strict strategy should fail with incompatible rules",
		},
		{
			name:                "Default strategy (empty) - should use manual",
			nodeScopeStrategy:   "",
			nodeSelector:        map[string]string{"kubernetes.io/os": "linux"},
			rules:               []complianceapi.Rule{controlPlaneRule, workerRule},
			expectError:         false,
			expectedJobCreation: true,
			description:         "Empty strategy should default to manual behavior",
		},
		{
			name:                "Manual strategy with targetNodeRoles - should filter nodes",
			nodeScopeStrategy:   "manual",
			targetNodeRoles:     []string{"control-plane"},
			rules:               []complianceapi.Rule{controlPlaneRule, workerRule},
			expectError:         false,
			expectedJobCreation: true,
			description:         "Manual strategy should respect targetNodeRoles and filter nodes",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建测试扫描
			scan := &complianceapi.Scan{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "test-scan",
					Namespace: "default",
				},
				Spec: complianceapi.ScanSpec{
					Profile:           "test-profile",
					ScanType:          "node",
					NodeScopeStrategy: tc.nodeScopeStrategy,
					TargetNodeRoles:   tc.targetNodeRoles,
					NodeSelector:      tc.nodeSelector,
				},
			}

			// 设置reconciler
			r := setupReconciler(t, scan, &controlPlaneNode, &workerNode1, &workerNode2)

			// 执行createNodeScanJobs
			err := r.createNodeScanJobs(context.Background(), scan, tc.rules, "test-scan-id")

			// 验证结果
			if tc.expectError {
				assert.Error(t, err, tc.description)
			} else {
				assert.NoError(t, err, tc.description)
			}

			t.Logf("Test case '%s': %s", tc.name, tc.description)
		})
	}
}

// TestCreateNodeScanJobsWithAutoStrategy 测试Auto策略的详细行为
func TestCreateNodeScanJobsWithAutoStrategy(t *testing.T) {
	// 创建测试节点
	controlPlaneNode := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "control-plane-node",
			Labels: map[string]string{
				"node-role.kubernetes.io/control-plane": "",
			},
		},
	}

	workerNode := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "worker-node",
			Labels: map[string]string{
				"node-role.kubernetes.io/worker": "",
			},
		},
	}

	// 创建测试规则
	rules := []complianceapi.Rule{
		{
			ObjectMeta: metav1.ObjectMeta{Name: "control-plane-rule", Namespace: "default"},
			Spec: complianceapi.RuleSpec{
				CheckType:   "node",
				NodeScope:   "control-plane",
				CheckScript: "echo 'control-plane check'",
			},
		},
		{
			ObjectMeta: metav1.ObjectMeta{Name: "worker-rule", Namespace: "default"},
			Spec: complianceapi.RuleSpec{
				CheckType:   "node",
				NodeScope:   "worker",
				CheckScript: "echo 'worker check'",
			},
		},
		{
			ObjectMeta: metav1.ObjectMeta{Name: "all-nodes-rule", Namespace: "default"},
			Spec: complianceapi.RuleSpec{
				CheckType:   "node",
				NodeScope:   "all",
				CheckScript: "echo 'all nodes check'",
			},
		},
		{
			ObjectMeta: metav1.ObjectMeta{Name: "platform-rule", Namespace: "default"},
			Spec: complianceapi.RuleSpec{
				CheckType:   "platform", // 应该被忽略
				CheckScript: "echo 'platform check'",
			},
		},
	}

	scan := &complianceapi.Scan{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-scan",
			Namespace: "default",
		},
		Spec: complianceapi.ScanSpec{
			Profile:           "test-profile",
			ScanType:          "node",
			NodeScopeStrategy: "auto",
		},
	}

	// 设置reconciler
	r := setupReconciler(t, scan, &controlPlaneNode, &workerNode)

	// 执行createNodeScanJobsWithAutoStrategy
	allNodes := []corev1.Node{controlPlaneNode, workerNode}
	err := r.createNodeScanJobsWithAutoStrategy(context.Background(), scan, rules, allNodes, "test-scan-id")

	// 验证结果
	assert.NoError(t, err)

	// 验证规则分组逻辑
	groups := r.groupRulesByNodeScope(rules)
	assert.Equal(t, 3, len(groups), "Should have 3 groups: control-plane, worker, all")

	// 验证每个组的规则数量
	assert.Equal(t, 1, len(groups["control-plane"]), "Should have 1 control-plane rule")
	assert.Equal(t, 1, len(groups["worker"]), "Should have 1 worker rule")
	assert.Equal(t, 1, len(groups["all"]), "Should have 1 all-nodes rule")
}

// TestCreateNodeScanJobsWithManualStrategy 测试Manual策略的行为
func TestCreateNodeScanJobsWithManualStrategy(t *testing.T) {
	// 创建测试节点
	controlPlaneNode := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "control-plane-node",
			Labels: map[string]string{
				"node-role.kubernetes.io/control-plane": "",
				"environment":                           "production",
			},
		},
	}

	workerNode := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "worker-node",
			Labels: map[string]string{
				"node-role.kubernetes.io/worker": "",
				"environment":                    "production",
			},
		},
	}

	// 创建测试规则（包含不同的NodeScope）
	rules := []complianceapi.Rule{
		{
			ObjectMeta: metav1.ObjectMeta{Name: "control-plane-rule", Namespace: "default"},
			Spec: complianceapi.RuleSpec{
				CheckType:   "node",
				NodeScope:   "control-plane", // Manual策略应该忽略这个
				CheckScript: "echo 'control-plane check'",
			},
		},
		{
			ObjectMeta: metav1.ObjectMeta{Name: "worker-rule", Namespace: "default"},
			Spec: complianceapi.RuleSpec{
				CheckType:   "node",
				NodeScope:   "worker", // Manual策略应该忽略这个
				CheckScript: "echo 'worker check'",
			},
		},
	}

	scan := &complianceapi.Scan{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-scan",
			Namespace: "default",
		},
		Spec: complianceapi.ScanSpec{
			Profile:           "test-profile",
			ScanType:          "node",
			NodeScopeStrategy: "manual",
			NodeSelector: map[string]string{
				"environment": "production", // 只选择production环境的节点
			},
		},
	}

	// 设置reconciler
	r := setupReconciler(t, scan, &controlPlaneNode, &workerNode)

	// 执行createNodeScanJobsWithManualStrategy
	allNodes := []corev1.Node{controlPlaneNode, workerNode}
	err := r.createNodeScanJobsWithManualStrategy(context.Background(), scan, rules, allNodes, "test-scan-id")

	// 验证结果
	assert.NoError(t, err)

	// Manual策略应该忽略NodeScope，在所有匹配nodeSelector的节点上运行所有规则
	// 这里我们主要验证方法执行成功，具体的job创建验证需要更复杂的mock设置
}
