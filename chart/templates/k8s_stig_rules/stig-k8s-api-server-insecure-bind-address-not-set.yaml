apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-insecure-bind-address-not-set
  namespace: compliance-system
spec:
  id: "V-242388"
  title: "The Kubernetes API server must have the insecure bind address not set."
  description: "By default, the API server will listen on two ports and addresses. One address is the secure address and the other address is called the \"insecure bind\" address and is set by default to localhost. Any requests to this address bypass authentication and authorization checks. If this insecure bind address is set to localhost, anyone who gains access to the host on which the Control Plane is running can bypass all authorization and authentication mechanisms put in place and have full control over the entire cluster.\n\nClose or set the insecure bind address by setting the API server's \"--insecure-bind-address\" flag to an IP or leave it unset and ensure that the \"--insecure-bind-port\" is not set."
  checkText: "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i insecure-bind-address * \n\nIf the setting \"--insecure-bind-address\" is found and set to \"localhost\" in the Kubernetes API manifest file, this is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nRemove the value of \"--insecure-bind-address\" setting."
  severity: "high"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Checking API Server insecure bind address configuration..."
    manifest_path="/host/etc/kubernetes/manifests/kube-apiserver.yaml"
    
    echo "Checking API Server manifest: $manifest_path"
    
    # Check if manifest file exists
    if [[ ! -f "$manifest_path" ]]; then
        echo "ERROR: API Server manifest file not found: $manifest_path"
        exit 1
    fi
    
    echo "Searching for insecure-bind-address parameter in manifest..."
    
    # Check for insecure-bind-address configuration
    insecure_bind=$(grep -E -- "--insecure-bind-address(=[^[:space:]]+| +[^[:space:]]+)" "$manifest_path" 2>/dev/null)
    
    if [[ -n "$insecure_bind" ]]; then
        echo "FAIL: Insecure bind address is configured (security risk)"
        echo "Found configuration: $insecure_bind"
        # Extract the actual value for detailed reporting
        bind_value=$(echo "$insecure_bind" | grep -oP -- "--insecure-bind-address[= ]+\K[^[:space:]]+" 2>/dev/null)
        if [[ -n "$bind_value" ]]; then
            echo "Insecure bind address value: $bind_value"
        fi
        exit 1
    fi
    
    echo "PASS: No insecure bind address configuration found"
    echo "API Server is properly configured without insecure binding"
    exit 0 