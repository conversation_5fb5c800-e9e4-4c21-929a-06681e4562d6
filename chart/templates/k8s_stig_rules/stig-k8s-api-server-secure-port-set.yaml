apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-secure-port-set
  namespace: compliance-system
spec:
  id: "V-242389"
  title: "The Kubernetes API server must have the secure port set."
  description: "By default, the API server will listen on what is rightfully called the secure port, port 6443. Any requests to this port will perform authentication and authorization checks. If this port is disabled, anyone who gains access to the host on which the Control Plane is running has full control of the entire cluster over encrypted traffic.\n\nOpen the secure port by setting the API server's \"--secure-port\" flag to a value other than \"0\"."
  checkText: "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i secure-port * \n\nIf the setting \"--secure-port\" is set to \"0\" or is not configured in the Kubernetes API manifest file, this is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--secure-port\" to a value greater than \"0\"."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting API Server secure port configuration check (V-242389)"
    
    manifests_dir="/host/etc/kubernetes/manifests"
    
    echo "Checking for API Server manifest files in: $manifests_dir"
    
    # Check if manifests directory exists
    if [[ ! -d "$manifests_dir" ]]; then
        echo "FAIL: Kubernetes manifests directory not found: $manifests_dir"
        exit 1
    fi
    
    # Find API Server manifest files
    api_files=$(grep -lis 'kube-apiserver' "$manifests_dir"/* 2>/dev/null)

    if [[ -z "$api_files" ]]; then
        echo "FAIL: No API Server manifest found in $manifests_dir"
        echo "Expected to find files containing 'kube-apiserver'"
        exit 1
    fi
    
    echo "Found API Server manifest files:"
    echo "$api_files"

    # Check each API Server manifest file
    for file in $api_files; do
        echo "Analyzing manifest file: $file"
        
        # Search for secure-port configuration
        secure_port_line=$(grep -i -- "--secure-port" "$file" 2>/dev/null)
        
        if [[ -z "$secure_port_line" ]]; then
            echo "FAIL: No --secure-port configuration found in $file"
            echo "Secure port must be explicitly configured"
            exit 1
        fi
        
        echo "Found secure-port configuration: $secure_port_line"
        
        # Extract port value using same logic as original
        secure_port=$(echo "$secure_port_line" | awk -F= '{print $2}' 2>/dev/null)
        
        echo "Extracted secure port value: '$secure_port'"
        
        # Check if port is set to 0 or empty (preserving original logic)
        if [[ "$secure_port" -eq 0 ]] 2>/dev/null || [[ -z "$secure_port" ]]; then
            echo "FAIL: Invalid secure-port configuration in $file"
            echo "Secure port must not be 0 and must be properly configured"
            echo "Current configuration: $secure_port_line"
            exit 1
        fi
        
        echo "Secure port is properly configured: $secure_port"
    done

    echo "PASS: Secure port properly configured in all manifest files"
    exit 0 