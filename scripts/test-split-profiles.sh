#!/bin/bash

# 测试分割后的STIG profiles脚本
# 验证每个profile的HTML报告生成是否正常

set -e

NAMESPACE="compliance-system"
PROFILES=(
    "stig-k8s-v2r2-quick-test"
    "stig-k8s-v2r2-api-server"
    "stig-k8s-v2r2-control-plane"
    "stig-k8s-v2r2-kubelet"
    "stig-k8s-v2r2-general"
)

usage() {
    echo "Usage: $0 [-n <namespace>] [-p <profile>]"
    echo "  -n: Namespace (default: compliance-system)"
    echo "  -p: Test specific profile only"
    echo ""
    echo "Available profiles:"
    for profile in "${PROFILES[@]}"; do
        echo "  - $profile"
    done
    exit 1
}

SPECIFIC_PROFILE=""

while getopts "n:p:h" opt; do
    case $opt in
        n) NAMESPACE="$OPTARG" ;;
        p) SPECIFIC_PROFILE="$OPTARG" ;;
        h) usage ;;
        *) usage ;;
    esac
done

echo "🧪 测试分割后的STIG Profiles"
echo "==============================="
echo "Namespace: $NAMESPACE"

# 如果指定了特定profile，只测试该profile
if [ -n "$SPECIFIC_PROFILE" ]; then
    PROFILES=("$SPECIFIC_PROFILE")
    echo "Testing specific profile: $SPECIFIC_PROFILE"
fi

# 函数：创建测试扫描
create_test_scan() {
    local profile_name="$1"
    local scan_name="test-$profile_name"
    
    echo "  创建扫描: $scan_name (profile: $profile_name)"
    
    cat <<EOF | kubectl apply -f -
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Scan
metadata:
  name: $scan_name
  namespace: $NAMESPACE
spec:
  profile: $profile_name
  scanType: all
  nodeSelector: {}
  targetNodeRoles: []
EOF
    
    echo "    ✅ 扫描已创建"
}

# 函数：等待扫描完成
wait_for_scan_completion() {
    local scan_name="$1"
    local max_wait=600  # 10分钟
    local wait_time=0
    
    echo "  等待扫描完成: $scan_name"
    
    while [ $wait_time -lt $max_wait ]; do
        status=$(kubectl get scan "$scan_name" -n "$NAMESPACE" -o jsonpath='{.status.phase}' 2>/dev/null || echo "NotFound")
        
        case $status in
            "Done")
                echo "    ✅ 扫描完成"
                return 0
                ;;
            "Error")
                echo "    ❌ 扫描失败"
                kubectl get scan "$scan_name" -n "$NAMESPACE" -o jsonpath='{.status.message}'
                return 1
                ;;
            "Running")
                echo "    ⏳ 扫描进行中... (${wait_time}s)"
                ;;
            "NotFound")
                echo "    ❌ 扫描不存在"
                return 1
                ;;
            *)
                echo "    ⏳ 状态: $status (${wait_time}s)"
                ;;
        esac
        
        sleep 10
        wait_time=$((wait_time + 10))
    done
    
    echo "    ❌ 扫描超时"
    return 1
}

# 函数：检查报告生成
check_report_generation() {
    local scan_name="$1"
    local profile_name="$2"
    
    echo "  检查报告生成: $scan_name"
    
    # 获取scanID
    local scan_id=$(kubectl get scan "$scan_name" -n "$NAMESPACE" -o jsonpath='{.status.latestResult.scanID}' 2>/dev/null || echo "")
    
    if [ -z "$scan_id" ]; then
        echo "    ❌ 无法获取scanID"
        return 1
    fi
    
    echo "    ScanID: $scan_id"
    
    # 检查CheckResult
    local checkresult_name="checkresult-$scan_id"
    if kubectl get checkresult "$checkresult_name" -n "$NAMESPACE" >/dev/null 2>&1; then
        local rule_count=$(kubectl get checkresult "$checkresult_name" -n "$NAMESPACE" -o jsonpath='{.spec.ruleResults}' | jq length 2>/dev/null || echo "0")
        echo "    ✅ CheckResult存在，规则数量: $rule_count"
    else
        echo "    ❌ CheckResult不存在"
        return 1
    fi
    
    # 检查HTML报告ConfigMap
    local report_name="report-$scan_id"
    if kubectl get configmap "$report_name" -n "$NAMESPACE" >/dev/null 2>&1; then
        # 检查压缩信息
        local compression_type=$(kubectl get configmap "$report_name" -n "$NAMESPACE" -o jsonpath='{.data.compression}' 2>/dev/null || echo "none")
        local original_size=$(kubectl get configmap "$report_name" -n "$NAMESPACE" -o jsonpath='{.metadata.annotations.compliance-operator\.alauda\.io/original-size}' 2>/dev/null || echo "unknown")
        local compressed_size=$(kubectl get configmap "$report_name" -n "$NAMESPACE" -o jsonpath='{.metadata.annotations.compliance-operator\.alauda\.io/compressed-size}' 2>/dev/null || echo "unknown")
        
        echo "    ✅ HTML报告ConfigMap存在"
        echo "       压缩类型: $compression_type"
        echo "       原始大小: $original_size bytes"
        echo "       压缩大小: $compressed_size bytes"
        
        # 检查是否有实际的HTML内容
        if kubectl get configmap "$report_name" -n "$NAMESPACE" -o jsonpath='{.data.report\.html}' >/dev/null 2>&1; then
            local html_size=$(kubectl get configmap "$report_name" -n "$NAMESPACE" -o jsonpath='{.data.report\.html}' | wc -c)
            echo "       HTML内容大小: $html_size bytes"
        elif kubectl get configmap "$report_name" -n "$NAMESPACE" -o jsonpath='{.data.report\.html\.gz\.b64}' >/dev/null 2>&1; then
            local gzip_size=$(kubectl get configmap "$report_name" -n "$NAMESPACE" -o jsonpath='{.data.report\.html\.gz\.b64}' | wc -c)
            echo "       Gzip压缩内容大小: $gzip_size bytes"
        else
            echo "    ❌ 没有找到HTML内容"
            return 1
        fi
    else
        echo "    ❌ HTML报告ConfigMap不存在"
        return 1
    fi
    
    return 0
}

# 函数：清理测试资源
cleanup_test_resources() {
    local scan_name="$1"
    
    echo "  清理测试资源: $scan_name"
    
    # 删除scan（会自动清理相关资源）
    kubectl delete scan "$scan_name" -n "$NAMESPACE" --ignore-not-found=true
    
    echo "    ✅ 资源已清理"
}

# 主测试循环
failed_profiles=()
successful_profiles=()

for profile in "${PROFILES[@]}"; do
    echo ""
    echo "🔍 测试Profile: $profile"
    echo "----------------------------------------"
    
    scan_name="test-$profile"
    
    # 检查profile是否存在
    if ! kubectl get profile "$profile" -n "$NAMESPACE" >/dev/null 2>&1; then
        echo "  ❌ Profile不存在: $profile"
        failed_profiles+=("$profile (profile not found)")
        continue
    fi
    
    # 清理可能存在的旧资源
    cleanup_test_resources "$scan_name" >/dev/null 2>&1 || true
    
    # 创建测试扫描
    if ! create_test_scan "$profile"; then
        echo "  ❌ 创建扫描失败"
        failed_profiles+=("$profile (scan creation failed)")
        continue
    fi
    
    # 等待扫描完成
    if ! wait_for_scan_completion "$scan_name"; then
        echo "  ❌ 扫描未完成"
        failed_profiles+=("$profile (scan incomplete)")
        cleanup_test_resources "$scan_name"
        continue
    fi
    
    # 检查报告生成
    if ! check_report_generation "$scan_name" "$profile"; then
        echo "  ❌ 报告生成检查失败"
        failed_profiles+=("$profile (report generation failed)")
        cleanup_test_resources "$scan_name"
        continue
    fi
    
    echo "  ✅ Profile测试成功"
    successful_profiles+=("$profile")
    
    # 清理测试资源
    cleanup_test_resources "$scan_name"
done

# 输出测试结果
echo ""
echo "📊 测试结果汇总"
echo "=================="
echo "成功的Profiles (${#successful_profiles[@]}):"
for profile in "${successful_profiles[@]}"; do
    echo "  ✅ $profile"
done

echo ""
echo "失败的Profiles (${#failed_profiles[@]}):"
for profile in "${failed_profiles[@]}"; do
    echo "  ❌ $profile"
done

echo ""
if [ ${#failed_profiles[@]} -eq 0 ]; then
    echo "🎉 所有Profile测试成功！HTML报告大小问题已解决。"
    exit 0
else
    echo "💥 有 ${#failed_profiles[@]} 个Profile测试失败。"
    exit 1
fi 