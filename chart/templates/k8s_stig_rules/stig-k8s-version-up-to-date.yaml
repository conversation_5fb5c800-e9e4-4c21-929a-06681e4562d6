apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-version-up-to-date
  namespace: compliance-system
spec:
  id: "V-242443"
  title: "Kubernetes must contain the latest updates as authorized by IAVMs, CTOs, DTMs, and STIGs."
  description: "Kubernetes software must stay up to date with the latest patches, service packs, and hot fixes. Not updating the Kubernetes control plane will expose the organization to vulnerabilities.\n\nFlaws discovered during security assessments, continuous monitoring, incident response activities, or information system error handling must also be addressed expeditiously. \n\nOrganization-defined time periods for updating security-relevant container platform components may vary based on a variety of factors including, for example, the security category of the information system or the criticality of the update (i.e., severity of the vulnerability related to the discovered flaw). \n\nThis requirement will apply to software patch management solutions that are used to install patches across the enclave and also to applications themselves that are not part of that patch management solution. For example, many browsers today provide the capability to install their own patch software. Patch criticality, as well as system criticality will vary. Therefore, the tactical situations regarding the patch management process will also vary. This means that the time period utilized must be a configurable parameter. Time frames for application of security-relevant software updates may be dependent upon the IAVM process.\n\nThe container platform components will be configured to check for and install security-relevant software updates within an identified time period from the availability of the update. The container platform registry will ensure the images are current. The specific time period will be defined by an authoritative source (e.g., IAVM, CTOs, DTMs, and STIGs)."
  checkText: "Authenticate on the Kubernetes Control Plane. Run the command:\nkubectl version --short\n\nIf kubectl version has a setting not supporting Kubernetes skew policy, this is a finding.\n\nNote: Kubernetes Skew Policy can be found at: https://kubernetes.io/docs/setup/release/version-skew-policy/#supported-versions"
  fixText: "Upgrade Kubernetes to the supported version. Institute and adhere to the policies and procedures to ensure that patches are consistently applied within the time allowed."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "MANUAL VERIFICATION REQUIRED: Kubernetes version compliance check"
    echo "=============================================================="
    
    # Display current Kubernetes version information
    echo "Current Kubernetes version information:"
    kubectl version --short 2>/dev/null || kubectl version 2>/dev/null
    
    echo ""
    echo "MANUAL VERIFICATION STEPS:"
    echo "1. Check current Kubernetes version against supported versions"
    echo "2. Verify version is within Kubernetes skew policy support"
    echo "3. Compare with latest security patches and updates"
    echo "4. Ensure compliance with IAVMs, CTOs, DTMs, and STIGs requirements"
    
    echo ""
    echo "REFERENCE LINKS:"
    echo "- Kubernetes Version Skew Policy: https://kubernetes.io/docs/setup/release/version-skew-policy/#supported-versions"
    echo "- Kubernetes Releases: https://kubernetes.io/releases/"
    echo "- Security Updates: https://kubernetes.io/docs/reference/issues-security/"
    
    echo ""
    echo "VERIFICATION CRITERIA:"
    echo "- Kubernetes version must be within supported lifecycle"
    echo "- All security patches must be applied within authorized timeframes"
    echo "- Version must comply with organizational security policies"
    
    echo ""
    echo "MANUAL VERIFICATION: Administrator must verify Kubernetes version compliance"
    echo "This check requires manual inspection of version support status and patch levels"
    
    exit 2