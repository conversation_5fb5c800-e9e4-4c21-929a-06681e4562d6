apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-protect-kernel-defaults
  namespace: compliance-system
spec:
  id: "V-242434"
  title: "Kubernetes Kubelet must enable kernel protection."
  description: "System kernel is responsible for memory, disk, and task management. The kernel provides a gateway between the system hardware and software. Kubernetes requires kernel access to allocate resources to the Control Plane. Threat actors that penetrate the system kernel can inject malicious code or hijack the Kubernetes architecture. It is vital to implement protections through Kubernetes components to reduce the attack surface."
  checkText: "On the Control Plane, run the command:\nps -ef | grep kubelet\n\nIf the \"--protect-kernel-defaults\" option exists, this is a finding.\n\nNote the path to the config file (identified by --config).\n\nRun the command:\ngrep -i protectKernelDefaults <path_to_config_file>\n\nIf the setting \"protectKernelDefaults\" is not set or is set to false, this is a finding."
  fixText: "On the Control Plane, run the command:\nps -ef | grep kubelet\n\nRemove the \"--protect-kernel-defaults\" option if present.\n\nNote the path to the Kubernetes Kubelet config file (identified by --config).\n\nEdit the Kubernetes Kubelet config file: \nSet \"protectKernelDefaults\" to \"true\". \n\nRestart the kubelet service using the following command:\nsystemctl daemon-reload && systemctl restart kubelet"
  severity: "high"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting kubelet kernel protection check (V-242434)"
    
    # First try to find kubelet process and extract config path
    echo "Searching for kubelet process to find config file..."
    kubelet_cmd=$(chroot /host ps -ef 2>/dev/null | grep kubelet | grep -v grep)
    
    config_file=""
    if [[ -n "$kubelet_cmd" ]]; then
        # Extract config file from kubelet command line
        extracted_config=$(echo "$kubelet_cmd" | grep -o -- '--config=[^ ]*' | cut -d= -f2 2>/dev/null)
        if [[ -n "$extracted_config" && ! "$extracted_config" =~ ^/host ]]; then
            extracted_config="/host$extracted_config"
        fi
        if [[ -f "$extracted_config" ]]; then
            config_file="$extracted_config"
            echo "Found kubelet config from process: $config_file"
        fi
    fi
    
    # Fallback to default location if not found from process
    if [[ -z "$config_file" ]]; then
        config_file="/host/var/lib/kubelet/config.yaml"
        echo "Using default kubelet config location: $config_file"
    fi
    
    echo "Checking kubelet configuration file: $config_file"
    
    # Check if config file exists
    if [[ ! -f "$config_file" ]]; then
        echo "FAIL: Kubelet config file not found"
        echo "Configuration file does not exist at: $config_file"
        exit 1
    fi
    
    echo "Found kubelet configuration file"
    echo "Checking protectKernelDefaults setting..."
    
    # Check if protectKernelDefaults exists in config file (preserving original logic)
    if grep "protectKernelDefaults" "$config_file" >/dev/null 2>&1; then
        echo "Found protectKernelDefaults setting in config file"
        
        # Get the value of protectKernelDefaults (preserving original logic)
        protect_value=$(yq '.protectKernelDefaults' "$config_file" 2>/dev/null)
        
        echo "protectKernelDefaults value: $protect_value"
        echo "Required value: true"
        
        # Check if value is false (preserving original logic)
        if [[ "$protect_value" == "false" ]]; then
            echo "FAIL: protectKernelDefaults not set to true"
            echo "Current value: $protect_value"
            echo "Kernel protection must be enabled for security"
            exit 1
        else
            echo "PASS: Kernel protection properly configured"
            echo "protectKernelDefaults is set to: $protect_value"
            echo "Kubelet kernel protection is enabled"
            exit 0
        fi
    else
        echo "FAIL: protectKernelDefaults not set"
        echo "protectKernelDefaults setting is missing from config file"
        echo "Kernel protection must be explicitly enabled"
        echo "Add 'protectKernelDefaults: true' to kubelet configuration"
        exit 1
    fi