apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-scheduler-tls-min-version
  namespace: compliance-system
spec:
  id: "V-242377"
  title: "The Kubernetes Scheduler must use TLS 1.2, at a minimum, to protect the confidentiality of sensitive data during electronic dissemination."
  description: "The Kubernetes Scheduler will prohibit the use of SSL and unauthorized versions of TLS protocols to properly secure communication.\n\nThe use of unsupported protocol exposes vulnerabilities to the Kubernetes by rogue traffic interceptions, man-in-the-middle attacks, and impersonation of users or services from the container platform runtime, registry, and keystore. To enable the minimum version of TLS to be used by the Kubernetes API Server, the setting \"tls-min-version\" must be set."
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i tls-min-version * \n\nIf the setting \"tls-min-version\" is not configured in the Kubernetes Scheduler manifest file or it is set to \"VersionTLS10\" or \"VersionTLS11\", this is a finding."
  fixText: "Edit the Kubernetes Scheduler manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of \"--tls-min-version\" to \"VersionTLS12\" or higher."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting V-242377: Kubernetes Scheduler TLS minimum version check"
    
    manifest_path="/host/etc/kubernetes/manifests/kube-scheduler.yaml"
    
    echo "Checking Scheduler manifest file: $manifest_path"
    
    # Check if manifest exists
    if [[ ! -f "$manifest_path" ]]; then
        echo "FAIL: Scheduler manifest file not found: $manifest_path"
        exit 1
    fi
    
    echo "Scheduler manifest file found"
    echo "Searching for --tls-min-version parameter..."
    
    # Check TLS minimum version
    tls_min_version=$(grep -oP -- "--tls-min-version=\K[^ ]+" "$manifest_path" 2>/dev/null)
    
    if [[ -z "$tls_min_version" ]]; then
        echo "FAIL: TLS minimum version parameter not configured in Scheduler"
        echo "The --tls-min-version parameter must be set to VersionTLS12 or higher"
        exit 1
    fi
    
    echo "Found TLS minimum version setting: $tls_min_version"
    
    if [[ "$tls_min_version" == "VersionTLS10" ]] || [[ "$tls_min_version" == "VersionTLS11" ]]; then
        echo "FAIL: Insecure TLS version configured: $tls_min_version"
        echo "TLS version must be VersionTLS12 or higher for security compliance"
        exit 1
    elif [[ "$tls_min_version" == "VersionTLS12" ]] || [[ "$tls_min_version" == "VersionTLS13" ]]; then
        echo "PASS: TLS minimum version correctly set to $tls_min_version"
        exit 0
    else
        echo "FAIL: Unknown or invalid TLS version: $tls_min_version"
        echo "Valid versions are: VersionTLS12, VersionTLS13"
        exit 1
    fi 