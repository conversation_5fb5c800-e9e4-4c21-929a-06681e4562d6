apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-pki-certificate-file-permissions
  namespace: compliance-system
spec:
  id: "V-242466"
  title: "The Kubernetes PKI CRT must have file permissions set to 644 or more restrictive."
  description: "The Kubernetes PKI directory contains all certificates (.crt files) supporting secure network communications in the Kubernetes Control Plane. If these files can be modified, data traversing within the architecture components would become unsecure and compromised."
  checkText: "Review the permissions of the Kubernetes PKI cert files by using the command:\n\nsudo find /etc/kubernetes/pki/* -name \"*.crt\" | xargs stat -c '%n %a'\n\nIf any of the files have permissions more permissive than \"644\", this is a finding."
  fixText: "Change the ownership of the cert files to \"644\" by executing the command: \n\nfind /etc/kubernetes/pki -name \"*.crt\" | xargs chmod 644"
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting V-242466: Kubernetes PKI certificate file permissions check"
    
    pki_dir="/host/etc/kubernetes/pki"
    failed_files=""
    has_failures=false
    
    echo "Checking PKI certificate files in directory: $pki_dir"
    
    if [[ ! -d "$pki_dir" ]]; then
        echo "FAIL: PKI directory not found: $pki_dir"
        exit 1
    fi
    
    echo "Scanning for .crt certificate files..."
    
    # Check certificate files
    crt_files=$(find "$pki_dir" -type f -name "*.crt" 2>/dev/null)
    if [[ -n "$crt_files" ]]; then
        echo "Found certificate files, checking permissions..."
        
        while IFS= read -r file; do
            if [[ -n "$file" ]]; then
                perm=$(stat -c '%a' "$file" 2>/dev/null)
                echo "Checking file: $file (permissions: $perm)"
                
                if [[ $perm -gt 644 ]]; then
                    echo "FAIL: Excessive permissions $perm for certificate $file (should be ≤644)"
                    failed_files+="${failed_files:+, }$(basename "$file"):$perm"
                    has_failures=true
                else
                    echo "PASS: Valid permissions $perm for certificate $file"
                fi
            fi
        done <<< "$crt_files"
    else
        echo "FAIL: No PKI certificate files found in $pki_dir"
        exit 1
    fi
    
    if [[ "$has_failures" == true ]]; then
        echo "FAIL: Some certificate files have excessive permissions: $failed_files"
        exit 1
    else
        echo "PASS: All PKI certificate files have compliant permissions (≤644)"
        exit 0
    fi