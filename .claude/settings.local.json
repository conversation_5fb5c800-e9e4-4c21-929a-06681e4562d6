{"permissions": {"allow": ["Bash(go build ./pkg/controller/scan)", "Bash(rg -n \"createNodeScanJobs.*func\" /Users/<USER>/work/go/src/gitlab-ce.alauda.cn/compliance-operator-1/pkg/controller/scan/)", "Bash(rg -n \"createNodeScanJobs\" /Users/<USER>/work/go/src/gitlab-ce.alauda.cn/compliance-operator-1/pkg/controller/scan/)", "Bash(go test ./pkg/controller/scan -run \"TestCreateNodeScanJobs$\" -v)", "Bash(go test -v ./pkg/controller/scan -run \".*Cleanup.*\")", "Bash(kubectl logs comp-control-plane-ait-chaowang1-s4202-5c32465c-m49g-jjw46 -n compliance-system)", "Bash(chmod +x /Users/<USER>/work/go/src/gitlab-ce.alauda.cn/compliance-operator-1/test_filename_fix.sh)", "Bash(rg -n \"strings\\.Join|fmt\\.Sprintf.*newline|\\\\n\" /Users/<USER>/work/go/src/gitlab-ce.alauda.cn/compliance-operator-1/pkg/controller/scan/ --type go)", "Bash(rg -n \"IFS=|read.*-r|while.*read|echo.*\\n\" /Users/<USER>/work/go/src/gitlab-ce.alauda.cn/compliance-operator-1/images/unified-scanner/scripts/)", "Bash(rg -n \"IFS=|while.*read|echo.*newline\" /Users/<USER>/work/go/src/gitlab-ce.alauda.cn/compliance-operator-1/images/unified-scanner/scripts/)", "Bash(rg -n -A 20 \"type.*RuleResult.*struct\" /Users/<USER>/work/go/src/gitlab-ce.alauda.cn/compliance-operator-1/pkg/apis/compliance/v1alpha1/types.go)", "Bash(rg -n -A 10 -B 5 \"NodeResults.*\\[\\]\" /Users/<USER>/work/go/src/gitlab-ce.alauda.cn/compliance-operator-1/pkg/apis/compliance/v1alpha1/types.go)", "Bash(rg -n \"RuleID.*=.*fmt\\.Sprintf|RuleName.*=.*fmt\\.Sprintf|Severity.*=.*fmt\\.Sprintf\" /Users/<USER>/work/go/src/gitlab-ce.alauda.cn/compliance-operator-1/pkg/controller/scan/ --type go)", "Bash(kubectl get scan -n compliance-system scan-stig-k8s-v2r2-general -o yaml)", "Bash(kubectl get pods -n compliance-system --sort-by=.metadata.creationTimestamp)", "Bash(kubectl get checkresult -n compliance-system --sort-by=.metadata.creationTimestamp)", "Bash(kubectl get checkresult -n compliance-system checkresult-scan-stig-k8s-v2r2-general-d4353933-20250707-080834-wgps -o yaml)", "Bash(kubectl get pod -n compliance-system comp-control-plane-ait-chaowang1-s4202-5c32465c-8i0w-vhwcm -o yaml)", "<PERSON><PERSON>(git add -A)", "Bash(git commit -m \"Fix consolidated scanner rule processing\n\n- Fix processConsolidatedJobResults to use updateRuleResult for proper rule merging\n- Fix filename too long issues in consolidated-scanner.sh with MD5 hashing  \n- Improve error handling for base64 decoding and exit code processing\n- Use crypto/rand for secure random string generation\n- Fix ConfigMap cleanup race conditions\n\n🤖 Generated with [Claude Code](https://claude.ai/code)\n\nCo-Authored-By: <PERSON> <<EMAIL>>\")", "Bash(git push origin feat/improve_check)", "Bash(chmod +x /Users/<USER>/work/go/src/gitlab-ce.alauda.cn/compliance-operator-1/test_rule_parsing.sh)", "Bash(/Users/<USER>/work/go/src/gitlab-ce.alauda.cn/compliance-operator-1/test_rule_parsing.sh)", "Bash(rm -f /Users/<USER>/work/go/src/gitlab-ce.alauda.cn/compliance-operator-1/test_rule_parsing.sh)", "Bash(grep -n \"processConsolidatedJobResults.*context\" /Users/<USER>/work/go/src/gitlab-ce.alauda.cn/compliance-operator-1/pkg/controller/scan/result_consistency_test.go)", "Bash(kubectl get checkresult -A -l compliance-operator.alauda.io/scan=scan-stig-k8s-v2r2-general)", "Bash(kubectl get checkresult checkresult-scan-stig-k8s-v2r2-general-d4353933-20250707-092344-5agw -n compliance-system -o yaml)", "Bash(kubectl get profile stig-k8s-v2r2-general -n compliance-system -o yaml)", "Bash(kubectl logs -n compliance-system deployment/compliance-operator --tail=500)", "Bash(kubectl logs -n compliance-system deployment/compliance-operator --tail=1000)", "Bash(kubectl logs comp-control-plane-ait-chaowang1-s4202-5c32465c-1b9l-zlswt -n compliance-system --tail=50)", "Bash(grep -n \"jq.*\\..*\" /Users/<USER>/work/go/src/gitlab-ce.alauda.cn/compliance-operator-1/images/unified-scanner/scripts/consolidated-scanner.sh)", "Bash(kubectl get pods -n compliance-system)", "Bash(kubectl get jobs -n compliance-system)", "Bash(kubectl logs comp-control-plane-ait-chaowang1-s4202-5c32465c-oicy-nn9p4 -n compliance-system)", "Bash(kubectl describe pod comp-control-plane-ait-chaowang1-s4202-5c32465c-oicy-nn9p4 -n compliance-system)", "Bash(kubectl get configmap -n compliance-system)", "Bash(kubectl get configmap -n compliance-system -l compliance-operator.alauda.io/scan=scan-stig-k8s-v2r2-general)", "Bash(kubectl get configmap comp-all-ait-chaowang1-s4202-0f5ea29f-7x9t-787aca06 -n compliance-system -o yaml)", "Bash(kubectl get checkresult -n compliance-system)", "Bash(kubectl describe checkresult checkresult-scan-stig-k8s-v2r2-general-d4353933-20250707-164848-mven -n compliance-system)", "Bash(kubectl get checkresult checkresult-scan-stig-k8s-v2r2-general-d4353933-20250707-164848-mven -n compliance-system -o yaml)", "Bash(kubectl get checkresult checkresult-scan-stig-k8s-v2r2-general-d4353933-20250707-164848-mven -n compliance-system -o jsonpath='{.spec.check_results}')"], "deny": []}}