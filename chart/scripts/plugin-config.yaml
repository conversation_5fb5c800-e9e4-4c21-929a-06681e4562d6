supportedUpgradeVersions: ">= v1.0.0"
mustUpgrade: false
config:
  reportStorage:
    storageClass: ""
    size: 1Gi
valuesTemplates:
  ait/chart-compliance-operator: |
    reportStorage:
      storageClass: << .MiConfig.reportStorage.storageClass | quote >>
      size: << .MiConfig.reportStorage.size | quote >>
deployDescriptors:
  - path: reportStorage.storageClass
    x-descriptors:
      - 'urn:alm:descriptor:io.kubernetes:StorageClass'
      - 'urn:alm:descriptor:com.tectonic.ui:validation:required'
      - 'urn:alm:descriptor:label:en:StorageClass'
      - 'urn:alm:descriptor:label:zh:存储类'
      - 'urn:alm:descriptor:description:en:Report persistence storage class.'
      - 'urn:alm:descriptor:description:zh:报告持久化存储类。'