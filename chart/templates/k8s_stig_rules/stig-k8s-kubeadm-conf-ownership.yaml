apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubeadm-conf-ownership
  namespace: compliance-system
spec:
  id: "V-242454"
  title: "Kubernetes kubeadm.conf must be root-owned"
  description: "Kubernetes kubeadm.conf file must be owned by root to prevent unauthorized modifications."
  fixText: "Change the ownership of the kubeadm.conf to root: root by executing the command:\n\nchown root:root \nkubeadm.conf path\n\n"
  checkText: "Review the Kubeadm.conf file :\n\nGet the path for Kubeadm.conf by running: \nsytstemctl status kubelet\n\nNote the configuration file installed by the kubeadm is written to \n(Default Location: /etc/systemd/system/kubelet.service.d/10-kubeadm.conf)\nstat -c %U:%G <kubeadm.conf path> | grep -v root:root\n\nIf the command returns any non root:root file permissions, this is a finding. "
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting kubeadm.conf ownership check (V-242454)"
    
    # Define kubeadm configuration file path (preserving original logic)
    kubeadm_conf="/host/usr/lib/systemd/system/kubelet.service.d/10-kubeadm.conf"

    echo "Checking kubeadm configuration file: $kubeadm_conf"

    # Check if kubeadm config file exists (preserving original logic)
    if [[ -f "$kubeadm_conf" ]]; then
        echo "Found kubeadm configuration file"
        echo "Checking file ownership..."
        
        # Get file ownership (preserving original logic)
        ownership=$(stat -c '%U:%G' "$kubeadm_conf" 2>/dev/null)
        
        echo "Current ownership: $ownership"
        echo "Required ownership: root:root"
        
        # Validate ownership (preserving original logic)
        if [[ "$ownership" != "root:root" ]]; then
            echo "FAIL: Invalid ownership for kubeadm.conf"
            echo "File: $kubeadm_conf"
            echo "Current: $ownership"
            echo "Expected: root:root"
            echo "Kubeadm config must be owned by root for security"
            exit 1
        else
            echo "PASS: Kubeadm.conf has proper ownership"
            echo "File ownership is correctly set to root:root"
            exit 0
        fi
    else
        echo "FAIL: Kubeadm config missing"
        echo "Expected to find configuration file at $kubeadm_conf"
        exit 1
    fi 