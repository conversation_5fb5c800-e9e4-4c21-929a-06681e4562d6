apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubectl-version
  namespace: compliance-system
spec:
  id: "V-242396"
  title: "Kubernetes must use kubectl v1.12.9+"
  description: "Kubernetes kubectl is the command line tool used to interact with the Kubernetes API. Older versions of kubectl may contain security vulnerabilities that have been fixed in newer versions. Using an outdated version of kubectl can expose the cluster to security risks."
  checkText: "On the Control Plane, run the command:\nkubectl version --client\n\nIf the client version is less than v1.12.9, this is a finding."
  fixText: "Update kubectl to a version that is at least v1.12.9 or higher. Follow the installation instructions for your operating system from the Kubernetes documentation."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting kubectl version check (V-242396)"
    
    # Define minimum required version (preserving original logic)
    min_version="1.12.9"
    
    echo "Checking kubectl client version..."
    echo "Minimum required version: v$min_version"
    
    # Get kubectl version (preserving original logic)
    version_output=$(chroot /host kubectl version --client 2>&1 | grep "Client Version")
    
    if [[ -z "$version_output" ]]; then
        echo "FAIL: kubectl not found or unable to determine version"
        echo "kubectl command failed or returned no client version information"
        exit 1
    fi
    
    echo "Found kubectl version output: $version_output"
    echo "Extracting client version..."
    
    # Extract client version (preserving original logic)
    client_version=$(echo "$version_output" | awk -F'v' '{print $2}' | awk -F'.' '{print $1"."$2"."$3}')
    
    if [[ -z "$client_version" ]]; then
        echo "FAIL: Unable to parse kubectl client version"
        echo "Could not extract version from kubectl output"
        exit 1
    fi
    
    echo "Client version: v$client_version"
    echo "Comparing with minimum required version: v$min_version"
    
    # Version comparison logic (preserving original logic)
    if [[ "$(printf '%s\n' "$min_version" "$client_version" | sort -V | head -n1)" != "$min_version" ]]; then
        echo "FAIL: Outdated kubectl version"
        echo "Current version: v$client_version"
        echo "Minimum required: v$min_version"
        echo "kubectl must be updated to meet security requirements"
        exit 1
    else
        echo "PASS: kubectl version is compliant"
        echo "Current version v$client_version meets minimum requirement v$min_version"
        exit 0
    fi 