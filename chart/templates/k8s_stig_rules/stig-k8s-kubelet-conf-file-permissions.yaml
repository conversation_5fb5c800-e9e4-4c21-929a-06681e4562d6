apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-conf-file-permissions
  namespace: compliance-system
spec:
  id: "V-242452"
  title: "Kubernetes kubelet KubeConfig permissions must ≤644"
  description: "The Kubernetes kubelet agent registers nodes with the API Server and performs health checks to containers within pods. If this file can be modified, the information system would be unaware of pod or container degradation."
  checkText: "Review the Kubernetes Kubeadm kubelet conf file by using the command:\n\nstat -c %a /etc/kubernetes/kubelet.conf\n\nIf the file has permissions more permissive than \"644\", this is a finding."
  fixText: "Change the permissions of the kubelet config to \"644\" by executing the command:\n\nchmod 644 /etc/kubernetes/kubelet.conf"
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting kubelet config file permissions check (V-242452)"
    
    # Define kubelet config file path (preserving original logic)
    kubelet_conf="/host/etc/kubernetes/kubelet.conf"
    
    echo "Checking kubelet configuration file: $kubelet_conf"
    
    # Check if kubelet config file exists (preserving original logic)
    if [[ ! -f "$kubelet_conf" ]]; then
        echo "FAIL: Kubelet config missing"
        echo "Kubelet configuration file not found at: $kubelet_conf"
        echo "Expected kubelet.conf file must exist for permissions verification"
        exit 1
    fi
    
    echo "Found kubelet configuration file"
    echo "Checking file permissions..."
    
    # Check file permissions (preserving original logic)
    perm=$(stat -c '%a' "$kubelet_conf" 2>/dev/null)
    
    if [[ -z "$perm" ]]; then
        echo "FAIL: Unable to read file permissions"
        echo "Cannot determine permissions for: $kubelet_conf"
        exit 1
    fi
    
    echo "Current file permissions: $perm"
    echo "Maximum allowed permissions: 644"
    
    # Validate permissions (preserving original logic)
    if [[ $perm -gt 644 ]]; then
        echo "FAIL: Excessive permissions"
        echo "File: $kubelet_conf"
        echo "Current permissions: $perm"
        echo "Maximum allowed permissions: 644"
        echo "Kubelet config file permissions must be 644 or more restrictive"
        exit 1
    else
        echo "PASS: Kubelet.conf has compliant permissions"
        echo "File: $kubelet_conf"
        echo "Permissions: $perm (compliant)"
        exit 0
    fi 