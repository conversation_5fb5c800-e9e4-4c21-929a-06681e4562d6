apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kube-proxy-conf-file-ownership
  namespace: compliance-system
spec:
  id: "V-242448"
  title: "The Kubernetes Kube Proxy kubeconfig must be owned by root."
  description: "The Kubernetes Kube Proxy kubeconfig contain the argument and setting for the Control Planes. These settings contain network rules for restricting network communication between pods, clusters, and networks. If these files can be changed, data traversing between the Kubernetes Control Panel components would be compromised. Many of the security settings within the document are implemented through this file."
  checkText: "Check if Kube-Proxy is running use the following command:\nps -ef | grep kube-proxy\n\nIf Kube-Proxy exists:\nReview the permissions of the Kubernetes Kube Proxy by using the command:\nstat -c %U:%G <location from --kubeconfig>| grep -v root:root\n\nIf the command returns any non root:root file permissions, this is a finding."
  fixText: "Change the ownership of the Kube Proxy to root:root by executing the command:\n\nchown root:root <location from kubeconfig>."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash

    echo "Starting kube-proxy kubeconfig ownership check (V-242448)"
    
    # Define kube-proxy configuration path (preserving original logic)
    kube_proxy_conf="/var/lib/kube-proxy/..data/config.conf"

    echo "Checking for kube-proxy pod in kube-system namespace..."
    
    # Find kube-proxy pod (preserving original logic)
    pod_name=$(kubectl get pod -n kube-system | grep kube-proxy | awk '{print $1}' | head -1 2>/dev/null)

    if [[ -z "$pod_name" ]]; then
        echo "FAIL: No kube-proxy pod found in kube-system namespace"
        echo "Expected to find kube-proxy pod for configuration check"
        exit 1
    fi

    echo "Found kube-proxy pod: $pod_name"
    echo "Checking kube-proxy configuration file: $kube_proxy_conf"

    # Check if kube-proxy config file exists (preserving original logic)
    if kubectl exec -it -n kube-system $pod_name -- ls /var/lib/kube-proxy/..data/config.conf 2>/dev/null >/dev/null; then
        echo "Found kube-proxy configuration file"
        echo "Checking file ownership..."
        
        # Get file ownership (preserving original logic)
        ownership=$(kubectl exec -it -n kube-system $pod_name -- stat -c '%U:%G' /var/lib/kube-proxy/..data/config.conf 2>/dev/null | tr -d '\r')
        
        echo "Current ownership: $ownership"
        echo "Required ownership: root:root"
        
        # Validate ownership (preserving original logic)
        if [[ "$ownership" != 'root:root' ]]; then
            echo "FAIL: Invalid ownership for kube-proxy config"
            echo "File: $kube_proxy_conf"
            echo "Current: $ownership"
            echo "Expected: root:root"
            echo "Kube-proxy config must be owned by root for security"
            exit 1
        else
            echo "PASS: Kube-proxy config has proper ownership"
            echo "File ownership is correctly set to root:root"
            exit 0
        fi
    else
        echo "FAIL: Kube-proxy config missing"
        echo "Expected to find configuration file at $kube_proxy_conf"
        exit 1
    fi 