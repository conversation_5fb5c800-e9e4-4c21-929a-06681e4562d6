apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-pki-directory-ownership
  namespace: compliance-system
spec:
  id: "V-242451"
  title: "Kubernetes PKI files must be root-owned"
  description: "The Kubernetes PKI directory contains all certificates and key files supporting secure network communications in the Kubernetes Control Plane. If these files can be modified, data traversing within the architecture components would become unsecure and compromised."
  checkText: "Review the ownership of the Kubernetes PKI files by using the command:\n\nsudo find /etc/kubernetes/pki -type f | xargs stat -c '%n %U:%G' | grep -v root:root\n\nIf the command returns any non root:root file permissions, this is a finding."
  fixText: "Change the ownership of the PKI files to root: root by executing the command:\n\nsudo find /etc/kubernetes/pki -type f | xargs chown root:root"
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting V-242451: Kubernetes PKI files ownership check"
    
    pki_dir="/host/etc/kubernetes/pki"
    failed_files=""
    has_failures=false
    
    echo "Checking PKI files ownership in directory: $pki_dir"
    
    if [[ -d "$pki_dir" ]]; then
        echo "PKI directory found, scanning files for ownership compliance..."
        
        while IFS= read -r file; do
            if [[ -n "$file" ]]; then
                ownership=$(stat -c '%U:%G' "$file" 2>/dev/null)
                echo "Checking file: $file (owner: $ownership)"
                
                if [[ "$ownership" != "root:root" ]]; then
                    echo "FAIL: Invalid PKI ownership $ownership for $file (should be root:root)"
                    failed_files+="${failed_files:+, }$(basename "$file"):$ownership"
                    has_failures=true
                else
                    echo "PASS: Correct ownership $ownership for $file"
                fi
            fi
        done < <(find "$pki_dir" -type f 2>/dev/null)
    else
        echo "FAIL: PKI directory not found: $pki_dir"
        exit 1
    fi
    
    if [[ "$has_failures" == true ]]; then
        echo "FAIL: Some PKI files have incorrect ownership: $failed_files"
        exit 1
    else
        echo "PASS: All PKI files have proper ownership (root:root)"
        exit 0
    fi