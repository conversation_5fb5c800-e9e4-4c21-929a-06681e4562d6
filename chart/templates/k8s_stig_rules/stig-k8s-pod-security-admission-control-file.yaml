apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-pod-security-admission-control-file
  namespace: compliance-system
spec:
  id: "V-254800"
  title: "Kubernetes must have a Pod Security Admission control file configured."
  description: "Kubernetes must have a Pod Security Admission control file configured to enforce security policies."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--admission-control-config-file\" to a valid path for the file.\n\nCreate an admission control file with the following content:\n\napiVersion: apiserver.config.k8s.io/v1\nkind: AdmissionConfiguration\nplugins:\n- name: PodSecurity\nconfiguration:\napiVersion: pod-security.admission.config.k8s.io/v1beta1\nkind: PodSecurityConfiguration\ndefaults:\nenforce: restricted\nenforce-version: latest\naudit: restricted\naudit-version: latest\nlog-level: 3\nlog-backtrace-limit: 1\n\nInspect the .yaml file defined by the --admission-control-config-file. Verify PodSecurity is properly configured. \nIf least privilege is not represented, this is a finding."
  checkText: "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i admission-control-config-file *\n\nIf the setting \"--admission-control-config-file\" is not configured in the Kubernetes API Server manifest file, this is a finding.\n\nInspect the .yaml file defined by the --admission-control-config-file. Verify PodSecurity is properly configured. \nIf least privilege is not represented, this is a finding."
  severity: "high"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting V-254800: Kubernetes Pod Security Admission control file check"
    
    apiserver_manifest="/host/etc/kubernetes/manifests/kube-apiserver.yaml"
    
    echo "Checking API Server manifest for admission control configuration: $apiserver_manifest"
    
    if [[ ! -f "$apiserver_manifest" ]]; then
        echo "FAIL: API Server manifest file not found: $apiserver_manifest"
        exit 1
    fi
    
    echo "Scanning for --admission-control-config-file parameter..."
    
    # API Server Parameter Check
    manifest_check=$(grep -Po -- "--admission-control-config-file(=\S+|\s+\S+)" "$apiserver_manifest" 2>/dev/null)
    
    if [[ -z "$manifest_check" ]]; then
        echo "FAIL: API Server missing --admission-control-config-file parameter"
        echo "The API Server must be configured with --admission-control-config-file parameter"
        exit 1
    else
        echo "PASS: Found admission control configuration: $manifest_check"
        echo "Pod Security Admission control file is properly configured"
        exit 0
    fi