apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-config-file-ownership
  namespace: compliance-system
spec:
  id: "V-242457"
  title: "The Kubernetes kubelet configuration file must be owned by root."
  description: "The kubelet configuration file contains settings for the Kubernetes worker nodes. If this file can be modified by non-privileged users, the integrity and availability of the Kubernetes cluster could be compromised."
  checkText: "Check the ownership of the kubelet configuration file by running the command:\n\nstat -c %U:%G /var/lib/kubelet/config.yaml\n\nIf the file is not owned by root:root, this is a finding."
  fixText: "Change the ownership of the kubelet configuration file to root:root by executing the command:\n\nchown root:root /var/lib/kubelet/config.yaml"
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting kubelet configuration file ownership check (V-242457)"
    
    # Define kubelet config file path (preserving original logic)
    kubelet_config="/host/var/lib/kubelet/config.yaml"
    
    echo "Checking kubelet configuration file: $kubelet_config"
    
    # Check if kubelet config file exists (preserving original logic)
    if [[ ! -f "$kubelet_config" ]]; then
        echo "FAIL: Kubelet config missing"
        echo "Kubelet configuration file not found at: $kubelet_config"
        echo "Expected config.yaml file must exist for ownership verification"
        exit 1
    fi
    
    echo "Found kubelet configuration file"
    echo "Checking file ownership..."
    
    # Check file ownership (preserving original logic)
    ownership=$(stat -c '%U:%G' "$kubelet_config" 2>/dev/null)
    
    if [[ -z "$ownership" ]]; then
        echo "FAIL: Unable to read file ownership"
        echo "Cannot determine ownership for: $kubelet_config"
        exit 1
    fi
    
    echo "Current file ownership: $ownership"
    echo "Required ownership: root:root"
    
    # Validate ownership (preserving original logic)
    if [[ "$ownership" != "root:root" ]]; then
        echo "FAIL: Invalid ownership"
        echo "File: $kubelet_config"
        echo "Current ownership: $ownership"
        echo "Required ownership: root:root"
        echo "Kubelet config file must be owned by root:root for security"
        exit 1
    else
        echo "PASS: Kubelet config has proper ownership"
        echo "File: $kubelet_config"
        echo "Ownership: $ownership (compliant)"
        exit 0
    fi 