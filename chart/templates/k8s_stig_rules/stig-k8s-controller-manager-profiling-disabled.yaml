apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-controller-manager-profiling-disabled
  namespace: compliance-system
spec:
  id: "V-242409"
  title: "Kubernetes Controller Manager must disable profiling."
  description: "Kubernetes profiling provides the ability to analyze and troubleshoot Controller Manager events over a web interface on a host port. Enabling this service can expose details about the Kubernetes architecture. This service must not be enabled unless deemed necessary."
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i profiling * \n\nIf the setting \"profiling\" is not configured in the Kubernetes Controller Manager manifest file or it is set to \"True\", this is a finding."
  fixText: "Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument \"--profiling value\" to \"false\"."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting Controller Manager profiling configuration check (V-242409)"
    
    manifests_dir="/host/etc/kubernetes/manifests"
    
    echo "Checking for Controller Manager manifest files in: $manifests_dir"
    
    # Check if manifests directory exists
    if [[ ! -d "$manifests_dir" ]]; then
        echo "FAIL: Kubernetes manifests directory not found: $manifests_dir"
        exit 1
    fi
    
    # Find Controller Manager manifest files (preserving original logic)
    cm_files=$(grep -lis 'kube-controller-manager' "$manifests_dir"/* 2>/dev/null)
    
    if [[ -z "$cm_files" ]]; then
        echo "FAIL: No Controller Manager manifest found in $manifests_dir"
        echo "Expected to find files containing 'kube-controller-manager'"
        exit 1
    fi
    
    echo "Found Controller Manager manifest files:"
    echo "$cm_files"
    
    # Check each Controller Manager manifest file (preserving original logic)
    for file in $cm_files; do
        echo "Analyzing manifest file: $file"
        
        # Search for profiling configuration (preserving original logic)
        profiling=$(grep -i -- "--profiling" "$file" 2>/dev/null)
        
        if [[ -n "$profiling" ]]; then
            echo "Found profiling configuration: $profiling"
            
            # Check if profiling is set to true (preserving original logic)
            if echo "$profiling" | grep -q "=true"; then
                echo "FAIL: Profiling enabled in $file"
                echo "Configuration found: $profiling"
                echo "Profiling must be explicitly disabled for security"
                exit 1
            fi
            
            echo "Profiling is properly configured (not set to true)"
        else
            # Default is enabled if not set (preserving original logic)
            echo "FAIL: Profiling not explicitly disabled in $file"
            echo "No --profiling configuration found"
            echo "Default behavior enables profiling, which is a security risk"
            echo "Must explicitly set --profiling=false"
            exit 1
        fi
        
        echo "Profiling check passed for $file"
    done
    
    echo "PASS: Profiling properly disabled"
    exit 0 