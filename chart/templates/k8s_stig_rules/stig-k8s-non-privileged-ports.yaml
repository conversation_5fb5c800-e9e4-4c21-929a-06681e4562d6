apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-non-privileged-ports
  namespace: compliance-system
spec:
  id: "V-242414"
  title: "The Kubernetes cluster must use non-privileged host ports for user pods."
  description: "Privileged ports are those ports below 1024 and that require system privileges for their use. If containers can use these ports, the container must be run as a privileged user. Kubernetes must stop containers that try to map to these ports directly. Allowing non-privileged ports to be mapped to the container-privileged port is the allowable method when a certain port is needed. An example is mapping port 8080 externally to port 80 in the container."
  checkText: "On the Control Plane, run the command:\nkubectl get pods --all-namespaces\n\nThe list returned is all pods running within the Kubernetes cluster. For those pods running within the user namespaces (System namespaces are kube-system, kube-node-lease and kube-public), run the command:\nkubectl get pod podname -o yaml | grep -i port\n\nNote: In the above command, \"podname\" is the name of the pod. For the command to work correctly, the current context must be changed to the namespace for the pod. The command to do this is:\n\nkubectl config set-context --current --namespace=namespace-name\n(Note: \"namespace-name\" is the name of the namespace.)\n\nReview the ports that are returned for the pod.\n\nIf any host-privileged ports are returned for any of the pods, this is a finding."
  fixText: "For any of the pods that are using host-privileged ports, reconfigure the pod to use a service to map a host non-privileged port to the pod port or reconfigure the image to use non-privileged ports."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting V-242414: Kubernetes non-privileged host ports check"
    
    system_ns=("kube-system" "kube-public" "kube-node-lease")
    vulnerable_pods=()
    
    echo "Checking for pods using privileged host ports (≤1024) in user namespaces..."
    echo "System namespaces excluded: ${system_ns[*]}"
    
    all_pods=$(kubectl get pods --all-namespaces --no-headers 2>/dev/null | grep -vE "$(IFS="|"; echo "${system_ns[*]}")")
    
    if [[ -z "$all_pods" ]]; then
        echo "PASS: No pods found in user namespaces"
        exit 0
    fi
    
    echo "Found pods in user namespaces, checking for privileged host ports..."
    
    while read -r line; do
        if [[ -n "$line" ]]; then
            namespace=$(chroot /host awk '{print $1}' <<< "$line")
            podname=$(chroot /host awk '{print $2}' <<< "$line")
            
            echo "Checking pod: $namespace/$podname"
            
            ports=$(kubectl get pod -n "$namespace" "$podname" -o jsonpath='{range .spec.containers[*].ports[*]}{.hostPort} {end}{range .spec.initContainers[*].ports[*]}{.hostPort} {end}' 2>/dev/null)
            
            found=0
            for port in $ports; do
                if [[ $port =~ ^[0-9]+$ ]] && [[ "$port" -le 1024 ]] && [[ "$port" -gt 0 ]]; then
                    echo "  FAIL: Found privileged host port $port in pod $namespace/$podname"
                    found=1
                    break
                else
                    if [[ $port =~ ^[0-9]+$ ]]; then
                        echo "  PASS: Non-privileged host port $port in pod $namespace/$podname"
                    fi
                fi
            done
            
            if [[ $found -eq 1 ]]; then
                vulnerable_pods+=("$namespace/$podname (ports: $ports)")
            fi
        fi
    done <<< "$all_pods"
    
    if [[ ${#vulnerable_pods[@]} -gt 0 ]]; then
        echo "FAIL: Found ${#vulnerable_pods[@]} pods with privileged host ports:"
        for pod in "${vulnerable_pods[@]}"; do
            echo "  - $pod"
        done
        exit 1
    else
        echo "PASS: No privileged host ports detected in user namespaces"
        exit 0
    fi