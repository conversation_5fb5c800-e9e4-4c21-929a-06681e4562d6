package scan

import (
	"context"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

func TestGenerateJobName(t *testing.T) {
	testCases := []struct {
		name          string
		checkType     string
		ruleName      string
		nodeName      string
		expectedStart string
		maxLength     int
	}{
		{
			name:          "Platform check",
			checkType:     "platform",
			ruleName:      "stig-k8s-api-server-anonymous-auth-disabled",
			nodeName:      "",
			expectedStart: "stig-k8s-api-server-anonymous-auth-disabled-",
			maxLength:     63,
		},
		{
			name:          "Node check",
			checkType:     "node",
			ruleName:      "stig-k8s-kubelet-anonymous-auth-disabled",
			nodeName:      "worker-node-1",
			expectedStart: "stig-k8s-kubelet-anonymous-auth-disabled-worker-node-1-",
			maxLength:     63,
		},
		{
			name:          "Very long rule name",
			checkType:     "platform",
			ruleName:      "this-is-a-very-long-rule-name-that-exceeds-the-kubernetes-resource-name-length-limit-and-needs-to-be-truncated",
			nodeName:      "",
			expectedStart: "", // 不检查前缀，因为会被截断
			maxLength:     63,
		},
		{
			name:          "Special characters",
			checkType:     "platform",
			ruleName:      "rule_with.special@characters!",
			nodeName:      "",
			expectedStart: "rule-with-special-characters-",
			maxLength:     63,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			jobName := generateJobName(tc.checkType, tc.ruleName, tc.nodeName)

			// 检查名称长度
			assert.LessOrEqual(t, len(jobName), tc.maxLength)

			// 检查名称前缀（如果有期望的前缀）
			if tc.expectedStart != "" {
				assert.True(t, strings.HasPrefix(jobName, tc.expectedStart))
			}

			// 检查是否包含随机后缀
			parts := strings.Split(jobName, "-")
			suffix := parts[len(parts)-1]
			assert.Equal(t, 5, len(suffix))

			// 检查是否符合 Kubernetes 命名规范
			assert.Regexp(t, "^[a-z0-9]([-a-z0-9]*[a-z0-9])?$", jobName)
		})
	}
}

func TestCleanKubernetesName(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Simple name",
			input:    "simple-name",
			expected: "simple-name",
		},
		{
			name:     "Uppercase letters",
			input:    "UPPERCASE",
			expected: "uppercase",
		},
		{
			name:     "Special characters",
			input:    "name_with.special@chars!",
			expected: "name-with-special-chars",
		},
		{
			name:     "Leading special characters",
			input:    "-_leading",
			expected: "leading",
		},
		{
			name:     "Trailing special characters",
			input:    "trailing-_",
			expected: "trailing",
		},
		{
			name:     "Multiple consecutive dashes",
			input:    "multiple---dashes",
			expected: "multiple-dashes",
		},
		{
			name:     "Empty string",
			input:    "",
			expected: "default",
		},
		{
			name:     "Only special characters",
			input:    "-_@!.",
			expected: "default",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := cleanKubernetesName(tc.input)
			assert.Equal(t, tc.expected, result)
		})
	}
}

func TestInitializeScan(t *testing.T) {
	// 创建一个带有 scanID 的 Scan
	scanID := "test-scan-12345678-20230101-120000-abcd"
	scan := &complianceapi.Scan{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-scan",
			Namespace: "default",
			Annotations: map[string]string{
				"compliance-operator.alauda.io/current-scan-id": scanID,
			},
		},
		Spec: complianceapi.ScanSpec{
			Profile: "test-profile",
		},
	}

	// 创建 Profile
	profile := &complianceapi.Profile{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-profile",
			Namespace: "default",
		},
		Spec: complianceapi.ProfileSpec{
			Rules: []complianceapi.RuleReference{
				{
					Name: "test-rule-1",
				},
				{
					Name: "test-rule-2",
				},
			},
		},
	}

	// 创建规则
	rule1 := &complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-rule-1",
			Namespace: "default",
		},
		Spec: complianceapi.RuleSpec{
			Title:       "Test Rule 1",
			Description: "Rule for testing",
			CheckType:   "platform",
			CheckScript: "echo 'Test script 1'",
		},
	}

	rule2 := &complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-rule-2",
			Namespace: "default",
		},
		Spec: complianceapi.RuleSpec{
			Title:       "Test Rule 2",
			Description: "Rule for testing",
			CheckType:   "platform",
			CheckScript: "echo 'Test script 2'",
		},
	}

	// 设置 reconciler
	r := setupReconciler(t, scan, profile, rule1, rule2)

	// 执行 initializeScan
	result, err := r.initializeScan(context.Background(), scan)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 由于 fake client 的限制，我们可能无法直接更新 Status 子资源
	// 这里我们只验证 initializeScan 是否成功执行
	updatedScan := &complianceapi.Scan{}
	err = r.Get(context.Background(), types.NamespacedName{Name: "test-scan", Namespace: "default"}, updatedScan)
	assert.NoError(t, err)
	assert.NotNil(t, updatedScan)
}

func TestCreatePlatformScanJobs(t *testing.T) {
	// 创建测试资源
	scan := &complianceapi.Scan{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-scan",
			Namespace: "default",
		},
		Spec: complianceapi.ScanSpec{
			Profile:  "test-profile",
			ScanType: "platform",
		},
	}

	rule1 := &complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-rule-1",
			Namespace: "default",
		},
		Spec: complianceapi.RuleSpec{
			Title:       "Test Rule 1",
			Description: "Rule for testing",
			CheckType:   "platform",
			CheckScript: "echo 'Test script 1'",
		},
	}

	rule2 := &complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-rule-2",
			Namespace: "default",
		},
		Spec: complianceapi.RuleSpec{
			Title:       "Test Rule 2",
			Description: "Rule for testing",
			CheckType:   "node", // 这个规则不应该被处理
			CheckScript: "echo 'Test script 2'",
		},
	}

	// 设置 reconciler
	r := setupReconciler(t, scan, rule1, rule2)

	// 执行 createPlatformScanJobs
	scanID := "test-scan-id"
	rules := []complianceapi.Rule{*rule1, *rule2}
	err := r.createPlatformScanJobs(context.Background(), scan, rules, scanID)

	// 验证结果
	assert.NoError(t, err)

	// 由于 fake client 的限制，我们可能无法直接验证 Job 创建
	// 这里我们只验证 createPlatformScanJobs 是否成功执行
}

func TestCreateNodeScanJobs(t *testing.T) {
	// 创建测试资源
	scan := &complianceapi.Scan{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-scan",
			Namespace: "default",
		},
		Spec: complianceapi.ScanSpec{
			Profile:      "test-profile",
			ScanType:     "node",
			NodeSelector: map[string]string{"role": "worker"},
		},
	}

	rule1 := &complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-rule-1",
			Namespace: "default",
		},
		Spec: complianceapi.RuleSpec{
			Title:       "Test Rule 1",
			Description: "Rule for testing",
			CheckType:   "node",
			CheckScript: "echo 'Test script 1'",
		},
	}

	rule2 := &complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-rule-2",
			Namespace: "default",
		},
		Spec: complianceapi.RuleSpec{
			Title:       "Test Rule 2",
			Description: "Rule for testing",
			CheckType:   "platform", // 这个规则不应该被处理
			CheckScript: "echo 'Test script 2'",
		},
	}

	node1 := &corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "worker-node-1",
			Labels: map[string]string{
				"role": "worker",
			},
		},
	}

	node2 := &corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "worker-node-2",
			Labels: map[string]string{
				"role": "worker",
			},
		},
	}

	// 设置 reconciler
	r := setupReconciler(t, scan, rule1, rule2, node1, node2)

	// 执行 createNodeScanJobs
	scanID := "test-scan-id"
	rules := []complianceapi.Rule{*rule1, *rule2}
	err := r.createNodeScanJobs(context.Background(), scan, rules, scanID)

	// 验证结果
	assert.NoError(t, err)

	// 由于 fake client 的限制，我们可能无法直接验证 Job 创建
	// 这里我们只验证 createNodeScanJobs 是否成功执行
}

// ========== 新增的增强功能测试 ==========

func TestGetNodeRole(t *testing.T) {
	r := &ScanReconciler{}

	testCases := []struct {
		name         string
		nodeLabels   map[string]string
		expectedRole string
	}{
		{
			name: "Control-plane node (new label)",
			nodeLabels: map[string]string{
				"node-role.kubernetes.io/control-plane": "",
			},
			expectedRole: "control-plane",
		},
		{
			name: "Master node (old label)",
			nodeLabels: map[string]string{
				"node-role.kubernetes.io/master": "",
			},
			expectedRole: "master",
		},
		{
			name: "Worker node",
			nodeLabels: map[string]string{
				"node-role.kubernetes.io/worker": "",
			},
			expectedRole: "worker",
		},
		{
			name: "Node with both control-plane and master labels",
			nodeLabels: map[string]string{
				"node-role.kubernetes.io/control-plane": "",
				"node-role.kubernetes.io/master":        "",
			},
			expectedRole: "control-plane", // control-plane优先
		},
		{
			name: "Node with no role labels",
			nodeLabels: map[string]string{
				"kubernetes.io/os": "linux",
			},
			expectedRole: "worker", // 默认为worker
		},
		{
			name:         "Node with empty labels",
			nodeLabels:   map[string]string{},
			expectedRole: "worker",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			node := corev1.Node{
				ObjectMeta: metav1.ObjectMeta{
					Name:   "test-node",
					Labels: tc.nodeLabels,
				},
			}

			role := r.getNodeRole(node)
			assert.Equal(t, tc.expectedRole, role)
		})
	}
}

func TestFilterNodesByScope(t *testing.T) {
	r := &ScanReconciler{}

	// 创建测试节点
	controlPlaneNode := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "control-plane-node",
			Labels: map[string]string{
				"node-role.kubernetes.io/control-plane": "",
			},
		},
	}

	masterNode := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "master-node",
			Labels: map[string]string{
				"node-role.kubernetes.io/master": "",
			},
		},
	}

	workerNode1 := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "worker-node-1",
			Labels: map[string]string{
				"node-role.kubernetes.io/worker": "",
			},
		},
	}

	workerNode2 := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "worker-node-2",
			Labels: map[string]string{
				"kubernetes.io/os": "linux", // 没有角色标签，默认为worker
			},
		},
	}

	allNodes := []corev1.Node{controlPlaneNode, masterNode, workerNode1, workerNode2}

	testCases := []struct {
		name          string
		nodeScope     string
		expectedNodes []string
	}{
		{
			name:          "All nodes scope",
			nodeScope:     "all",
			expectedNodes: []string{"control-plane-node", "master-node", "worker-node-1", "worker-node-2"},
		},
		{
			name:          "Empty scope (defaults to all)",
			nodeScope:     "",
			expectedNodes: []string{"control-plane-node", "master-node", "worker-node-1", "worker-node-2"},
		},
		{
			name:          "Control-plane scope",
			nodeScope:     "control-plane",
			expectedNodes: []string{"control-plane-node"},
		},
		{
			name:          "Master scope",
			nodeScope:     "master",
			expectedNodes: []string{"master-node", "control-plane-node"}, // master包含control-plane
		},
		{
			name:          "Worker scope",
			nodeScope:     "worker",
			expectedNodes: []string{"worker-node-1", "worker-node-2"},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			filteredNodes := r.filterNodesByScope(allNodes, tc.nodeScope)

			// 提取节点名称
			var nodeNames []string
			for _, node := range filteredNodes {
				nodeNames = append(nodeNames, node.Name)
			}

			assert.ElementsMatch(t, tc.expectedNodes, nodeNames)
		})
	}
}

func TestFilterNodesByRoles(t *testing.T) {
	r := &ScanReconciler{}

	// 创建测试节点
	controlPlaneNode := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "control-plane-node",
			Labels: map[string]string{
				"node-role.kubernetes.io/control-plane": "",
			},
		},
	}

	masterNode := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "master-node",
			Labels: map[string]string{
				"node-role.kubernetes.io/master": "",
			},
		},
	}

	workerNode := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "worker-node",
			Labels: map[string]string{
				"node-role.kubernetes.io/worker": "",
			},
		},
	}

	allNodes := []corev1.Node{controlPlaneNode, masterNode, workerNode}

	testCases := []struct {
		name          string
		targetRoles   []string
		expectedNodes []string
	}{
		{
			name:          "Filter by control-plane role",
			targetRoles:   []string{"control-plane"},
			expectedNodes: []string{"control-plane-node"},
		},
		{
			name:          "Filter by master role",
			targetRoles:   []string{"master"},
			expectedNodes: []string{"master-node"},
		},
		{
			name:          "Filter by worker role",
			targetRoles:   []string{"worker"},
			expectedNodes: []string{"worker-node"},
		},
		{
			name:          "Filter by multiple roles",
			targetRoles:   []string{"control-plane", "worker"},
			expectedNodes: []string{"control-plane-node", "worker-node"},
		},
		{
			name:          "Empty target roles",
			targetRoles:   []string{},
			expectedNodes: []string{}, // 空的targetRoles应该返回空列表
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			filteredNodes := r.filterNodesByRoles(allNodes, tc.targetRoles)

			// 提取节点名称
			var nodeNames []string
			for _, node := range filteredNodes {
				nodeNames = append(nodeNames, node.Name)
			}

			assert.ElementsMatch(t, tc.expectedNodes, nodeNames)
		})
	}
}

func TestGetNodeTolerationsForRole(t *testing.T) {
	r := &ScanReconciler{}

	testCases := []struct {
		name                   string
		nodeRole               string
		expectedTolerations    int
		shouldHaveControlPlane bool
		shouldHaveMaster       bool
	}{
		{
			name:                   "Control-plane node tolerations",
			nodeRole:               "control-plane",
			expectedTolerations:    3, // 2个角色toleration + 1个通用toleration
			shouldHaveControlPlane: true,
			shouldHaveMaster:       true,
		},
		{
			name:                   "Master node tolerations",
			nodeRole:               "master",
			expectedTolerations:    3, // 2个角色toleration + 1个通用toleration
			shouldHaveControlPlane: true,
			shouldHaveMaster:       true,
		},
		{
			name:                   "Worker node tolerations",
			nodeRole:               "worker",
			expectedTolerations:    1, // 只有1个通用toleration
			shouldHaveControlPlane: false,
			shouldHaveMaster:       false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tolerations := r.getNodeTolerationsForRole(tc.nodeRole)

			assert.Equal(t, tc.expectedTolerations, len(tolerations))

			if tc.shouldHaveControlPlane {
				found := false
				for _, toleration := range tolerations {
					if toleration.Key == "node-role.kubernetes.io/control-plane" {
						found = true
						assert.Equal(t, corev1.TolerationOpExists, toleration.Operator)
						assert.Equal(t, corev1.TaintEffectNoSchedule, toleration.Effect)
						break
					}
				}
				assert.True(t, found, "Should have control-plane toleration")
			}

			if tc.shouldHaveMaster {
				found := false
				for _, toleration := range tolerations {
					if toleration.Key == "node-role.kubernetes.io/master" {
						found = true
						assert.Equal(t, corev1.TolerationOpExists, toleration.Operator)
						assert.Equal(t, corev1.TaintEffectNoSchedule, toleration.Effect)
						break
					}
				}
				assert.True(t, found, "Should have master toleration")
			}
		})
	}
}

func TestGroupRulesByNodeScope(t *testing.T) {
	r := &ScanReconciler{}

	// 创建测试规则
	rules := []complianceapi.Rule{
		{
			ObjectMeta: metav1.ObjectMeta{Name: "control-plane-rule"},
			Spec: complianceapi.RuleSpec{
				CheckType: "node",
				NodeScope: "control-plane",
			},
		},
		{
			ObjectMeta: metav1.ObjectMeta{Name: "worker-rule"},
			Spec: complianceapi.RuleSpec{
				CheckType: "node",
				NodeScope: "worker",
			},
		},
		{
			ObjectMeta: metav1.ObjectMeta{Name: "all-nodes-rule"},
			Spec: complianceapi.RuleSpec{
				CheckType: "node",
				NodeScope: "all",
			},
		},
		{
			ObjectMeta: metav1.ObjectMeta{Name: "empty-scope-rule"},
			Spec: complianceapi.RuleSpec{
				CheckType: "node",
				NodeScope: "", // 空的NodeScope应该被归类为"all"
			},
		},
		{
			ObjectMeta: metav1.ObjectMeta{Name: "platform-rule"},
			Spec: complianceapi.RuleSpec{
				CheckType: "platform", // 非node类型的规则应该被忽略
				NodeScope: "control-plane",
			},
		},
	}

	groups := r.groupRulesByNodeScope(rules)

	// 验证分组结果
	assert.Equal(t, 3, len(groups), "Should have 3 groups")

	// 验证control-plane组
	controlPlaneRules, exists := groups["control-plane"]
	assert.True(t, exists, "Should have control-plane group")
	assert.Equal(t, 1, len(controlPlaneRules))
	assert.Equal(t, "control-plane-rule", controlPlaneRules[0].Name)

	// 验证worker组
	workerRules, exists := groups["worker"]
	assert.True(t, exists, "Should have worker group")
	assert.Equal(t, 1, len(workerRules))
	assert.Equal(t, "worker-rule", workerRules[0].Name)

	// 验证all组（包括空NodeScope的规则）
	allRules, exists := groups["all"]
	assert.True(t, exists, "Should have all group")
	assert.Equal(t, 2, len(allRules))
	ruleNames := []string{allRules[0].Name, allRules[1].Name}
	assert.ElementsMatch(t, []string{"all-nodes-rule", "empty-scope-rule"}, ruleNames)
}

func TestIsNodeScopeCompatible(t *testing.T) {
	r := &ScanReconciler{}

	testCases := []struct {
		name            string
		ruleNodeScope   string
		targetNodeRoles []string
		expectedResult  bool
	}{
		{
			name:            "All scope is always compatible",
			ruleNodeScope:   "all",
			targetNodeRoles: []string{"control-plane", "worker"},
			expectedResult:  true,
		},
		{
			name:            "All scope with empty target roles",
			ruleNodeScope:   "all",
			targetNodeRoles: []string{},
			expectedResult:  true,
		},
		{
			name:            "Control-plane scope matches control-plane role",
			ruleNodeScope:   "control-plane",
			targetNodeRoles: []string{"control-plane"},
			expectedResult:  true,
		},
		{
			name:            "Master scope matches master role",
			ruleNodeScope:   "master",
			targetNodeRoles: []string{"master"},
			expectedResult:  true,
		},
		{
			name:            "Master scope matches control-plane role (compatibility)",
			ruleNodeScope:   "master",
			targetNodeRoles: []string{"control-plane"},
			expectedResult:  true,
		},
		{
			name:            "Control-plane scope matches master role (compatibility)",
			ruleNodeScope:   "control-plane",
			targetNodeRoles: []string{"master"},
			expectedResult:  true,
		},
		{
			name:            "Worker scope matches worker role",
			ruleNodeScope:   "worker",
			targetNodeRoles: []string{"worker"},
			expectedResult:  true,
		},
		{
			name:            "Control-plane scope doesn't match worker role",
			ruleNodeScope:   "control-plane",
			targetNodeRoles: []string{"worker"},
			expectedResult:  false,
		},
		{
			name:            "Worker scope doesn't match control-plane role",
			ruleNodeScope:   "worker",
			targetNodeRoles: []string{"control-plane"},
			expectedResult:  false,
		},
		{
			name:            "Multiple target roles with match",
			ruleNodeScope:   "worker",
			targetNodeRoles: []string{"control-plane", "worker"},
			expectedResult:  true,
		},
		{
			name:            "Multiple target roles without match",
			ruleNodeScope:   "worker",
			targetNodeRoles: []string{"control-plane", "master"},
			expectedResult:  false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := r.isNodeScopeCompatible(tc.ruleNodeScope, tc.targetNodeRoles)
			assert.Equal(t, tc.expectedResult, result)
		})
	}
}

func TestFilterNodesForScan(t *testing.T) {
	r := &ScanReconciler{}

	// 创建测试节点
	controlPlaneNode := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "control-plane-node",
			Labels: map[string]string{
				"node-role.kubernetes.io/control-plane": "",
				"kubernetes.io/os":                      "linux",
			},
		},
	}

	workerNode1 := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "worker-node-1",
			Labels: map[string]string{
				"node-role.kubernetes.io/worker": "",
				"kubernetes.io/os":               "linux",
			},
		},
	}

	workerNode2 := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "worker-node-2",
			Labels: map[string]string{
				"node-role.kubernetes.io/worker": "",
				"kubernetes.io/os":               "linux",
				"environment":                    "test",
			},
		},
	}

	allNodes := []corev1.Node{controlPlaneNode, workerNode1, workerNode2}

	testCases := []struct {
		name          string
		scan          *complianceapi.Scan
		expectedNodes []string
	}{
		{
			name: "Manual strategy - all nodes",
			scan: &complianceapi.Scan{
				Spec: complianceapi.ScanSpec{
					NodeScopeStrategy: "manual",
				},
			},
			expectedNodes: []string{"control-plane-node", "worker-node-1", "worker-node-2"},
		},
		{
			name: "Empty strategy - all nodes (defaults to manual)",
			scan: &complianceapi.Scan{
				Spec: complianceapi.ScanSpec{
					NodeScopeStrategy: "",
				},
			},
			expectedNodes: []string{"control-plane-node", "worker-node-1", "worker-node-2"},
		},
		{
			name: "Auto strategy without target roles - all nodes",
			scan: &complianceapi.Scan{
				Spec: complianceapi.ScanSpec{
					NodeScopeStrategy: "auto",
					TargetNodeRoles:   []string{},
				},
			},
			expectedNodes: []string{"control-plane-node", "worker-node-1", "worker-node-2"},
		},
		{
			name: "Auto strategy with target roles - all nodes (auto strategy ignores targetNodeRoles)",
			scan: &complianceapi.Scan{
				Spec: complianceapi.ScanSpec{
					NodeScopeStrategy: "auto",
					TargetNodeRoles:   []string{"worker"}, // 应该被忽略
				},
			},
			expectedNodes: []string{"control-plane-node", "worker-node-1", "worker-node-2"}, // 返回所有节点
		},
		{
			name: "Strict strategy with target roles - filtered nodes",
			scan: &complianceapi.Scan{
				Spec: complianceapi.ScanSpec{
					NodeScopeStrategy: "strict",
					TargetNodeRoles:   []string{"worker"},
				},
			},
			expectedNodes: []string{"worker-node-1", "worker-node-2"}, // strict策略应该根据targetNodeRoles过滤
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			filteredNodes := r.filterNodesForScan(allNodes, tc.scan)

			// 提取节点名称
			var nodeNames []string
			for _, node := range filteredNodes {
				nodeNames = append(nodeNames, node.Name)
			}

			assert.ElementsMatch(t, tc.expectedNodes, nodeNames)
		})
	}
}

func TestCreateNodeScanJobsWithStrictStrategy(t *testing.T) {
	// 创建测试节点
	controlPlaneNode := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "control-plane-node",
			Labels: map[string]string{
				"node-role.kubernetes.io/control-plane": "",
			},
		},
	}

	workerNode := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "worker-node",
			Labels: map[string]string{
				"node-role.kubernetes.io/worker": "",
			},
		},
	}

	// 创建测试规则
	compatibleRule := complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{Name: "compatible-rule"},
		Spec: complianceapi.RuleSpec{
			CheckType: "node",
			NodeScope: "worker",
		},
	}

	incompatibleRule := complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{Name: "incompatible-rule"},
		Spec: complianceapi.RuleSpec{
			CheckType: "node",
			NodeScope: "control-plane", // 与scan的targetNodeRoles不兼容
		},
	}

	platformRule := complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{Name: "platform-rule"},
		Spec: complianceapi.RuleSpec{
			CheckType: "platform", // 非node类型，应该被忽略
			NodeScope: "control-plane",
		},
	}

	testCases := []struct {
		name        string
		scan        *complianceapi.Scan
		rules       []complianceapi.Rule
		expectError bool
		errorMsg    string
	}{
		{
			name: "Compatible rules should pass",
			scan: &complianceapi.Scan{
				ObjectMeta: metav1.ObjectMeta{Name: "test-scan", Namespace: "default"},
				Spec: complianceapi.ScanSpec{
					TargetNodeRoles: []string{"worker"},
				},
			},
			rules:       []complianceapi.Rule{compatibleRule, platformRule},
			expectError: false,
		},
		{
			name: "Incompatible rules should fail",
			scan: &complianceapi.Scan{
				ObjectMeta: metav1.ObjectMeta{Name: "test-scan", Namespace: "default"},
				Spec: complianceapi.ScanSpec{
					TargetNodeRoles: []string{"worker"},
				},
			},
			rules:       []complianceapi.Rule{incompatibleRule},
			expectError: true,
			errorMsg:    "not compatible with scan targetNodeRoles",
		},
		{
			name: "No target node roles should pass",
			scan: &complianceapi.Scan{
				ObjectMeta: metav1.ObjectMeta{Name: "test-scan", Namespace: "default"},
				Spec: complianceapi.ScanSpec{
					TargetNodeRoles: []string{},
				},
			},
			rules:       []complianceapi.Rule{incompatibleRule},
			expectError: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			r := setupReconciler(t, tc.scan, &controlPlaneNode, &workerNode)

			allNodes := []corev1.Node{controlPlaneNode, workerNode}
			err := r.createNodeScanJobsWithStrictStrategy(context.Background(), tc.scan, tc.rules, allNodes, "test-scan-id")

			if tc.expectError {
				assert.Error(t, err)
				if tc.errorMsg != "" {
					assert.Contains(t, err.Error(), tc.errorMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestAutoStrategyNodeSelectionFix 测试auto策略的节点选择修复
func TestAutoStrategyNodeSelectionFix(t *testing.T) {
	r := &ScanReconciler{}

	// 创建测试节点
	controlPlaneNode := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "control-plane-node",
			Labels: map[string]string{
				"node-role.kubernetes.io/control-plane": "",
				"kubernetes.io/os":                      "linux",
			},
		},
	}

	workerNode1 := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "worker-node-1",
			Labels: map[string]string{
				"node-role.kubernetes.io/worker": "",
				"kubernetes.io/os":               "linux",
			},
		},
	}

	workerNode2 := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "worker-node-2",
			Labels: map[string]string{
				"node-role.kubernetes.io/worker": "",
				"kubernetes.io/os":               "linux",
			},
		},
	}

	allNodes := []corev1.Node{controlPlaneNode, workerNode1, workerNode2}

	// 创建测试规则
	controlPlaneRule := complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{Name: "control-plane-rule"},
		Spec: complianceapi.RuleSpec{
			CheckType: "node",
			NodeScope: "control-plane",
		},
	}

	workerRule := complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{Name: "worker-rule"},
		Spec: complianceapi.RuleSpec{
			CheckType: "node",
			NodeScope: "worker",
		},
	}

	allScopeRule := complianceapi.Rule{
		ObjectMeta: metav1.ObjectMeta{Name: "all-scope-rule"},
		Spec: complianceapi.RuleSpec{
			CheckType: "node",
			NodeScope: "all",
		},
	}

	testCases := []struct {
		name                    string
		rules                   []complianceapi.Rule
		expectedJobsByNodeScope map[string][]string // nodeScope -> expected node names
		description             string
	}{
		{
			name:  "Control-plane rule should only target control-plane nodes",
			rules: []complianceapi.Rule{controlPlaneRule},
			expectedJobsByNodeScope: map[string][]string{
				"control-plane": {"control-plane-node"},
			},
			description: "规则nodeScope=control-plane应该只在control-plane节点创建Job",
		},
		{
			name:  "Worker rule should only target worker nodes",
			rules: []complianceapi.Rule{workerRule},
			expectedJobsByNodeScope: map[string][]string{
				"worker": {"worker-node-1", "worker-node-2"},
			},
			description: "规则nodeScope=worker应该只在worker节点创建Job",
		},
		{
			name:  "All scope rule should target all nodes",
			rules: []complianceapi.Rule{allScopeRule},
			expectedJobsByNodeScope: map[string][]string{
				"all": {"control-plane-node", "worker-node-1", "worker-node-2"},
			},
			description: "规则nodeScope=all应该在所有节点创建Job",
		},
		{
			name:  "Mixed rules should target appropriate nodes",
			rules: []complianceapi.Rule{controlPlaneRule, workerRule},
			expectedJobsByNodeScope: map[string][]string{
				"control-plane": {"control-plane-node"},
				"worker":        {"worker-node-1", "worker-node-2"},
			},
			description: "混合规则应该根据各自的nodeScope在对应节点创建Job",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 测试groupRulesByNodeScope
			ruleGroups := r.groupRulesByNodeScope(tc.rules)

			// 验证规则分组
			for expectedScope, expectedNodes := range tc.expectedJobsByNodeScope {
				rules, exists := ruleGroups[expectedScope]
				assert.True(t, exists, "Expected nodeScope %s not found in rule groups", expectedScope)
				assert.NotEmpty(t, rules, "No rules found for nodeScope %s", expectedScope)

				// 测试filterNodesByScope
				for _, rule := range rules {
					targetNodes := r.filterNodesByScope(allNodes, rule.Spec.NodeScope)
					var nodeNames []string
					for _, node := range targetNodes {
						nodeNames = append(nodeNames, node.Name)
					}

					assert.ElementsMatch(t, expectedNodes, nodeNames,
						"Rule %s with nodeScope %s should target nodes %v, but got %v",
						rule.Name, rule.Spec.NodeScope, expectedNodes, nodeNames)
				}
			}

			t.Logf("✅ %s", tc.description)
		})
	}
}

// TestAutoStrategyIgnoresTargetNodeRoles 测试auto策略完全忽略targetNodeRoles
func TestAutoStrategyIgnoresTargetNodeRoles(t *testing.T) {
	r := &ScanReconciler{}

	// 创建多种类型的节点
	controlPlaneNode := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "control-plane-node",
			Labels: map[string]string{
				"node-role.kubernetes.io/control-plane": "",
			},
		},
	}

	workerNode1 := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "worker-node-1",
			Labels: map[string]string{
				"node-role.kubernetes.io/worker": "",
			},
		},
	}

	workerNode2 := corev1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: "worker-node-2",
			Labels: map[string]string{
				"node-role.kubernetes.io/worker": "",
			},
		},
	}

	allNodes := []corev1.Node{controlPlaneNode, workerNode1, workerNode2}

	// 测试各种targetNodeRoles配置对auto策略的影响
	testCases := []struct {
		name            string
		targetNodeRoles []string
		description     string
	}{
		{
			name:            "Empty targetNodeRoles",
			targetNodeRoles: []string{},
			description:     "空的targetNodeRoles，auto策略应该返回所有节点",
		},
		{
			name:            "Only control-plane targetNodeRoles",
			targetNodeRoles: []string{"control-plane"},
			description:     "只有control-plane的targetNodeRoles，auto策略应该忽略并返回所有节点",
		},
		{
			name:            "Only worker targetNodeRoles",
			targetNodeRoles: []string{"worker"},
			description:     "只有worker的targetNodeRoles，auto策略应该忽略并返回所有节点",
		},
		{
			name:            "Mixed targetNodeRoles",
			targetNodeRoles: []string{"control-plane", "worker"},
			description:     "混合的targetNodeRoles，auto策略应该忽略并返回所有节点",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			scan := &complianceapi.Scan{
				Spec: complianceapi.ScanSpec{
					NodeScopeStrategy: "auto",
					TargetNodeRoles:   tc.targetNodeRoles,
				},
			}

			filteredNodes := r.filterNodesForScan(allNodes, scan)

			// Auto策略应该总是返回所有节点，不管targetNodeRoles如何设置
			assert.Len(t, filteredNodes, 3, "Auto strategy should always return all nodes regardless of targetNodeRoles")

			var nodeNames []string
			for _, node := range filteredNodes {
				nodeNames = append(nodeNames, node.Name)
			}

			expectedNodes := []string{"control-plane-node", "worker-node-1", "worker-node-2"}
			assert.ElementsMatch(t, expectedNodes, nodeNames,
				"Auto strategy should return all nodes: %v, but got: %v", expectedNodes, nodeNames)

			t.Logf("✅ %s", tc.description)
		})
	}
}
