#!/bin/bash

echo "=== OpenSCAP 崩溃问题调试 ==="
echo

echo "问题描述："
echo "- 主机上运行 ssg-slmicro5-ds.xml 扫描成功"
echo "- 容器中运行相同命令出现崩溃："
echo "  oscap: ./src/XCCDF_POLICY/xccdf_policy.c:638: xccdf_policy_is_item_selected: Assertion \`false' failed."
echo "  Aborted (core dumped)"
echo

echo "1. 测试容器环境"
echo "=============="

# 创建测试目录
mkdir -p /tmp/debug-crash
cd /tmp/debug-crash

# 复制内容文件
cp /Users/<USER>/work/go/src/gitlab-ce.alauda.cn/ait/compliance-operator/images/content/ds/ssg-slmicro5-ds.xml .

echo "使用 openscap-scanner-fixed:latest 测试："

docker run --rm -v $(pwd):/workspace openscap-scanner-fixed:latest bash -c "
cd /workspace

echo '=== 环境信息 ==='
echo 'OpenSCAP 版本:'
oscap --version | head -5

echo
echo 'Linux 发行版:'
cat /etc/os-release | head -5

echo
echo '=== 测试不同的扫描方式 ==='

echo '1. 测试 oscap info (应该成功):'
oscap info ssg-slmicro5-ds.xml | head -10

echo
echo '2. 测试 oscap xccdf eval 不指定 profile (可能崩溃):'
timeout 30s oscap xccdf eval ssg-slmicro5-ds.xml 2>&1 | head -10
echo 'Exit code:' \$?

echo
echo '3. 测试 oscap xccdf eval 指定 profile (可能崩溃):'
timeout 30s oscap xccdf eval --profile xccdf_org.ssgproject.content_profile_stig ssg-slmicro5-ds.xml 2>&1 | head -10
echo 'Exit code:' \$?

echo
echo '4. 测试 oscap xccdf eval 使用 --fetch-remote-resources:'
timeout 30s oscap xccdf eval --fetch-remote-resources --profile xccdf_org.ssgproject.content_profile_stig ssg-slmicro5-ds.xml 2>&1 | head -10
echo 'Exit code:' \$?

echo
echo '5. 测试 oscap xccdf eval 使用最小参数:'
timeout 30s oscap xccdf eval --profile xccdf_org.ssgproject.content_profile_stig --results /tmp/results.xml ssg-slmicro5-ds.xml 2>&1 | head -10
echo 'Exit code:' \$?
"

echo
echo "2. 分析可能的原因"
echo "================"
echo "可能的崩溃原因："
echo "1. Profile 选择逻辑错误"
echo "2. 远程资源依赖问题"
echo "3. 内存不足或栈溢出"
echo "4. OpenSCAP 版本兼容性问题"
echo "5. 数据流文件损坏或不完整"

echo
echo "3. 建议的解决方案"
echo "================"
echo "1. 使用 --fetch-remote-resources 预下载依赖"
echo "2. 检查 Profile 是否存在和有效"
echo "3. 增加内存和栈大小限制"
echo "4. 使用更稳定的 OpenSCAP 版本"
echo "5. 验证数据流文件完整性"
