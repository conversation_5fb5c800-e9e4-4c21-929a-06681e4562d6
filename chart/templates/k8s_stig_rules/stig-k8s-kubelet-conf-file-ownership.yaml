apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-conf-file-ownership
  namespace: compliance-system
spec:
  id: "V-242453"
  title: "Kubernetes kubelet KubeConfig must be root-owned"
  description: "The Kubernetes kubelet agent registers nodes with the API Server and performs health checks to containers within pods. If this file can be modified, the information system would be unaware of pod or container degradation."
  checkText: "Review the Kubernetes Kubeadm kubelet conf file by using the command:\n\nstat -c %U:%G /etc/kubernetes/kubelet.conf| grep -v root:root\n\nIf the command returns any non root:root file permissions, this is a finding."
  fixText: "Change the ownership of the kubelet config to \"root: root\" by executing the command:\n\nchown root:root /etc/kubernetes/kubelet.conf"
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting kubelet config file ownership check (V-242453)"
    
    # Define kubelet config file path (preserving original logic)
    kubelet_conf="/host/etc/kubernetes/kubelet.conf"
    
    echo "Checking kubelet configuration file: $kubelet_conf"
    
    # Check if kubelet config file exists (preserving original logic)
    if [[ ! -f "$kubelet_conf" ]]; then
        echo "FAIL: Kubelet config missing"
        echo "Kubelet configuration file not found at: $kubelet_conf"
        echo "Expected kubelet.conf file must exist for ownership verification"
        exit 1
    fi
    
    echo "Found kubelet configuration file"
    echo "Checking file ownership..."
    
    # Check file ownership (preserving original logic)
    ownership=$(stat -c '%U:%G' "$kubelet_conf" 2>/dev/null)
    
    if [[ -z "$ownership" ]]; then
        echo "FAIL: Unable to read file ownership"
        echo "Cannot determine ownership for: $kubelet_conf"
        exit 1
    fi
    
    echo "Current file ownership: $ownership"
    echo "Required ownership: root:root"
    
    # Validate ownership (preserving original logic)
    if [[ "$ownership" != "root:root" ]]; then
        echo "FAIL: Invalid ownership"
        echo "File: $kubelet_conf"
        echo "Current ownership: $ownership"
        echo "Required ownership: root:root"
        echo "Kubelet config file must be owned by root:root for security"
        exit 1
    else
        echo "PASS: Kubelet.conf has proper ownership"
        echo "File: $kubelet_conf"
        echo "Ownership: $ownership (compliant)"
        exit 0
    fi 