apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-controller-manager-tls-min-version
  namespace: compliance-system
spec:
  id: "V-242376"
  title: "The Kubernetes Controller Manager must use TLS 1.2, at a minimum, to protect the confidentiality of sensitive data during electronic dissemination."
  description: "The Kubernetes Controller Manager will prohibit the use of SSL and unauthorized versions of TLS protocols to properly secure communication.\n\nThe use of unsupported protocol exposes vulnerabilities to the Kubernetes by rogue traffic interceptions, man-in-the-middle attacks, and impersonation of users or services from the container platform runtime, registry, and key store. To enable the minimum version of TLS to be used by the Kubernetes Controller Manager, the setting \"tls-min-version\" must be set."
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i tls-min-version * \n\nIf the setting \"tls-min-version\" is not configured in the Kubernetes Controller Manager manifest file or it is set to \"VersionTLS10\" or \"VersionTLS11\", this is a finding."
  fixText: "Edit the Kubernetes Controller Manager manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of \"--tls-min-version\" to \"VersionTLS12\" or higher."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting Controller Manager TLS minimum version check (V-242376)"
    
    # Set manifest file path (preserving original logic)
    manifest_path="/host/etc/kubernetes/manifests/kube-controller-manager.yaml"
    
    echo "Checking Controller Manager manifest file: $manifest_path"
    
    # Check if manifest exists (preserving original logic)
    if [[ ! -f "$manifest_path" ]]; then
        echo "FAIL: Controller Manager manifest missing at $manifest_path"
        echo "Expected to find kube-controller-manager.yaml manifest file"
        exit 1
    fi
    
    echo "Found Controller Manager manifest file"
    echo "Searching for --tls-min-version configuration..."
    
    # Check TLS minimum version (preserving original logic)
    tls_min_version=$(grep -oP -- "--tls-min-version=\K[^ ]+" "$manifest_path" 2>/dev/null)
    
    if [[ -z "$tls_min_version" ]]; then
        echo "FAIL: TLS minimum version not configured"
        echo "The --tls-min-version parameter must be explicitly set"
        echo "This parameter is required for secure TLS communication"
        exit 1
    fi
    
    echo "Found TLS minimum version configuration: $tls_min_version"
    echo "Validating TLS version against security requirements..."
    
    # Validate TLS version (preserving original logic)
    if [[ "$tls_min_version" == "VersionTLS10" ]] || [[ "$tls_min_version" == "VersionTLS11" ]]; then
        echo "FAIL: Unsupported TLS version: $tls_min_version"
        echo "TLS 1.0 and 1.1 are deprecated and insecure"
        echo "Must use VersionTLS12 or VersionTLS13"
        exit 1
    elif [[ "$tls_min_version" == "VersionTLS12" ]] || [[ "$tls_min_version" == "VersionTLS13" ]]; then
        echo "PASS: TLS version correctly configured: $tls_min_version"
        echo "Using secure TLS version (1.2 or higher)"
        exit 0
    else
        echo "FAIL: Unknown TLS version: $tls_min_version"
        echo "Unrecognized TLS version specification"
        echo "Valid values: VersionTLS12, VersionTLS13"
        exit 1
    fi 