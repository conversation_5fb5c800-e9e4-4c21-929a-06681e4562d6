apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-ca-file-permissions
  namespace: compliance-system
spec:
  id: "V-242449"
  title: "Kubernetes Kubelet CA permissions must ≤644"
  description: "The Kubernetes kubelet agent registers nodes with the API Server and performs health checks to containers within pods. The client CA file contains the CA certificate bundle to verify client certificates. If this file can be modified, the integrity and availability of the Kubernetes cluster could be compromised."
  checkText: "Check the permissions of the Kubelet CA file by running the command:\n\nstat -c %a /path/to/client-ca-file\n\nIf the file has permissions more permissive than \"644\", this is a finding."
  fixText: "Change the permissions of the Kubelet CA file to 644 by executing the command:\n\nchmod 644 /path/to/client-ca-file"
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting kubelet CA file permissions check (V-242449)"
    
    # Check kubelet process arguments (preserving original logic)
    echo "Checking kubelet process arguments..."
    kubelet_cmd=$(chroot /host ps -ef | grep kubelet | grep -v grep)
    
    if [[ -z "$kubelet_cmd" ]]; then
        echo "FAIL: Kubelet process not found"
        echo "Unable to locate running kubelet process"
        exit 1
    fi
    
    echo "Found kubelet process"
    
    # Extract config file path from process arguments (preserving original logic)
    config_file=$(echo "$kubelet_cmd" | grep -oP -- '--config=\K[^ ]+' || echo "/host/var/lib/kubelet/config.yaml")
    
    # Ensure config file has /host prefix if extracted from process
    if [[ -n "$config_file" && ! "$config_file" =~ ^/host ]]; then
        config_file="/host$config_file"
    fi
    
    echo "Using kubelet config file: $config_file"
    
    # Check if config file exists
    if [[ ! -f "$config_file" ]]; then
        echo "FAIL: Kubelet config file not found"
        echo "Config file does not exist at: $config_file"
        exit 1
    fi
    
    echo "Found kubelet configuration file"
    echo "Checking authentication.x509.clientCAFile setting..."
    
    # Check config file for CA file path (preserving original logic)
    ca_file=$(yq '.authentication.x509.clientCAFile' "$config_file" 2>/dev/null)
    
    if [[ -z "$ca_file" || "$ca_file" == "null" ]]; then
        echo "FAIL: Client CA file not configured"
        echo "authentication.x509.clientCAFile setting is missing or null"
        echo "Kubelet client CA file must be configured for proper authentication"
        exit 1
    fi
    
    echo "Found CA file configuration: $ca_file"
    echo "Checking CA file existence and permissions..."
    
    # Ensure CA file has /host prefix for container access
    actual_ca_file=""
    if [[ -f "$ca_file" ]]; then
        actual_ca_file="$ca_file"
    elif [[ -f "/host$ca_file" ]]; then
        actual_ca_file="/host$ca_file"
    else
        echo "FAIL: Client CA file not found"
        echo "Checked paths:"
        echo "  - $ca_file"
        echo "  - /host$ca_file"
        exit 1
    fi
    
    echo "Using CA file: $actual_ca_file"
    
    # Check CA file permissions (preserving original logic)
    perm=$(stat -c '%a' "$actual_ca_file" 2>/dev/null)
    
    if [[ -z "$perm" ]]; then
        echo "FAIL: Unable to read CA file permissions"
        echo "Cannot determine permissions for: $actual_ca_file"
        exit 1
    fi
    
    echo "Current CA file permissions: $perm"
    echo "Required permissions: ≤644"
    
    # Validate permissions (preserving original logic)
    if [[ $perm -gt 644 ]]; then
        echo "FAIL: Excessive CA permissions"
        echo "CA file: $actual_ca_file"
        echo "Current permissions: $perm"
        echo "Maximum allowed permissions: 644"
        echo "CA file permissions must be 644 or more restrictive"
        exit 1
    else
        echo "PASS: Kubelet CA has compliant permissions"
        echo "CA file: $actual_ca_file"
        echo "Permissions: $perm (compliant)"
        exit 0
    fi