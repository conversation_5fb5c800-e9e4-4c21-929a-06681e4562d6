apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-client-ca-file
  namespace: compliance-system
spec:
  id: "V-242419"
  title: "Kubernetes API Server must have the SSL Certificate Authority set."
  description: "Kubernetes control plane and external communication are managed by API Server. The main implementation of the API Server is to manage hardware resources for pods and containers using horizontal or vertical scaling. Anyone who can access the API Server can effectively control the Kubernetes architecture. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols such as TLS. TLS provides the Kubernetes API Server with a means to authenticate sessions and encrypt traffic. \n\nTo enable encrypted communication for API Server, the parameter client-ca-file must be set. This parameter gives the location of the SSL Certificate Authority file used to secure API Server communication."
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i client-ca-file *\n\nIf the setting feature client-ca-file is not set in the Kubernetes API server manifest file or contains no value, this is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--client-ca-file\" to path containing Approved Organizational Certificate."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Checking API Server client CA file configuration..."
    manifests_dir="/host/etc/kubernetes/manifests"
    echo "Searching for API Server manifest files in: $manifests_dir"
    
    # Check if manifests directory exists
    if [[ ! -d "$manifests_dir" ]]; then
        echo "ERROR: Kubernetes manifests directory not found: $manifests_dir"
        echo "FAIL: Manifests directory missing"
        exit 1
    fi
    
    echo "Found manifests directory"
    
    # Find API Server manifest files
    api_files=$(grep -lis 'kube-apiserver' "$manifests_dir"/* 2>/dev/null)
    
    if [[ -z "$api_files" ]]; then
        echo "ERROR: No API Server manifest files found in $manifests_dir"
        echo "FAIL: No API Server manifest found"
        exit 1
    fi
    
    echo "Found API Server manifest files: $api_files"
    
    # Check each API Server manifest file for client-ca-file
    all_configured=true
    violation_files=""
    
    for file in $api_files; do
        echo "Checking file: $(basename "$file")"
        
        # Check if client-ca-file parameter exists
        if ! grep -iq -- "--client-ca-file" "$file" 2>/dev/null; then
            echo "VIOLATION: client-ca-file not configured in $(basename "$file")"
            all_configured=false
            if [[ -n "$violation_files" ]]; then
                violation_files="${violation_files}, $(basename "$file")"
            else
                violation_files="$(basename "$file")"
            fi
        else
            echo "Found client-ca-file parameter in $(basename "$file")"
            
            # Extract and validate the CA file value
            ca_file=$(grep -i -- "--client-ca-file" "$file" 2>/dev/null | awk -F= '{print $2}' | tr -d ' ')
            
            if [[ -z "$ca_file" ]]; then
                echo "VIOLATION: Empty client-ca-file value in $(basename "$file")"
                all_configured=false
                if [[ -n "$violation_files" ]]; then
                    violation_files="${violation_files}, $(basename "$file")"
                else
                    violation_files="$(basename "$file")"
                fi
            else
                echo "OK: client-ca-file has valid value in $(basename "$file")"
                echo "  CA file path: $ca_file"
            fi
        fi
    done
    
    # Final result
    if [[ "$all_configured" == false ]]; then
        echo "FAIL: Invalid client-ca-file configuration in: $violation_files"
        exit 1
    fi
    
    echo "OK: All API Server manifests have client-ca-file properly configured"
    echo "PASS: client-ca-file properly configured"
    exit 0 