apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-audit-policy-configured
  namespace: compliance-system
spec:
  id: "V-242461"
  title: "Kubernetes API Server audit logs must be enabled."
  description: "Kubernetes API Server validates and configures pods and services for the API object. The REST operation provides frontend functionality to the cluster share state. Enabling audit logs provides a way to monitor and identify security risk events or misuse of information. Audit logs are necessary to provide evidence in the case the Kubernetes API Server is compromised requiring a Cyber Security Investigation."
  checkText: "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\ngrep -i audit-policy-file * \n\nIf the setting \"audit-policy-file\" is not set or is found in the Kubernetes API manifest file without valid content, this is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument \"--audit-policy-file\" to \"log file directory\"."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Checking API Server audit policy configuration..."
    manifest_file="/host/etc/kubernetes/manifests/kube-apiserver.yaml"
    echo "Checking API Server manifest: $manifest_file"

    # Check if manifest file exists
    if [[ ! -f "$manifest_file" ]]; then
        echo "ERROR: API Server manifest file not found"
        echo "FAIL: API Server manifest missing"
        exit 1
    fi

    echo "Found API Server manifest file"

    # Check audit policy file configuration
    echo "Searching for audit-policy-file parameter..."ku
            policy_file=$(grep -i "\-\-audit-policy-file" "$manifest_file" | awk -F'=' '{print $2}' 2>/dev/null | tr -d ' ')
    
    if [[ -z "$policy_file" ]]; then
        echo "VIOLATION: audit-policy-file parameter not configured"
        echo "FAIL: audit-policy-file not configured"
        exit 1
    fi

    echo "Found audit-policy-file configuration: $policy_file"

    # Check if audit policy file exists (add /host prefix for host filesystem)
    host_policy_file="/host$policy_file"
    echo "Checking if audit policy file exists: $host_policy_file"
    
    if [[ ! -f "$host_policy_file" ]]; then
        echo "ERROR: Audit policy file not found: $policy_file"
        echo "FAIL: audit policy file missing: $policy_file"
        exit 1
    fi

    echo "Found audit policy file"

    # Validate audit policy file format
    echo "Validating audit policy file format..."
    
    if ! grep -q "apiVersion: audit.k8s.io/v1" "$host_policy_file" 2>/dev/null; then
        echo "ERROR: Invalid audit policy file format - missing apiVersion: audit.k8s.io/v1"
        echo "FAIL: invalid audit policy format"
        exit 1
    fi

    echo "OK: Audit policy file has valid format"
    echo "OK: audit-policy-file properly configured and valid"
    echo "PASS: audit-policy-file properly configured"
    exit 0 