apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-config-file-permissions
  namespace: compliance-system
spec:
  id: "V-242456"
  title: "The Kubernetes kubelet configuration file must have file permissions set to 644 or more restrictive."
  description: "The kubelet configuration file contains settings for the Kubernetes worker nodes. If this file can be modified by non-privileged users, the integrity and availability of the Kubernetes cluster could be compromised."
  checkText: "Check the permissions of the kubelet configuration file by running the command:\n\nstat -c %a /var/lib/kubelet/config.yaml\n\nIf the file has permissions more permissive than \"644\", this is a finding."
  fixText: "Change the permissions of the kubelet configuration file to 644 or more restrictive by executing the command:\n\nchmod 644 /var/lib/kubelet/config.yaml"
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting kubelet configuration file permissions check (V-242456)"
    
    # Define kubelet config file path (preserving original logic)
    kubelet_config="/host/var/lib/kubelet/config.yaml"
    
    echo "Checking kubelet configuration file: $kubelet_config"
    
    # Check if kubelet config file exists (preserving original logic)
    if [[ ! -f "$kubelet_config" ]]; then
        echo "FAIL: Kubelet config missing"
        echo "Kubelet configuration file not found at: $kubelet_config"
        echo "Expected config.yaml file must exist for permissions verification"
        exit 1
    fi
    
    echo "Found kubelet configuration file"
    echo "Checking file permissions..."
    
    # Check file permissions (preserving original logic)
    perm=$(stat -c '%a' "$kubelet_config" 2>/dev/null)
    
    if [[ -z "$perm" ]]; then
        echo "FAIL: Unable to read file permissions"
        echo "Cannot determine permissions for: $kubelet_config"
        exit 1
    fi
    
    echo "Current file permissions: $perm"
    echo "Maximum allowed permissions: 644"
    
    # Validate permissions (preserving original logic)
    if [[ $perm -gt 644 ]]; then
        echo "FAIL: Excessive kubelet permissions"
        echo "File: $kubelet_config"
        echo "Current permissions: $perm"
        echo "Maximum allowed permissions: 644"
        echo "Kubelet config file permissions must be 644 or more restrictive"
        exit 1
    else
        echo "PASS: Kubelet config has compliant permissions"
        echo "File: $kubelet_config"
        echo "Permissions: $perm (compliant)"
        exit 0
    fi 