apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-client-ca-file
  namespace: compliance-system
spec:
  id: "V-242420"
  title: "Kubernetes Kubelet must have the SSL Certificate Authority set."
  description: "Kubernetes container and pod configuration are maintained by Kubelet. Kubelet agents register nodes with the API Server, mount volume storage, and perform health checks for containers and pods. Anyone who gains access to Kubelet agents can effectively control applications within the pods and containers. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols such as TLS. TLS provides the Kubernetes API Server with a means to authenticate sessions and encrypt traffic.\n\nTo enable encrypted communication for Kubelet, the clientCAFile must be set. This parameter gives the location of the SSL Certificate Authority file used to secure Kubelet communication."
  checkText: "On the Control Plane, run the command:\nps -ef | grep kubelet\n\nIf the \"--client-ca-file\" option exists, this is a finding.\n\nNote the path to the config file (identified by --config).\n\nRun the command:\ngrep -i clientCAFile <path_to_config_file>\n\nIf the setting \"clientCAFile\" is not set or contains no value, this is a finding."
  fixText: "On the Control Plane, run the command:\nps -ef | grep kubelet\n\nRemove the \"--client-ca-file\" option if present.\n\nNote the path to the config file (identified by --config).\n\nEdit the Kubernetes Kubelet config file: \nSet the value of \"clientCAFile\" to a path containing an Approved Organizational Certificate. \n\nRestart the kubelet service using the following command:\nsystemctl daemon-reload && systemctl restart kubelet"
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting kubelet SSL Certificate Authority check (V-242420)"
    
    # Check kubelet process arguments (preserving original logic)
    echo "Checking kubelet process arguments..."
    kubelet_cmd=$(chroot /host ps -ef | grep kubelet | grep -v grep)
    
    if [[ -z "$kubelet_cmd" ]]; then
        echo "FAIL: Kubelet process not found"
        echo "Unable to locate running kubelet process"
        exit 1
    fi
    
    echo "Found kubelet process"
    
    # Extract config path from process args (preserving original logic)
    config_file="/host/var/lib/kubelet/config.yaml"
    config_arg=$(echo "$kubelet_cmd" | grep -o -- "--config=[^ ]*")
    
    if [[ -n "$config_arg" ]]; then
        extracted_config=$(echo "$config_arg" | cut -d= -f2)
        # Ensure config file has /host prefix if extracted from process
        if [[ -n "$extracted_config" && ! "$extracted_config" =~ ^/host ]]; then
            config_file="/host$extracted_config"
        else
            config_file="$extracted_config"
        fi
        echo "Found config file from --config argument: $config_file"
    else
        config_arg=$(echo "$kubelet_cmd" | grep -o -- "--config [^ ]*" | awk '{print $2}')
        if [[ -n "$config_arg" ]]; then
            # Ensure config file has /host prefix if extracted from process
            if [[ ! "$config_arg" =~ ^/host ]]; then
                config_file="/host$config_arg"
            else
                config_file="$config_arg"
            fi
            echo "Found config file from --config argument: $config_file"
        else
            echo "Using default config file: $config_file"
        fi
    fi
    
    # Check for --client-ca-file in process (preserving original logic)
    echo "Checking for --client-ca-file in process arguments..."
    if echo "$kubelet_cmd" | grep "\-\-client-ca-file" 2>/dev/null >/dev/null; then
        echo "PASS: --client-ca-file found in process arguments"
        echo "Client CA properly configured via process arguments"
        exit 0
    fi
    
    echo "No --client-ca-file found in process arguments"
    echo "Checking configuration file for clientCAFile setting..."
    
    # Check config file (preserving original logic)
    if [[ ! -f "$config_file" ]]; then
        echo "FAIL: Config file missing"
        echo "Config file does not exist at: $config_file"
        echo "Either process argument --client-ca-file or config file clientCAFile must be set"
        exit 1
    fi
    
    echo "Found configuration file: $config_file"
    echo "Checking authentication.x509.clientCAFile setting..."
    
    # Check clientCAFile in config (preserving original logic)
    ca_file=$(yq '.authentication.x509.clientCAFile' "$config_file" 2>/dev/null)
    
    if [[ -z "$ca_file" || "$ca_file" == "null" ]]; then
        echo "FAIL: clientCAFile not set in configuration"
        echo "Config file: $config_file"
        echo "authentication.x509.clientCAFile setting is missing or empty"
        echo "Client CA file must be configured for SSL certificate authority"
        exit 1
    else
        echo "PASS: Client CA properly configured"
        echo "Config file: $config_file"
        echo "clientCAFile setting: $ca_file"
        echo "SSL Certificate Authority is properly configured"
        exit 0
    fi