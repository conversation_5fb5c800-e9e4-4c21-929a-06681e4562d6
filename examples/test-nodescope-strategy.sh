#!/bin/bash

# NodeScopeStrategy 测试脚本
# 用于快速部署和测试 NodeScopeStrategy 功能

set -e

NAMESPACE="compliance-system"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 kubectl 是否可用
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 命令未找到，请先安装 kubectl"
        exit 1
    fi
    
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到 Kubernetes 集群"
        exit 1
    fi
    
    log_success "Kubernetes 集群连接正常"
}

# 检查命名空间
check_namespace() {
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_warning "命名空间 $NAMESPACE 不存在，正在创建..."
        kubectl create namespace "$NAMESPACE"
        log_success "命名空间 $NAMESPACE 创建成功"
    else
        log_info "命名空间 $NAMESPACE 已存在"
    fi
}

# 部署测试资源
deploy_test_resources() {
    log_info "部署测试资源..."

    # 部署基础资源
    kubectl apply -f "$SCRIPT_DIR/test-content-basic.yaml"
    kubectl apply -f "$SCRIPT_DIR/test-profile-basic.yaml"
    kubectl apply -f "$SCRIPT_DIR/test-rules-basic.yaml"

    log_success "测试资源部署完成"

    # 等待资源就绪
    log_info "等待资源就绪..."
    sleep 5

    # 验证资源
    log_info "验证部署的资源:"
    kubectl get profilebundle,profile,rule -n "$NAMESPACE" -l compliance-operator.alauda.io/rule-type=test -o wide
}

# 运行特定策略测试
run_strategy_test() {
    local strategy=$1
    local scan_name="test-scan-${strategy}-strategy"
    
    log_info "测试 $strategy 策略..."
    
    # 创建 scan
    kubectl apply -f "$SCRIPT_DIR/test-scans-nodescope-strategy.yaml"
    
    # 等待 scan 处理
    log_info "等待 scan 处理 (30秒)..."
    sleep 30
    
    # 检查 scan 状态
    log_info "检查 $scan_name 状态:"
    kubectl get scan "$scan_name" -n "$NAMESPACE" -o yaml | grep -A10 "status:" || log_warning "Scan 状态信息不可用"
    
    # 检查创建的 jobs
    log_info "检查创建的 Jobs:"
    kubectl get jobs -n "$NAMESPACE" -l "compliance-operator.alauda.io/scan=$scan_name" -o wide || log_warning "未找到相关 Jobs"
    
    # 检查 job pods
    log_info "检查 Job Pods:"
    kubectl get pods -n "$NAMESPACE" -l "compliance-operator.alauda.io/scan=$scan_name" -o wide || log_warning "未找到相关 Pods"
}

# 显示节点信息
show_cluster_info() {
    log_info "集群节点信息:"
    kubectl get nodes -o wide
    
    log_info "节点角色标签:"
    kubectl get nodes --show-labels | grep -E "(control-plane|master|worker)" || log_warning "未找到角色标签"
}

# 清理测试资源
cleanup() {
    log_info "清理测试资源..."
    
    # 删除 scans
    kubectl delete scan -n "$NAMESPACE" -l compliance-operator.alauda.io/scan-type=test --ignore-not-found=true
    
    # 删除 jobs
    kubectl delete jobs -n "$NAMESPACE" -l compliance-operator.alauda.io/scan-type=test --ignore-not-found=true
    
    # 删除基础资源
    kubectl delete -f "$SCRIPT_DIR/test-rules-basic.yaml" --ignore-not-found=true
    kubectl delete -f "$SCRIPT_DIR/test-profile-basic.yaml" --ignore-not-found=true
    kubectl delete -f "$SCRIPT_DIR/test-content-basic.yaml" --ignore-not-found=true
    
    log_success "清理完成"
}

# 主函数
main() {
    case "${1:-}" in
        "deploy")
            check_kubectl
            check_namespace
            deploy_test_resources
            ;;
        "test")
            strategy="${2:-auto}"
            run_strategy_test "$strategy"
            ;;
        "info")
            show_cluster_info
            ;;
        "cleanup")
            cleanup
            ;;
        "full")
            check_kubectl
            check_namespace
            show_cluster_info
            deploy_test_resources
            
            log_info "开始完整测试流程..."
            
            # 测试所有策略
            for strategy in auto manual strict; do
                echo
                log_info "========== 测试 $strategy 策略 =========="
                run_strategy_test "$strategy"
                echo
            done
            
            log_success "完整测试流程完成"
            ;;
        *)
            echo "用法: $0 {deploy|test|info|cleanup|full}"
            echo ""
            echo "命令说明:"
            echo "  deploy  - 部署测试资源"
            echo "  test    - 运行特定策略测试 (auto|manual|strict)"
            echo "  info    - 显示集群信息"
            echo "  cleanup - 清理测试资源"
            echo "  full    - 运行完整测试流程"
            echo ""
            echo "示例:"
            echo "  $0 deploy"
            echo "  $0 test auto"
            echo "  $0 full"
            echo "  $0 cleanup"
            exit 1
            ;;
    esac
}

main "$@"
