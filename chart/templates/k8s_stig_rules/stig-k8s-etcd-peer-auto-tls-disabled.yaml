apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-etcd-peer-auto-tls-disabled
  namespace: compliance-system
spec:
  id: "V-242380"
  title: "The Kubernetes etcd must use TLS to protect the confidentiality of sensitive data during electronic dissemination."
  description: "The etcd database contains all the configuration settings for the Kubernetes cluster. Without TLS encryption, sensitive information would be accessible to unauthorized users. Using TLS ensures that all communications between etcd peers are encrypted, protecting the confidentiality and integrity of the data."
  checkText: "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i peer-auto-tls etcd.yaml\n\nIf the setting \"--peer-auto-tls\" is set to \"true\" or is not configured in the etcd manifest file, this is a finding."
  fixText: "Edit the etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--peer-auto-tls\" to \"false\" or remove the setting."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting etcd peer auto-TLS disabled check (V-242380)"
    
    # Define etcd manifest file path (preserving original logic)
    etcd_file="/host/etc/kubernetes/manifests/etcd.yaml"

    echo "Checking etcd manifest file: $etcd_file"

    # Check if etcd manifest file exists (preserving original logic)
    if [[ -f "$etcd_file" ]]; then
        echo "Found etcd manifest file"
        echo "Checking --peer-auto-tls configuration..."
        
        # Check peer-auto-tls configuration (preserving original logic)
        peer_tls=$(grep -i -- "--peer-auto-tls" "$etcd_file" 2>/dev/null)
        
        if [[ "$peer_tls" =~ "true" ]] || [[ -z "$peer_tls" ]]; then
            if [[ "$peer_tls" =~ "true" ]]; then
                echo "FAIL: peer-auto-tls is explicitly set to true"
                echo "Found configuration: $peer_tls"
            else
                echo "FAIL: peer-auto-tls is not configured (defaults to true)"
                echo "peer-auto-tls parameter must be explicitly set to false"
            fi
            echo "etcd peer auto-TLS must be disabled for security"
            exit 1
        else
            echo "Found peer-auto-tls configuration: $peer_tls"
            echo "PASS: etcd peer auto-TLS properly disabled"
            exit 0
        fi
    else
        echo "FAIL: etcd manifest not found"
        echo "Expected to find etcd.yaml manifest file"
        exit 1
    fi 