apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-pps
  namespace: compliance-system
spec:
  id: "V-242410"
  title: "The Kubernetes API Server must enforce ports, protocols, and services (PPS) that adhere to the Ports, Protocols, and Services Management Category Assurance List (PPSM CAL)."
  description: "Kubernetes API Server PPSs must be controlled and conform to the PPSM CAL. Those PPS that fall outside the PPSM CAL must be blocked. Instructions on the PPSM can be found in DoD Instruction 8551.01 Policy."
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep kube-apiserver.manifest -I -secure-port *\ngrep kube-apiserver.manifest -I -etcd-servers *\n-edit manifest file:\nVIM <Manifest Name>\nReview livenessProbe:\nHttpGet:\nPort:\nReview ports:\n- containerPort:\nhostPort:\n- containerPort:\nhostPort:\n\nRun command: \nkubectl describe services --all-namespaces \nSearch labels for any apiserver namespaces.\nPort:\n\nAny manifest and namespace PPS or services configuration not in compliance with PPSM CAL is a finding.\n\nReview the information systems documentation and interview the team, gain an understanding of the API Server architecture, and determine applicable PPS. If there are any PPS in the system documentation not in compliance with the CAL PPSM, this is a finding. Any PPS not set in the system documentation is a finding.\n\nReview findings against the most recent PPSM CAL:\nhttps://cyber.mil/ppsm/cal/\n\nVerify API Server network boundary with the PPS associated with the CAL Assurance Categories. Any PPS not in compliance with the CAL Assurance Category requirements is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of --secure-port to a port that is in compliance with the PPSM CAL.\n\nAny other ports used by the API server must be in compliance with the PPSM CAL.\n\nReview the information systems documentation and interview the team, gain an understanding of the API Server architecture, and determine applicable PPS. Document all PPS in the system documentation. All PPS must be in compliance with the CAL PPSM."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Checking API Server PPSM CAL compliance..."
    echo "Rule ID: V-242410 - Ports, Protocols, and Services Management"
    
    echo "INFO: This rule requires manual verification of PPSM CAL compliance"
    echo "Manual inspection required for the following components:"
    echo "- API Server secure port configuration"
    echo "- etcd server connections"
    echo "- Container ports and host ports"
    echo "- Service configurations across all namespaces"
    echo "- Network boundary compliance"
    
    echo "Reference: PPSM CAL documentation at https://cyber.mil/ppsm/cal/"
    echo "Additional details: https://stigviewer.com/stigs/kubernetes/2024-08-22/finding/V-242410"
    
    echo "WARN: Manual verification required for PPSM CAL compliance"
    echo "System administrator must verify all ports, protocols, and services against PPSM CAL requirements"
    
    # Return warning status for manual review
    exit 2