apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-manifests-owned-by-root
  namespace: compliance-system
spec:
  id: "V-242444"
  title: "The Kubernetes component manifests must be owned by root."
  description: "The Kubernetes manifests are those files that contain the arguments and settings for the Control Plane services. These services are etcd, the api server, controller, proxy, and scheduler. If these files can be changed, the scheduler will be implementing the changes immediately. Many of the security settings within the document are implemented through these manifests."
  checkText: "Review the ownership of the Kubernetes manifests files by using the command:\n\nstat -c %U:%G /etc/kubernetes/manifests/* | grep -v root:root\n\nIf the command returns any non root:root file permissions, this is a finding."
  fixText: "Change the ownership of the manifest files to root: root by executing the command:\n\nchown root:root /etc/kubernetes/manifests/*"
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting V-242444: Kubernetes component manifests ownership check"
    
    manifest_dir="/host/etc/kubernetes/manifests"
    fail_count=0
    failed_files=""
    
    echo "Checking manifest files in directory: $manifest_dir"
    
    if [[ ! -d "$manifest_dir" ]]; then
        echo "FAIL: Manifest directory not found: $manifest_dir"
        exit 1
    fi
    
    echo "Scanning manifest files for ownership compliance..."
    
    while read -r file; do
        if [[ -n "$file" ]]; then
            owner=$(stat -c "%U:%G" "$file" 2>/dev/null)
            echo "Checking file: $file (owner: $owner)"
            
            if [[ "$owner" != "root:root" ]]; then
                echo "FAIL: Incorrect ownership $owner for $file (should be root:root)"
                failed_files+="${failed_files:+, }$(basename "$file"):$owner"
                fail_count=$((fail_count + 1))
            else
                echo "PASS: Correct ownership $owner for $file"
            fi
        fi
    done < <(find "$manifest_dir" -type f 2>/dev/null)
    
    if [[ $fail_count -gt 0 ]]; then
        echo "FAIL: Found $fail_count files with incorrect ownership: $failed_files"
        exit 1
    else
        echo "PASS: All manifest files have correct ownership (root:root)"
        exit 0
    fi 