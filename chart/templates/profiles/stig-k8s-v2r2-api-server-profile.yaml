---
# STIG Kubernetes V2R2 API Server Profile
# Contains API server security checks
apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Profile
metadata:
  name: stig-k8s-v2r2-api-server
  namespace: {{ .Values.global.complianceNamespace }}
spec:
  id: "STIG-K8S-V2R2-API-SERVER"
  title: "STIG Kubernetes V2R2 API Server Security Profile"
  description: |
    This profile contains API server security checks based on the 
    Security Technical Implementation Guide (STIG) for Kubernetes V2R2.
    Focuses specifically on kube-apiserver configuration and security.
  version: "1.0"
  rules:
    - name: stig-k8s-api-server-alpha-apis-disabled
    - name: stig-k8s-api-server-anonymous-auth-disabled
    - name: stig-k8s-api-server-audit-log-enabled
    - name: stig-k8s-api-server-audit-log-maxage
    - name: stig-k8s-api-server-audit-log-maxbackup
    - name: stig-k8s-api-server-audit-log-maxsize
    - name: stig-k8s-api-server-audit-log-path
    - name: stig-k8s-api-server-audit-policy-configured
    - name: stig-k8s-api-server-audit-policy
    - name: stig-k8s-api-server-authorization-mode
    - name: stig-k8s-api-server-basic-auth-disabled
    - name: stig-k8s-api-server-cipher-suites
    - name: stig-k8s-api-server-client-ca-file
    - name: stig-k8s-api-server-etcd-cafile
    - name: stig-k8s-api-server-etcd-certfile
    - name: stig-k8s-api-server-etcd-keyfile
    - name: stig-k8s-api-server-insecure-bind-address-not-set
    - name: stig-k8s-api-server-insecure-port-disabled
    - name: stig-k8s-api-server-pps
    - name: stig-k8s-api-server-request-timeout
    - name: stig-k8s-api-server-secure-port-set
    - name: stig-k8s-api-server-tls-cert-file
    - name: stig-k8s-api-server-tls-min-version
    - name: stig-k8s-api-server-token-auth-disabled
    - name: stig-k8s-api-server-validating-webhook 