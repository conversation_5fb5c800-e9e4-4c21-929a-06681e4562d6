package scan

import (
	"context"
	"fmt"
	"math/rand"
	"os"
	"regexp"
	"strings"
	"time"

	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/utils/pointer"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"

	complianceapi "github.com/alauda/compliance-operator/pkg/apis/compliance/v1alpha1"
)

// Batch job creation configuration
const (
	// Maximum number of jobs to create in one batch
	DEFAULT_MAX_BATCH_SIZE = 20
	// Time to wait between batches (in seconds)
	DEFAULT_BATCH_INTERVAL = 30
	// Maximum number of concurrent running jobs per scan
	DEFAULT_MAX_CONCURRENT_JOBS = 50
	// Environment variable to override batch size
	ENV_MAX_BATCH_SIZE = "COMPLIANCE_MAX_BATCH_SIZE"
	// Environment variable to override batch interval
	ENV_BATCH_INTERVAL = "COMPLIANCE_BATCH_INTERVAL"
	// Environment variable to override max concurrent jobs
	ENV_MAX_CONCURRENT_JOBS = "COMPLIANCE_MAX_CONCURRENT_JOBS"
)

// BatchJobCreator manages batch job creation with concurrency control
type BatchJobCreator struct {
	maxBatchSize      int
	batchInterval     time.Duration
	maxConcurrentJobs int
}

// NewBatchJobCreator creates a new batch job creator with configuration from environment variables
func NewBatchJobCreator() *BatchJobCreator {
	maxBatchSize := DEFAULT_MAX_BATCH_SIZE
	batchInterval := DEFAULT_BATCH_INTERVAL
	maxConcurrentJobs := DEFAULT_MAX_CONCURRENT_JOBS

	// Override from environment variables if set
	if envBatchSize := os.Getenv(ENV_MAX_BATCH_SIZE); envBatchSize != "" {
		if size, err := parseInt(envBatchSize); err == nil && size > 0 {
			maxBatchSize = size
		}
	}

	if envBatchInterval := os.Getenv(ENV_BATCH_INTERVAL); envBatchInterval != "" {
		if interval, err := parseInt(envBatchInterval); err == nil && interval > 0 {
			batchInterval = interval
		}
	}

	if envMaxConcurrent := os.Getenv(ENV_MAX_CONCURRENT_JOBS); envMaxConcurrent != "" {
		if maxConcurrent, err := parseInt(envMaxConcurrent); err == nil && maxConcurrent > 0 {
			maxConcurrentJobs = maxConcurrent
		}
	}

	return &BatchJobCreator{
		maxBatchSize:      maxBatchSize,
		batchInterval:     time.Duration(batchInterval) * time.Second,
		maxConcurrentJobs: maxConcurrentJobs,
	}
}

// Helper function to parse integer from string
func parseInt(s string) (int, error) {
	var result int
	_, err := fmt.Sscanf(s, "%d", &result)
	return result, err
}

// createJobsBatch creates jobs in batches with concurrency control
func (bjc *BatchJobCreator) createJobsBatch(ctx context.Context, r *ScanReconciler, scan *complianceapi.Scan, jobs []*batchv1.Job, scanID string) error {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace, "scanID", scanID)

	totalJobs := len(jobs)
	if totalJobs == 0 {
		return nil
	}

	log.Info("Starting batch job creation",
		"totalJobs", totalJobs,
		"maxBatchSize", bjc.maxBatchSize,
		"batchInterval", bjc.batchInterval,
		"maxConcurrentJobs", bjc.maxConcurrentJobs)

	createdJobs := 0
	batchNumber := 1

	for i := 0; i < totalJobs; i += bjc.maxBatchSize {
		// Check if we've reached the maximum concurrent jobs limit
		if err := bjc.waitForRunningJobsLimit(ctx, r, scan, scanID); err != nil {
			return fmt.Errorf("failed to wait for job limit: %v", err)
		}

		// Calculate batch end index
		batchEnd := i + bjc.maxBatchSize
		if batchEnd > totalJobs {
			batchEnd = totalJobs
		}

		batchJobs := jobs[i:batchEnd]
		log.Info("Creating job batch", "batchNumber", batchNumber, "batchSize", len(batchJobs), "progress", fmt.Sprintf("%d/%d", i, totalJobs))

		// Create jobs in current batch
		for _, job := range batchJobs {
			if err := r.Create(ctx, job); err != nil {
				return fmt.Errorf("failed to create job %s: %v", job.Name, err)
			}
			createdJobs++
			log.V(1).Info("Created job", "jobName", job.Name, "created", createdJobs, "total", totalJobs)
		}

		log.Info("Batch created successfully", "batchNumber", batchNumber, "jobsInBatch", len(batchJobs), "totalCreated", createdJobs)
		batchNumber++

		// Wait between batches (except for the last batch)
		if batchEnd < totalJobs {
			log.Info("Waiting between batches", "interval", bjc.batchInterval)
			time.Sleep(bjc.batchInterval)
		}
	}

	log.Info("All jobs created successfully", "totalCreated", createdJobs)
	return nil
}

// waitForRunningJobsLimit waits until the number of running jobs is below the limit
func (bjc *BatchJobCreator) waitForRunningJobsLimit(ctx context.Context, r *ScanReconciler, scan *complianceapi.Scan, scanID string) error {
	log := r.Log.WithValues("scan", scan.Name, "scanID", scanID)

	maxWaitTime := 10 * time.Minute   // Maximum wait time
	checkInterval := 15 * time.Second // Check every 15 seconds
	deadline := time.Now().Add(maxWaitTime)

	for time.Now().Before(deadline) {
		runningJobs, err := bjc.countRunningJobs(ctx, r, scan, scanID)
		if err != nil {
			return err
		}

		if runningJobs < bjc.maxConcurrentJobs {
			if runningJobs > 0 {
				log.V(1).Info("Current running jobs within limit", "runningJobs", runningJobs, "limit", bjc.maxConcurrentJobs)
			}
			return nil
		}

		log.Info("Too many running jobs, waiting", "runningJobs", runningJobs, "limit", bjc.maxConcurrentJobs, "waitTime", checkInterval)
		time.Sleep(checkInterval)
	}

	return fmt.Errorf("timeout waiting for running jobs to decrease below limit %d", bjc.maxConcurrentJobs)
}

// countRunningJobs counts the number of currently running jobs for a scan
func (bjc *BatchJobCreator) countRunningJobs(ctx context.Context, r *ScanReconciler, scan *complianceapi.Scan, scanID string) (int, error) {
	var jobs batchv1.JobList
	if err := r.List(ctx, &jobs, client.InNamespace(scan.Namespace), client.MatchingLabels{
		"compliance-operator.alauda.io/scan":    scan.Name,
		"compliance-operator.alauda.io/scan-id": scanID,
	}); err != nil {
		return 0, err
	}

	runningJobs := 0
	for _, job := range jobs.Items {
		// Count jobs that are not yet completed (neither succeeded nor failed)
		if job.Status.Succeeded == 0 && job.Status.Failed == 0 {
			runningJobs++
		}
	}

	return runningJobs, nil
}

// generateJobName generates a complete job name with random suffix
func generateJobName(checkType, ruleName, nodeName string) string {
	// Kubernetes resource names must be <= 63 characters
	// We need to leave space for our own random suffix (6 chars: -xxxxx)
	maxLength := 57 // 63 - 6 for suffix

	// Clean rule name: remove invalid characters
	cleanRuleName := cleanKubernetesName(ruleName)

	var baseName string
	switch checkType {
	case "platform":
		baseName = cleanRuleName
	case "node":
		if nodeName != "" {
			// Clean node name: remove invalid characters
			cleanNodeName := cleanKubernetesName(nodeName)
			baseName = fmt.Sprintf("%s-%s", cleanRuleName, cleanNodeName)
		} else {
			baseName = cleanRuleName
		}
	default:
		baseName = cleanRuleName
	}

	// Truncate if too long
	if len(baseName) > maxLength {
		baseName = baseName[:maxLength]
	}

	// Ensure it ends with alphanumeric character (Kubernetes requirement)
	baseName = strings.TrimRight(baseName, "-")

	// Generate random suffix
	rand.Seed(time.Now().UnixNano())
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"
	suffix := make([]byte, 5)
	for i := range suffix {
		suffix[i] = charset[rand.Intn(len(charset))]
	}

	return fmt.Sprintf("%s-%s", baseName, string(suffix))
}

// cleanKubernetesName cleans a string to be valid for Kubernetes resource names
// Kubernetes names must be lowercase alphanumeric characters or '-', and must start and end with alphanumeric
func cleanKubernetesName(name string) string {
	// Convert to lowercase
	cleaned := strings.ToLower(name)

	// Replace invalid characters with dash
	cleaned = regexp.MustCompile(`[^a-z0-9-]`).ReplaceAllString(cleaned, "-")

	// Remove consecutive dashes
	cleaned = regexp.MustCompile(`-+`).ReplaceAllString(cleaned, "-")

	// Ensure it starts with alphanumeric
	cleaned = regexp.MustCompile(`^[^a-z0-9]*`).ReplaceAllString(cleaned, "")

	// Ensure it ends with alphanumeric
	cleaned = regexp.MustCompile(`[^a-z0-9]*$`).ReplaceAllString(cleaned, "")

	// If empty after cleaning, provide a default
	if cleaned == "" {
		cleaned = "default"
	}

	return cleaned
}

// getImageRegistry returns the image registry from environment variable or default
func (r *ScanReconciler) getImageRegistry() string {
	if registry := os.Getenv("IMAGE_REGISTRY"); registry != "" {
		return registry
	}
	// Use proxy registry for K8s cluster access
	return "registry.alauda.cn:60070/test/compliance"
}

// getUnifiedScannerImage returns the unified scanner image path
func (r *ScanReconciler) getUnifiedScannerImage() string {
	if image := os.Getenv("UNIFIED_SCANNER_IMAGE"); image != "" {
		return image
	}
	// Fallback to constructing from registry + default tag
	return r.getImageRegistry() + "/unified-scanner:no-auto-path"
}

// getScannerImage returns the unified scanner image (backward compatibility)
func (r *ScanReconciler) getScannerImage() string {
	// Try SCANNER_IMAGE first for backward compatibility
	if image := os.Getenv("SCANNER_IMAGE"); image != "" {
		return image
	}
	return r.getUnifiedScannerImage()
}

// getNodeScannerImage returns the unified scanner image (backward compatibility)
func (r *ScanReconciler) getNodeScannerImage() string {
	return r.getUnifiedScannerImage()
}

// getOpenSCAPScannerImage returns the OpenSCAP scanner image path
func (r *ScanReconciler) getOpenSCAPScannerImage() string {
	if image := os.Getenv("OPENSCAP_SCANNER_IMAGE"); image != "" {
		return image
	}
	// Fallback to constructing from registry + default tag
	return r.getImageRegistry() + "/openscap-scanner:latest"
}

// getContentImage returns the OS content image path
func (r *ScanReconciler) getContentImage() string {
	if image := os.Getenv("CONTENT_IMAGE"); image != "" {
		return image
	}
	// Fallback to constructing from registry + default tag
	return r.getImageRegistry() + "/os-content:latest"
}

// getReportServiceImage returns the report service image path
func (r *ScanReconciler) getReportServiceImage() string {
	if image := os.Getenv("REPORT_SERVICE_IMAGE"); image != "" {
		return image
	}
	// Fallback to constructing from registry + default tag
	return r.getImageRegistry() + "/openscap-report-service:latest"
}

// initializeScan initializes a new scan
func (r *ScanReconciler) initializeScan(ctx context.Context, scan *complianceapi.Scan) (ctrl.Result, error) {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace)
	log.Info("Initializing scan")

	// 获取当前的 scanID
	scanID := ""
	if scan.Annotations != nil {
		scanID = scan.Annotations["compliance-operator.alauda.io/current-scan-id"]
	}

	if scanID == "" {
		// 如果没有 scanID，生成一个新的
		scanID = generateScanID(scan)
		log.Info("Generated new scanID for scan", "scanID", scanID)

		// 将 scanID 存储在注解中
		if scan.Annotations == nil {
			scan.Annotations = make(map[string]string)
		}
		scan.Annotations["compliance-operator.alauda.io/current-scan-id"] = scanID

		// 更新 Scan 对象
		if err := r.Update(ctx, scan); err != nil {
			log.Error(err, "Failed to update Scan with scanID")
			return ctrl.Result{}, err
		}

		// 重新入队等待更新后的对象
		return ctrl.Result{Requeue: true}, nil
	}

	log.Info("Using scanID for scan initialization", "scanID", scanID)

	// Get the profile
	var profile complianceapi.Profile
	if err := r.Get(ctx, client.ObjectKey{
		Name:      scan.Spec.Profile,
		Namespace: scan.Namespace,
	}, &profile); err != nil {
		scan.Status.Phase = "Error"
		scan.Status.Message = fmt.Sprintf("Failed to get profile: %v", err)
		if updateErr := r.Status().Update(ctx, scan); updateErr != nil {
			log.Error(updateErr, "Failed to update Scan status")
		}
		return ctrl.Result{}, err
	}

	// Check if jobs already exist (avoid duplicate creation)
	var existingJobs batchv1.JobList
	if err := r.List(ctx, &existingJobs, client.InNamespace(scan.Namespace), client.MatchingLabels{
		"compliance-operator.alauda.io/scan":    scan.Name,
		"compliance-operator.alauda.io/scan-id": scanID,
	}); err != nil {
		log.Error(err, "Failed to list existing jobs")
		return ctrl.Result{}, fmt.Errorf("failed to check existing jobs: %v", err)
	}

	if len(existingJobs.Items) > 0 {
		log.Info("Jobs already exist for scan, skipping creation", "scan", scan.Name, "scanID", scanID, "existingJobs", len(existingJobs.Items))
		// Update status to RUNNING since jobs already exist
		// 使用带重试机制的辅助方法更新状态
		if _, err := r.updateScanStatusWithStartTime(ctx, scan, "Running", "", "Scan jobs already created and running"); err != nil {
			log.Error(err, "Failed to update Scan status")
			return ctrl.Result{}, err
		}
		return ctrl.Result{RequeueAfter: 10 * time.Second}, nil
	}

	// Start the scan (create jobs)
	if err := r.startScan(ctx, scan, &profile, scanID); err != nil {
		log.Error(err, "Failed to start scan")
		// 使用带重试机制的辅助方法更新错误状态
		if _, updateErr := r.updateScanStatus(ctx, scan, "Error", "Error", fmt.Sprintf("Failed to start scan: %v", err)); updateErr != nil {
			log.Error(updateErr, "Failed to update Scan status")
		}
		return ctrl.Result{}, err
	}

	// Update status to RUNNING only after jobs are successfully created
	log.Info("Scan jobs created successfully, updating status to Running", "scan", scan.Name, "scanID", scanID)
	// 使用带重试机制的辅助方法更新状态为运行中
	if _, err := r.updateScanStatusWithStartTime(ctx, scan, "Running", "", "Scan jobs created and running"); err != nil {
		log.Error(err, "Failed to update Scan status")
		return ctrl.Result{}, err
	}

	return ctrl.Result{RequeueAfter: 10 * time.Second}, nil
}

// startScan starts the scan by creating appropriate jobs
func (r *ScanReconciler) startScan(ctx context.Context, scan *complianceapi.Scan, profile *complianceapi.Profile, scanID string) error {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace, "scanID", scanID)
	log.Info("Starting scan")

	// Check if this is an OpenSCAP datastream profile
	if profile.Spec.DataStream != nil {
		log.Info("Detected OpenSCAP datastream profile, using OpenSCAP scanner", "contentFile", profile.Spec.DataStream.ContentFile)
		return r.createOpenSCAPScanJobs(ctx, scan, profile, scanID)
	}

	// Original logic for rule-based profiles
	// Separate rules by type
	var platformRules, nodeRules []complianceapi.Rule

	for _, ruleRef := range profile.Spec.Rules {
		var rule complianceapi.Rule
		if err := r.Get(ctx, client.ObjectKey{
			Name:      ruleRef.Name,
			Namespace: scan.Namespace,
		}, &rule); err != nil {
			log.Error(err, "Failed to get rule", "rule", ruleRef.Name)
			continue
		}

		switch rule.Spec.CheckType {
		case "platform":
			platformRules = append(platformRules, rule)
		case "node":
			nodeRules = append(nodeRules, rule)
		default:
			log.Info("Unknown check type, defaulting to platform", "rule", rule.Name, "checkType", rule.Spec.CheckType)
			platformRules = append(platformRules, rule)
		}
	}

	log.Info("Scan rules categorized", "platformRules", len(platformRules), "nodeRules", len(nodeRules))

	// 创建平台扫描任务
	if len(platformRules) > 0 && (scan.Spec.ScanType == "platform" || scan.Spec.ScanType == "all") {
		if err := r.createPlatformScanJobs(ctx, scan, platformRules, scanID); err != nil {
			return fmt.Errorf("failed to create platform scan jobs: %v", err)
		}
	}

	// 创建节点扫描任务
	if len(nodeRules) > 0 && (scan.Spec.ScanType == "node" || scan.Spec.ScanType == "all") {
		// Pre-create rule scripts ConfigMaps for aggregated jobs
		if err := r.preCreateRuleScriptsConfigMaps(ctx, scan, nodeRules, scanID); err != nil {
			return fmt.Errorf("failed to pre-create rule scripts ConfigMaps: %v", err)
		}

		if err := r.createNodeScanJobs(ctx, scan, nodeRules, scanID); err != nil {
			return fmt.Errorf("failed to create node scan jobs: %v", err)
		}
	}

	return nil
}

// createPlatformScanJobs creates jobs for platform-level compliance checks
func (r *ScanReconciler) createPlatformScanJobs(ctx context.Context, scan *complianceapi.Scan, rules []complianceapi.Rule, scanID string) error {
	r.Log.Info("Creating platform scan jobs", "scan", scan.Name, "totalRules", len(rules))

	// Prepare jobs for batch creation
	var jobs []*batchv1.Job
	for _, rule := range rules {
		if rule.Spec.CheckType != "platform" {
			continue
		}

		// Generate complete job name with random suffix
		jobName := generateJobName("platform", rule.Name, "")

		job := &batchv1.Job{
			ObjectMeta: metav1.ObjectMeta{
				Name:      jobName,
				Namespace: scan.Namespace,
				Labels: map[string]string{
					"compliance-operator.alauda.io/scan":      scan.Name,
					"compliance-operator.alauda.io/scan-type": "platform",
					"compliance-operator.alauda.io/rule":      rule.Name,
					"compliance-operator.alauda.io/scan-id":   scanID,
					"compliance-operator.alauda.io/temporary": "true",
				},
			},
			Spec: batchv1.JobSpec{
				TTLSecondsAfterFinished: pointer.Int32Ptr(3600), // Auto cleanup after 5 minutes
				Template: corev1.PodTemplateSpec{
					ObjectMeta: metav1.ObjectMeta{
						Labels: map[string]string{
							"compliance-operator.alauda.io/scan":      scan.Name,
							"compliance-operator.alauda.io/scan-type": "platform",
							"compliance-operator.alauda.io/rule":      rule.Name,
							"compliance-operator.alauda.io/scan-id":   scanID,
							"compliance-operator.alauda.io/temporary": "true",
						},
					},
					Spec: corev1.PodSpec{
						RestartPolicy:      corev1.RestartPolicyNever,
						ServiceAccountName: "compliance-scanner",
						HostNetwork:        true,
						HostPID:            true,
						HostIPC:            true,
						SecurityContext: &corev1.PodSecurityContext{
							RunAsUser:  pointer.Int64Ptr(0),
							RunAsGroup: pointer.Int64Ptr(0),
						},
						Tolerations: []corev1.Toleration{
							{
								Operator: corev1.TolerationOpExists,
							},
						},
						Containers: []corev1.Container{
							{
								Name:            "scanner",
								Image:           r.getScannerImage(),
								ImagePullPolicy: corev1.PullAlways,
								Command:         []string{"/usr/local/bin/unified-scanner.sh"},
								Args:            []string{rule.Spec.CheckScript},
								SecurityContext: &corev1.SecurityContext{
									Privileged:   pointer.BoolPtr(true),
									RunAsUser:    pointer.Int64Ptr(0),
									RunAsGroup:   pointer.Int64Ptr(0),
									RunAsNonRoot: pointer.BoolPtr(false),
								},
								Env: []corev1.EnvVar{
									{Name: "RULE_ID", Value: rule.Name},
									{Name: "CHECK_TYPE", Value: "platform"},
									{Name: "SCAN_NAME", Value: scan.Name},
									{Name: "NAMESPACE", Value: scan.Namespace},
									{Name: "JOB_NAME", Value: jobName},
									{Name: "SCAN_ID", Value: scanID},
								},
								VolumeMounts: []corev1.VolumeMount{
									{
										Name:      "results",
										MountPath: "/tmp/results",
									},
									{
										Name:      "host-root",
										MountPath: "/host",
										ReadOnly:  true,
									},
								},
							},
						},
						Volumes: []corev1.Volume{
							{
								Name: "results",
								VolumeSource: corev1.VolumeSource{
									EmptyDir: &corev1.EmptyDirVolumeSource{},
								},
							},
							{
								Name: "host-root",
								VolumeSource: corev1.VolumeSource{
									HostPath: &corev1.HostPathVolumeSource{
										Path: "/",
									},
								},
							},
						},
					},
				},
			},
		}

		// Set owner reference
		if err := controllerutil.SetControllerReference(scan, job, r.Scheme); err != nil {
			return err
		}

		jobs = append(jobs, job)
	}

	if len(jobs) == 0 {
		r.Log.Info("No platform jobs to create")
		return nil
	}

	// Create jobs in batches
	batchCreator := NewBatchJobCreator()
	if err := batchCreator.createJobsBatch(ctx, r, scan, jobs, scanID); err != nil {
		return fmt.Errorf("failed to create platform jobs in batches: %v", err)
	}

	r.Log.Info("Platform scan jobs creation completed", "scan", scan.Name, "jobsCreated", len(jobs))
	return nil
}

// createNodeScanJobs creates jobs for node-level compliance checks with enhanced node selection
func (r *ScanReconciler) createNodeScanJobs(ctx context.Context, scan *complianceapi.Scan, rules []complianceapi.Rule, scanID string) error {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace, "scanID", scanID)

	// Get all nodes that match the basic selector
	var allNodes corev1.NodeList
	if err := r.List(ctx, &allNodes, client.MatchingLabels(scan.Spec.NodeSelector)); err != nil {
		return err
	}

	log.Info("Creating node scan jobs", "scan", scan.Name, "totalRules", len(rules), "totalNodes", len(allNodes.Items))

	// Handle different node scope strategies
	switch scan.Spec.NodeScopeStrategy {
	case "auto":
		// For auto strategy, pass all nodes directly - let the auto strategy function
		// decide which nodes to use based on each rule's nodeScope
		log.Info("Using auto strategy - nodes will be selected based on rule nodeScope", "totalNodes", len(allNodes.Items))
		return r.createNodeScanJobsWithAutoStrategy(ctx, scan, rules, allNodes.Items, scanID)
	case "strict":
		// Apply node filtering based on scan configuration for strict strategy
		targetNodes := r.filterNodesForScan(allNodes.Items, scan)
		log.Info("Filtered nodes for strict strategy", "totalNodes", len(allNodes.Items), "targetNodes", len(targetNodes), "targetNodeRoles", scan.Spec.TargetNodeRoles)
		return r.createNodeScanJobsWithStrictStrategy(ctx, scan, rules, targetNodes, scanID)
	default: // "manual" or empty
		// Apply node filtering based on scan configuration for manual strategy
		targetNodes := r.filterNodesForScan(allNodes.Items, scan)
		log.Info("Filtered nodes for manual strategy", "totalNodes", len(allNodes.Items), "targetNodes", len(targetNodes), "targetNodeRoles", scan.Spec.TargetNodeRoles)
		return r.createNodeScanJobsWithManualStrategy(ctx, scan, rules, targetNodes, scanID)
	}
}

// createNodeScanJobsWithManualStrategy creates aggregated jobs using manual node selection
func (r *ScanReconciler) createNodeScanJobsWithManualStrategy(ctx context.Context, scan *complianceapi.Scan, rules []complianceapi.Rule, allNodes []corev1.Node, scanID string) error {
	log := r.Log.WithValues("scan", scan.Name, "strategy", "manual-aggregated")

	// Filter node rules
	var nodeRules []complianceapi.Rule
	for _, rule := range rules {
		if rule.Spec.CheckType == "node" {
			nodeRules = append(nodeRules, rule)
		}
	}

	if len(nodeRules) == 0 {
		log.Info("No node rules to process")
		return nil
	}

	log.Info("Creating aggregated node scan jobs", "nodeRules", len(nodeRules), "nodes", len(allNodes))

	// Create one aggregated job per node
	var jobs []*batchv1.Job
	for _, node := range allNodes {
		// Filter rules applicable to this node
		applicableRules := r.filterRulesForNode(nodeRules, node, scan)
		if len(applicableRules) == 0 {
			log.Info("No applicable rules for node", "node", node.Name)
			continue
		}

		// Create aggregated job for this node
		job, err := r.createAggregatedNodeJob(scan, applicableRules, node, scanID)
		if err != nil {
			return fmt.Errorf("failed to create aggregated job for node %s: %v", node.Name, err)
		}

		jobs = append(jobs, job)
		log.Info("Created aggregated job for node", "node", node.Name, "applicableRules", len(applicableRules), "jobName", job.Name)
	}

	if len(jobs) == 0 {
		log.Info("No aggregated node jobs to create")
		return nil
	}

	// Create jobs in batches
	batchCreator := NewBatchJobCreator()
	if err := batchCreator.createJobsBatch(ctx, r, scan, jobs, scanID); err != nil {
		return fmt.Errorf("failed to create aggregated node jobs in batches: %v", err)
	}

	log.Info("Aggregated node scan jobs creation completed", "scan", scan.Name, "jobsCreated", len(jobs))
	return nil
}

// createOpenSCAPScanJobs creates OpenSCAP scanner jobs based on datastream profile
func (r *ScanReconciler) createOpenSCAPScanJobs(ctx context.Context, scan *complianceapi.Scan, profile *complianceapi.Profile, scanID string) error {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace, "scanID", scanID)
	log.Info("Creating OpenSCAP scanner jobs", "dataStream", profile.Spec.DataStream.ContentFile)

	// Determine scan type from profile or scan spec
	scanType := profile.Spec.DataStream.ScanType
	if scanType == "" {
		scanType = scan.Spec.ScanType
	}
	if scanType == "" {
		scanType = "both" // Default to both if not specified
	}

	jobsCreated := 0

	// Create platform scan job if needed
	if scanType == "platform" || scanType == "both" {
		if err := r.createOpenSCAPPlatformJob(ctx, scan, profile, scanID); err != nil {
			return fmt.Errorf("failed to create OpenSCAP platform job: %v", err)
		}
		jobsCreated++
	}

	// Create node scan jobs if needed
	if scanType == "node" || scanType == "both" {
		nodeJobsCount, err := r.createOpenSCAPNodeJobs(ctx, scan, profile, scanID)
		if err != nil {
			return fmt.Errorf("failed to create OpenSCAP node jobs: %v", err)
		}
		jobsCreated += nodeJobsCount
	}

	log.Info("OpenSCAP scanner jobs creation completed", "scan", scan.Name, "jobsCreated", jobsCreated)
	return nil
}

// createOpenSCAPPlatformJob creates a platform-level OpenSCAP scan job
func (r *ScanReconciler) createOpenSCAPPlatformJob(ctx context.Context, scan *complianceapi.Scan, profile *complianceapi.Profile, scanID string) error {
	jobName := generateJobName("openscap-platform", scan.Name, "")

	job := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      jobName,
			Namespace: scan.Namespace,
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan":      scan.Name,
				"compliance-operator.alauda.io/scan-type": "platform",
				"compliance-operator.alauda.io/scan-id":   scanID,
				"compliance-operator.alauda.io/scanner":   "openscap",
				"compliance-operator.alauda.io/profile":   profile.Name,
				"compliance-operator.alauda.io/temporary": "true",
			},
		},
		Spec: batchv1.JobSpec{
			TTLSecondsAfterFinished: pointer.Int32Ptr(3600), // Auto cleanup after 5 minutes
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"compliance-operator.alauda.io/scan":      scan.Name,
						"compliance-operator.alauda.io/scan-type": "platform",
						"compliance-operator.alauda.io/scan-id":   scanID,
						"compliance-operator.alauda.io/scanner":   "openscap",
						"compliance-operator.alauda.io/profile":   profile.Name,
						"compliance-operator.alauda.io/temporary": "true",
					},
				},
				Spec: corev1.PodSpec{
					RestartPolicy:      corev1.RestartPolicyNever,
					ServiceAccountName: "compliance-scanner",
					InitContainers: []corev1.Container{
						{
							Name:            "content-extractor",
							Image:           r.getContentImage(),
							ImagePullPolicy: corev1.PullAlways,
							Command:         []string{"/bin/sh", "-c"},
							Args: []string{
								"echo 'Extracting content files...' && " +
									"cp -v /content/*.xml /shared-content/ && " +
									"ls -la /shared-content/ && " +
									"echo 'Content extraction completed'",
							},
							VolumeMounts: []corev1.VolumeMount{
								{
									Name:      "shared-content",
									MountPath: "/shared-content",
								},
							},
							SecurityContext: &corev1.SecurityContext{
								RunAsUser:                pointer.Int64Ptr(0),
								RunAsGroup:               pointer.Int64Ptr(0),
								AllowPrivilegeEscalation: pointer.BoolPtr(false),
								ReadOnlyRootFilesystem:   pointer.BoolPtr(false),
								Capabilities: &corev1.Capabilities{
									Drop: []corev1.Capability{"ALL"},
								},
							},
						},
					},
					Containers: []corev1.Container{
						{
							Name:            "openscap-scanner",
							Image:           r.getOpenSCAPScannerImage(),
							ImagePullPolicy: corev1.PullAlways,
							Env: []corev1.EnvVar{
								{Name: "PROFILE", Value: profile.Spec.DataStream.ProfileID},
								{Name: "CONTENT", Value: profile.Spec.DataStream.ContentFile},
								{Name: "NODE_NAME", Value: "platform"},
								{Name: "SCAN_ID", Value: scanID},
								{Name: "SCAN_NAME", Value: scan.Name},
								{Name: "JOB_NAME", Value: jobName},
								{Name: "NAMESPACE", Value: scan.Namespace},
								{Name: "HOSTROOT", Value: "/host"},
								{Name: "REPORT_DIR", Value: "/reports"},
								{Name: "CONTENT_IMAGE", Value: r.getContentImage()},
								{Name: "OPENSCAP_REPORT_SERVICE_URL", Value: "http://openscap-report-service.compliance-system.svc.cluster.local:8080"},
							},
							VolumeMounts: []corev1.VolumeMount{
								{
									Name:      "shared-content",
									MountPath: "/shared-content",
									ReadOnly:  true,
								},
								{
									Name:      "host-proc",
									MountPath: "/host/proc",
									ReadOnly:  true,
								},
								{
									Name:      "host-etc-kubernetes",
									MountPath: "/host/etc/kubernetes",
									ReadOnly:  true,
								},
								{
									Name:      "reports",
									MountPath: "/reports",
								},
							},
							SecurityContext: &corev1.SecurityContext{
								RunAsUser:                pointer.Int64Ptr(0),
								RunAsGroup:               pointer.Int64Ptr(0),
								Privileged:               pointer.BoolPtr(false),
								AllowPrivilegeEscalation: pointer.BoolPtr(false),
								ReadOnlyRootFilesystem:   pointer.BoolPtr(false),
								Capabilities: &corev1.Capabilities{
									Add: []corev1.Capability{"DAC_OVERRIDE", "FOWNER"},
								},
							},
						},
					},
					Volumes: []corev1.Volume{
						{
							Name: "shared-content",
							VolumeSource: corev1.VolumeSource{
								EmptyDir: &corev1.EmptyDirVolumeSource{},
							},
						},
						{
							Name: "host-proc",
							VolumeSource: corev1.VolumeSource{
								HostPath: &corev1.HostPathVolumeSource{
									Path: "/proc",
								},
							},
						},
						{
							Name: "host-etc-kubernetes",
							VolumeSource: corev1.VolumeSource{
								HostPath: &corev1.HostPathVolumeSource{
									Path: "/etc/kubernetes",
								},
							},
						},
						{
							Name: "reports",
							VolumeSource: corev1.VolumeSource{
								EmptyDir: &corev1.EmptyDirVolumeSource{},
							},
						},
					},
				},
			},
		},
	}

	// Set owner reference
	if err := controllerutil.SetControllerReference(scan, job, r.Scheme); err != nil {
		return err
	}

	if err := r.Create(ctx, job); err != nil {
		return err
	}

	r.Log.Info("Created OpenSCAP platform scan job", "scan", scan.Name, "jobName", job.Name, "scanID", scanID)
	return nil
}

// createOpenSCAPNodeJobs creates node-level OpenSCAP scan jobs with enhanced node selection
func (r *ScanReconciler) createOpenSCAPNodeJobs(ctx context.Context, scan *complianceapi.Scan, profile *complianceapi.Profile, scanID string) (int, error) {
	log := r.Log.WithValues("scan", scan.Name, "namespace", scan.Namespace, "scanID", scanID)

	// Get all nodes that match the basic selector
	var allNodes corev1.NodeList
	if err := r.List(ctx, &allNodes, client.MatchingLabels(scan.Spec.NodeSelector)); err != nil {
		return 0, err
	}

	// Apply enhanced node filtering based on NodeScopeStrategy and TargetNodeRoles
	targetNodes := r.filterNodesForScan(allNodes.Items, scan)

	log.Info("Creating OpenSCAP node scan jobs",
		"totalNodes", len(allNodes.Items),
		"targetNodes", len(targetNodes),
		"nodeScopeStrategy", scan.Spec.NodeScopeStrategy,
		"targetNodeRoles", scan.Spec.TargetNodeRoles)

	// Prepare jobs for batch creation
	var jobs []*batchv1.Job

	for _, node := range targetNodes {
		nodeRole := r.getNodeRole(node)
		jobName := generateJobName("openscap-node", scan.Name, node.Name)

		// Enhanced job labels with node role and scheduling information
		jobLabels := map[string]string{
			"compliance-operator.alauda.io/scan":      scan.Name,
			"compliance-operator.alauda.io/scan-type": "node",
			"compliance-operator.alauda.io/scan-id":   scanID,
			"compliance-operator.alauda.io/scanner":   "openscap",
			"compliance-operator.alauda.io/profile":   profile.Name,
			"compliance-operator.alauda.io/node":      node.Name,
			"compliance-operator.alauda.io/node-role": nodeRole,
			"compliance-operator.alauda.io/temporary": "true",
		}

		// Add node scope strategy label if specified
		if scan.Spec.NodeScopeStrategy != "" {
			jobLabels["compliance-operator.alauda.io/node-scope-strategy"] = scan.Spec.NodeScopeStrategy
		}

		job := &batchv1.Job{
			ObjectMeta: metav1.ObjectMeta{
				Name:      jobName,
				Namespace: scan.Namespace,
				Labels:    jobLabels,
			},
			Spec: batchv1.JobSpec{
				TTLSecondsAfterFinished: pointer.Int32Ptr(3600), // Auto cleanup after 5 minutes
				Template: corev1.PodTemplateSpec{
					ObjectMeta: metav1.ObjectMeta{
						Labels: jobLabels,
					},
					Spec: corev1.PodSpec{
						RestartPolicy:      corev1.RestartPolicyNever,
						ServiceAccountName: "compliance-scanner",
						NodeName:           node.Name,
						HostNetwork:        false,
						HostPID:            false,
						// Add tolerations for control-plane nodes if needed
						Tolerations: r.getNodeTolerationsForRole(nodeRole),
						SecurityContext: &corev1.PodSecurityContext{
							RunAsUser:  pointer.Int64Ptr(0),
							RunAsGroup: pointer.Int64Ptr(0),
							FSGroup:    pointer.Int64Ptr(0),
						},
						InitContainers: []corev1.Container{
							{
								Name:            "content-extractor",
								Image:           r.getContentImage(),
								ImagePullPolicy: corev1.PullAlways,
								Command:         []string{"/bin/sh", "-c"},
								Args: []string{
									"echo 'Extracting content files...' && " +
										"cp -v /content/*.xml /shared-content/ && " +
										"ls -la /shared-content/ && " +
										"echo 'Content extraction completed'",
								},
								VolumeMounts: []corev1.VolumeMount{
									{
										Name:      "shared-content",
										MountPath: "/shared-content",
									},
								},
								SecurityContext: &corev1.SecurityContext{
									RunAsUser:                pointer.Int64Ptr(0),
									RunAsGroup:               pointer.Int64Ptr(0),
									AllowPrivilegeEscalation: pointer.BoolPtr(false),
									ReadOnlyRootFilesystem:   pointer.BoolPtr(false),
									Capabilities: &corev1.Capabilities{
										Drop: []corev1.Capability{"ALL"},
									},
								},
							},
						},
						Containers: []corev1.Container{
							{
								Name:            "openscap-scanner",
								Image:           r.getOpenSCAPScannerImage(),
								ImagePullPolicy: corev1.PullAlways,
								Env: []corev1.EnvVar{
									{Name: "PROFILE", Value: profile.Spec.DataStream.ProfileID},
									{Name: "CONTENT", Value: profile.Spec.DataStream.ContentFile},
									{Name: "NODE_NAME", Value: node.Name},
									{Name: "NODE_ROLE", Value: nodeRole},
									{Name: "SCAN_ID", Value: scanID},
									{Name: "SCAN_NAME", Value: scan.Name},
									{Name: "JOB_NAME", Value: jobName},
									{Name: "NAMESPACE", Value: scan.Namespace},
									{Name: "HOSTROOT", Value: "/host"},
									{Name: "REPORT_DIR", Value: "/reports"},
									{Name: "CONTENT_IMAGE", Value: r.getContentImage()},
									{Name: "OPENSCAP_REPORT_SERVICE_URL", Value: "http://openscap-report-service.compliance-system.svc.cluster.local:8080"},
									{Name: "NODE_SCOPE_STRATEGY", Value: scan.Spec.NodeScopeStrategy},
								},
								VolumeMounts: []corev1.VolumeMount{
									{
										Name:      "shared-content",
										MountPath: "/shared-content",
										ReadOnly:  true,
									},
									{
										Name:      "host-root",
										MountPath: "/host",
										ReadOnly:  true,
									},
									{
										Name:      "reports",
										MountPath: "/reports",
									},
								},
								SecurityContext: &corev1.SecurityContext{
									RunAsUser:                pointer.Int64Ptr(0),
									RunAsGroup:               pointer.Int64Ptr(0),
									Privileged:               pointer.BoolPtr(true),
									AllowPrivilegeEscalation: pointer.BoolPtr(true),
									ReadOnlyRootFilesystem:   pointer.BoolPtr(false),
									Capabilities: &corev1.Capabilities{
										Add: []corev1.Capability{"SYS_ADMIN", "DAC_OVERRIDE", "FOWNER", "SETUID", "SETGID"},
									},
								},
							},
						},
						Volumes: []corev1.Volume{
							{
								Name: "shared-content",
								VolumeSource: corev1.VolumeSource{
									EmptyDir: &corev1.EmptyDirVolumeSource{},
								},
							},
							{
								Name: "host-root",
								VolumeSource: corev1.VolumeSource{
									HostPath: &corev1.HostPathVolumeSource{
										Path: "/",
										Type: (*corev1.HostPathType)(pointer.StringPtr("Directory")),
									},
								},
							},
							{
								Name: "reports",
								VolumeSource: corev1.VolumeSource{
									EmptyDir: &corev1.EmptyDirVolumeSource{},
								},
							},
						},
					},
				},
			},
		}

		// Set owner reference
		if err := controllerutil.SetControllerReference(scan, job, r.Scheme); err != nil {
			return 0, err
		}

		jobs = append(jobs, job)
	}

	if len(jobs) == 0 {
		log.Info("No OpenSCAP node jobs to create")
		return 0, nil
	}

	// Create jobs in batches
	batchCreator := NewBatchJobCreator()
	if err := batchCreator.createJobsBatch(ctx, r, scan, jobs, scanID); err != nil {
		return 0, fmt.Errorf("failed to create OpenSCAP node jobs in batches: %v", err)
	}

	r.Log.Info("OpenSCAP node scan jobs creation completed", "scan", scan.Name, "jobsCreated", len(jobs))
	return len(jobs), nil
}

// getNodeRole determines the role of a node based on its labels
func (r *ScanReconciler) getNodeRole(node corev1.Node) string {
	// Check for control-plane role (newer Kubernetes versions)
	if _, exists := node.Labels["node-role.kubernetes.io/control-plane"]; exists {
		return "control-plane"
	}

	// Check for master role (older Kubernetes versions)
	if _, exists := node.Labels["node-role.kubernetes.io/master"]; exists {
		return "master"
	}

	// Check for worker role (explicitly labeled)
	if _, exists := node.Labels["node-role.kubernetes.io/worker"]; exists {
		return "worker"
	}

	// Default to worker if no specific role is found
	return "worker"
}

// filterNodesForScan applies enhanced node filtering based on scan configuration
// Note: This function is NOT used for "auto" strategy, which relies entirely on rule-level nodeScope
func (r *ScanReconciler) filterNodesForScan(allNodes []corev1.Node, scan *complianceapi.Scan) []corev1.Node {
	// For manual strategy, also respect TargetNodeRoles if specified
	if scan.Spec.NodeScopeStrategy == "" || scan.Spec.NodeScopeStrategy == "manual" {
		if len(scan.Spec.TargetNodeRoles) > 0 {
			return r.filterNodesByRoles(allNodes, scan.Spec.TargetNodeRoles)
		}
		return allNodes
	}

	// For strict strategy, filter based on TargetNodeRoles if specified
	if scan.Spec.NodeScopeStrategy == "strict" && len(scan.Spec.TargetNodeRoles) > 0 {
		return r.filterNodesByRoles(allNodes, scan.Spec.TargetNodeRoles)
	}

	// Default: return all nodes
	return allNodes
}

// filterNodesByRoles filters nodes based on specified roles
func (r *ScanReconciler) filterNodesByRoles(allNodes []corev1.Node, targetRoles []string) []corev1.Node {
	var filteredNodes []corev1.Node

	// Create a map for quick role lookup
	roleMap := make(map[string]bool)
	for _, role := range targetRoles {
		roleMap[role] = true
	}

	for _, node := range allNodes {
		nodeRole := r.getNodeRole(node)
		if roleMap[nodeRole] {
			filteredNodes = append(filteredNodes, node)
		}
	}

	return filteredNodes
}

// filterNodesByScope filters nodes based on a specific node scope (used for rule-based filtering)
func (r *ScanReconciler) filterNodesByScope(allNodes []corev1.Node, nodeScope string) []corev1.Node {
	var targetNodes []corev1.Node

	for _, node := range allNodes {
		nodeRole := r.getNodeRole(node)

		switch nodeScope {
		case "all", "":
			targetNodes = append(targetNodes, node)
		case "control-plane":
			if nodeRole == "control-plane" {
				targetNodes = append(targetNodes, node)
			}
		case "master":
			if nodeRole == "master" || nodeRole == "control-plane" {
				targetNodes = append(targetNodes, node)
			}
		case "worker":
			if nodeRole == "worker" {
				targetNodes = append(targetNodes, node)
			}
		}
	}

	return targetNodes
}

// getNodeTolerationsForRole returns appropriate tolerations for different node roles
func (r *ScanReconciler) getNodeTolerationsForRole(nodeRole string) []corev1.Toleration {
	var tolerations []corev1.Toleration

	// Add tolerations for control-plane/master nodes
	if nodeRole == "control-plane" || nodeRole == "master" {
		tolerations = append(tolerations, []corev1.Toleration{
			{
				Key:      "node-role.kubernetes.io/control-plane",
				Operator: corev1.TolerationOpExists,
				Effect:   corev1.TaintEffectNoSchedule,
			},
			{
				Key:      "node-role.kubernetes.io/master",
				Operator: corev1.TolerationOpExists,
				Effect:   corev1.TaintEffectNoSchedule,
			},
		}...)
	}

	// Add common tolerations for compliance scanning
	tolerations = append(tolerations, corev1.Toleration{
		Key:      "compliance-operator.alauda.io/scanner",
		Operator: corev1.TolerationOpExists,
		Effect:   corev1.TaintEffectNoSchedule,
	})

	return tolerations
}

// createNodeScanJobsWithAutoStrategy creates aggregated jobs with automatic node scope handling
func (r *ScanReconciler) createNodeScanJobsWithAutoStrategy(ctx context.Context, scan *complianceapi.Scan, rules []complianceapi.Rule, allNodes []corev1.Node, scanID string) error {
	log := r.Log.WithValues("scan", scan.Name, "strategy", "auto-aggregated")

	// Filter node rules
	var nodeRules []complianceapi.Rule
	for _, rule := range rules {
		if rule.Spec.CheckType == "node" {
			nodeRules = append(nodeRules, rule)
		}
	}

	if len(nodeRules) == 0 {
		log.Info("No node rules to process")
		return nil
	}

	log.Info("Creating aggregated auto strategy jobs", "nodeRules", len(nodeRules), "nodes", len(allNodes))

	// Create one aggregated job per node with auto rule filtering
	var jobs []*batchv1.Job
	for _, node := range allNodes {
		// Filter rules applicable to this node using auto strategy
		applicableRules := r.filterRulesForNodeAutoStrategy(nodeRules, node, scan)
		if len(applicableRules) == 0 {
			log.Info("No applicable rules for node (auto strategy)", "node", node.Name)
			continue
		}

		// Create aggregated job for this node
		job, err := r.createAggregatedNodeJob(scan, applicableRules, node, scanID)
		if err != nil {
			return fmt.Errorf("failed to create aggregated auto job for node %s: %v", node.Name, err)
		}

		// Update job labels to indicate auto strategy
		job.Labels["compliance-operator.alauda.io/strategy"] = "auto"

		jobs = append(jobs, job)
		log.Info("Created aggregated auto job for node", "node", node.Name, "applicableRules", len(applicableRules), "jobName", job.Name)
	}

	if len(jobs) == 0 {
		log.Info("No auto strategy aggregated jobs to create")
		return nil
	}

	// Create all jobs in batches
	batchCreator := NewBatchJobCreator()
	if err := batchCreator.createJobsBatch(ctx, r, scan, jobs, scanID); err != nil {
		return fmt.Errorf("failed to create auto strategy aggregated jobs in batches: %v", err)
	}

	log.Info("Auto strategy aggregated node scan jobs creation completed", "jobsCreated", len(jobs))
	return nil
}

// createNodeScanJobsWithStrictStrategy creates aggregated jobs with strict node scope validation
func (r *ScanReconciler) createNodeScanJobsWithStrictStrategy(ctx context.Context, scan *complianceapi.Scan, rules []complianceapi.Rule, allNodes []corev1.Node, scanID string) error {
	log := r.Log.WithValues("scan", scan.Name, "strategy", "strict-aggregated")
	log.Info("Validating rules for strict node scope strategy")

	// Filter node rules
	var nodeRules []complianceapi.Rule
	for _, rule := range rules {
		if rule.Spec.CheckType == "node" {
			nodeRules = append(nodeRules, rule)
		}
	}

	if len(nodeRules) == 0 {
		log.Info("No node rules to validate")
		return nil
	}

	// In strict mode, validate that all rules are compatible with the scan configuration
	for _, rule := range nodeRules {
		// Check if rule's node scope is compatible with scan's target node roles
		if len(scan.Spec.TargetNodeRoles) > 0 {
			ruleNodeScope := rule.Spec.NodeScope
			if ruleNodeScope == "" {
				ruleNodeScope = "all"
			}

			if !r.isNodeScopeCompatible(ruleNodeScope, scan.Spec.TargetNodeRoles) {
				return fmt.Errorf("rule %s with nodeScope '%s' is not compatible with scan targetNodeRoles %v",
					rule.Name, ruleNodeScope, scan.Spec.TargetNodeRoles)
			}
		}
	}

	log.Info("All rules passed strict validation, creating aggregated jobs")

	// If validation passes, create aggregated jobs
	var jobs []*batchv1.Job
	for _, node := range allNodes {
		// Filter rules applicable to this node
		applicableRules := r.filterRulesForNode(nodeRules, node, scan)
		if len(applicableRules) == 0 {
			log.Info("No applicable rules for node (strict strategy)", "node", node.Name)
			continue
		}

		// Create aggregated job for this node
		job, err := r.createAggregatedNodeJob(scan, applicableRules, node, scanID)
		if err != nil {
			return fmt.Errorf("failed to create aggregated strict job for node %s: %v", node.Name, err)
		}

		// Update job labels to indicate strict strategy
		job.Labels["compliance-operator.alauda.io/strategy"] = "strict"

		jobs = append(jobs, job)
		log.Info("Created aggregated strict job for node", "node", node.Name, "applicableRules", len(applicableRules), "jobName", job.Name)
	}

	if len(jobs) == 0 {
		log.Info("No strict strategy aggregated jobs to create")
		return nil
	}

	// Create jobs in batches
	batchCreator := NewBatchJobCreator()
	if err := batchCreator.createJobsBatch(ctx, r, scan, jobs, scanID); err != nil {
		return fmt.Errorf("failed to create strict strategy aggregated jobs in batches: %v", err)
	}

	log.Info("Strict strategy aggregated node scan jobs creation completed", "jobsCreated", len(jobs))
	return nil
}

// groupRulesByNodeScope groups rules by their node scope
func (r *ScanReconciler) groupRulesByNodeScope(rules []complianceapi.Rule) map[string][]complianceapi.Rule {
	groups := make(map[string][]complianceapi.Rule)

	for _, rule := range rules {
		if rule.Spec.CheckType != "node" {
			continue
		}

		nodeScope := rule.Spec.NodeScope
		if nodeScope == "" {
			nodeScope = "all"
		}

		groups[nodeScope] = append(groups[nodeScope], rule)
	}

	return groups
}

// isNodeScopeCompatible checks if a rule's node scope is compatible with target node roles
func (r *ScanReconciler) isNodeScopeCompatible(ruleNodeScope string, targetNodeRoles []string) bool {
	if ruleNodeScope == "all" {
		return true
	}

	for _, role := range targetNodeRoles {
		if ruleNodeScope == role ||
			(ruleNodeScope == "master" && role == "control-plane") ||
			(ruleNodeScope == "control-plane" && role == "master") {
			return true
		}
	}

	return false
}

// createSingleNodeScanJob creates a single node scan job for a specific rule and node
func (r *ScanReconciler) createSingleNodeScanJob(ctx context.Context, scan *complianceapi.Scan, rule complianceapi.Rule, node corev1.Node, scanID string) error {
	nodeRole := r.getNodeRole(node)
	jobName := generateJobName("node", rule.Name, node.Name)

	// Enhanced job labels with node role and rule information
	jobLabels := map[string]string{
		"compliance-operator.alauda.io/scan":       scan.Name,
		"compliance-operator.alauda.io/scan-type":  "node",
		"compliance-operator.alauda.io/scan-id":    scanID,
		"compliance-operator.alauda.io/scanner":    "unified",
		"compliance-operator.alauda.io/rule":       rule.Name,
		"compliance-operator.alauda.io/node":       node.Name,
		"compliance-operator.alauda.io/node-role":  nodeRole,
		"compliance-operator.alauda.io/node-scope": rule.Spec.NodeScope,
		"compliance-operator.alauda.io/temporary":  "true",
	}

	// Add node scope strategy label if specified
	if scan.Spec.NodeScopeStrategy != "" {
		jobLabels["compliance-operator.alauda.io/node-scope-strategy"] = scan.Spec.NodeScopeStrategy
	}

	job := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      jobName,
			Namespace: scan.Namespace,
			Labels:    jobLabels,
		},
		Spec: batchv1.JobSpec{
			TTLSecondsAfterFinished: pointer.Int32Ptr(3600), // Auto cleanup after 5 minutes
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: jobLabels,
				},
				Spec: corev1.PodSpec{
					RestartPolicy:      corev1.RestartPolicyNever,
					ServiceAccountName: "compliance-scanner",
					NodeName:           node.Name,
					// Add tolerations for control-plane nodes if needed
					Tolerations: r.getNodeTolerationsForRole(nodeRole),
					SecurityContext: &corev1.PodSecurityContext{
						RunAsUser:  pointer.Int64Ptr(0),
						RunAsGroup: pointer.Int64Ptr(0),
					},
					Containers: []corev1.Container{
						{
							Name:            "node-scanner",
							Image:           r.getUnifiedScannerImage(),
							ImagePullPolicy: corev1.PullAlways,
							Command:         []string{"/usr/local/bin/unified-scanner.sh"},
							Args:            []string{rule.Spec.CheckScript},
							SecurityContext: &corev1.SecurityContext{
								Privileged:   pointer.BoolPtr(true),
								RunAsUser:    pointer.Int64Ptr(0),
								RunAsGroup:   pointer.Int64Ptr(0),
								RunAsNonRoot: pointer.BoolPtr(false),
							},
							Env: []corev1.EnvVar{
								{Name: "RULE_ID", Value: rule.Name},
								{Name: "CHECK_TYPE", Value: "node"},
								{Name: "NODE_SCOPE", Value: rule.Spec.NodeScope},
								{Name: "SCAN_NAME", Value: scan.Name},
								{Name: "NAMESPACE", Value: scan.Namespace},
								{Name: "JOB_NAME", Value: jobName},
								{Name: "SCAN_ID", Value: scanID},
								{Name: "NODE_NAME", Value: node.Name},
								{Name: "NODE_ROLE", Value: nodeRole},
							},
							VolumeMounts: []corev1.VolumeMount{
								{
									Name:      "host-root",
									MountPath: "/host",
									ReadOnly:  true,
								},
								{
									Name:      "results",
									MountPath: "/tmp/results",
								},
							},
						},
					},
					Volumes: []corev1.Volume{
						{
							Name: "host-root",
							VolumeSource: corev1.VolumeSource{
								HostPath: &corev1.HostPathVolumeSource{
									Path: "/",
								},
							},
						},
						{
							Name: "results",
							VolumeSource: corev1.VolumeSource{
								EmptyDir: &corev1.EmptyDirVolumeSource{},
							},
						},
					},
				},
			},
		},
	}

	if err := r.Create(ctx, job); err != nil {
		return err
	}

	r.Log.Info("Created enhanced node scan job", "scan", scan.Name, "rule", rule.Name, "node", node.Name, "nodeRole", nodeRole, "nodeScope", rule.Spec.NodeScope, "jobName", job.Name, "scanID", scanID)
	return nil
}

// createSingleNodeScanJobObject creates a single node scan job object for a specific rule and node
func (r *ScanReconciler) createSingleNodeScanJobObject(scan *complianceapi.Scan, rule complianceapi.Rule, node corev1.Node, scanID string) (*batchv1.Job, error) {
	nodeRole := r.getNodeRole(node)
	jobName := generateJobName("node", rule.Name, node.Name)

	// Enhanced job labels with node role and rule information
	jobLabels := map[string]string{
		"compliance-operator.alauda.io/scan":       scan.Name,
		"compliance-operator.alauda.io/scan-type":  "node",
		"compliance-operator.alauda.io/scan-id":    scanID,
		"compliance-operator.alauda.io/scanner":    "unified",
		"compliance-operator.alauda.io/rule":       rule.Name,
		"compliance-operator.alauda.io/node":       node.Name,
		"compliance-operator.alauda.io/node-role":  nodeRole,
		"compliance-operator.alauda.io/node-scope": rule.Spec.NodeScope,
		"compliance-operator.alauda.io/temporary":  "true",
	}

	// Add node scope strategy label if specified
	if scan.Spec.NodeScopeStrategy != "" {
		jobLabels["compliance-operator.alauda.io/node-scope-strategy"] = scan.Spec.NodeScopeStrategy
	}

	job := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      jobName,
			Namespace: scan.Namespace,
			Labels:    jobLabels,
		},
		Spec: batchv1.JobSpec{
			TTLSecondsAfterFinished: pointer.Int32Ptr(3600), // Auto cleanup after 1 hour
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: jobLabels,
				},
				Spec: corev1.PodSpec{
					RestartPolicy:      corev1.RestartPolicyNever,
					ServiceAccountName: "compliance-scanner",
					NodeName:           node.Name,
					// Add tolerations for control-plane nodes if needed
					Tolerations: r.getNodeTolerationsForRole(nodeRole),
					SecurityContext: &corev1.PodSecurityContext{
						RunAsUser:  pointer.Int64Ptr(0),
						RunAsGroup: pointer.Int64Ptr(0),
					},
					Containers: []corev1.Container{
						{
							Name:            "node-scanner",
							Image:           r.getUnifiedScannerImage(),
							ImagePullPolicy: corev1.PullAlways,
							Command:         []string{"/usr/local/bin/unified-scanner.sh"},
							Args:            []string{rule.Spec.CheckScript},
							SecurityContext: &corev1.SecurityContext{
								Privileged:   pointer.BoolPtr(true),
								RunAsUser:    pointer.Int64Ptr(0),
								RunAsGroup:   pointer.Int64Ptr(0),
								RunAsNonRoot: pointer.BoolPtr(false),
							},
							Env: []corev1.EnvVar{
								{Name: "RULE_ID", Value: rule.Name},
								{Name: "CHECK_TYPE", Value: "node"},
								{Name: "NODE_SCOPE", Value: rule.Spec.NodeScope},
								{Name: "SCAN_NAME", Value: scan.Name},
								{Name: "NAMESPACE", Value: scan.Namespace},
								{Name: "JOB_NAME", Value: jobName},
								{Name: "SCAN_ID", Value: scanID},
								{Name: "NODE_NAME", Value: node.Name},
								{Name: "NODE_ROLE", Value: nodeRole},
							},
							VolumeMounts: []corev1.VolumeMount{
								{
									Name:      "host-root",
									MountPath: "/host",
									ReadOnly:  true,
								},
								{
									Name:      "results",
									MountPath: "/tmp/results",
								},
							},
						},
					},
					Volumes: []corev1.Volume{
						{
							Name: "host-root",
							VolumeSource: corev1.VolumeSource{
								HostPath: &corev1.HostPathVolumeSource{
									Path: "/",
								},
							},
						},
						{
							Name: "results",
							VolumeSource: corev1.VolumeSource{
								EmptyDir: &corev1.EmptyDirVolumeSource{},
							},
						},
					},
				},
			},
		},
	}

	// Set owner reference
	if err := controllerutil.SetControllerReference(scan, job, r.Scheme); err != nil {
		return nil, err
	}

	return job, nil
}

// filterRulesForNode filters rules that are applicable to a specific node
func (r *ScanReconciler) filterRulesForNode(rules []complianceapi.Rule, node corev1.Node, scan *complianceapi.Scan) []complianceapi.Rule {
	var applicableRules []complianceapi.Rule
	nodeRole := r.getNodeRole(node)

	for _, rule := range rules {
		if rule.Spec.CheckType != "node" {
			continue
		}

		// Check if rule's nodeScope matches this node
		ruleNodeScope := rule.Spec.NodeScope
		if ruleNodeScope == "" {
			ruleNodeScope = "all" // Default to all if not specified
		}

		isApplicable := false
		switch ruleNodeScope {
		case "all", "":
			isApplicable = true
		case "control-plane":
			isApplicable = (nodeRole == "control-plane" || nodeRole == "master")
		case "master":
			isApplicable = (nodeRole == "master" || nodeRole == "control-plane")
		case "worker":
			isApplicable = (nodeRole == "worker")
		default:
			// Custom node scope - check if it matches node role
			isApplicable = (ruleNodeScope == nodeRole)
		}

		if isApplicable {
			applicableRules = append(applicableRules, rule)
		}
	}

	return applicableRules
}

// filterRulesForNodeAutoStrategy filters rules for auto strategy (more intelligent filtering)
func (r *ScanReconciler) filterRulesForNodeAutoStrategy(rules []complianceapi.Rule, node corev1.Node, scan *complianceapi.Scan) []complianceapi.Rule {
	var applicableRules []complianceapi.Rule
	nodeRole := r.getNodeRole(node)

	for _, rule := range rules {
		if rule.Spec.CheckType != "node" {
			continue
		}

		// Auto strategy: intelligently determine if rule applies to this node
		ruleNodeScope := rule.Spec.NodeScope
		if ruleNodeScope == "" {
			ruleNodeScope = "all" // Default to all if not specified
		}

		isApplicable := false

		// Check against scan's target node roles if specified
		if len(scan.Spec.TargetNodeRoles) > 0 {
			// If scan has target roles, check if this node's role is in the target list
			nodeInTarget := false
			for _, targetRole := range scan.Spec.TargetNodeRoles {
				if nodeRole == targetRole ||
					(targetRole == "control-plane" && nodeRole == "master") ||
					(targetRole == "master" && nodeRole == "control-plane") {
					nodeInTarget = true
					break
				}
			}

			if !nodeInTarget {
				continue // Skip this rule if node is not in target roles
			}
		}

		// Now check rule's node scope
		switch ruleNodeScope {
		case "all", "":
			isApplicable = true
		case "control-plane":
			isApplicable = (nodeRole == "control-plane" || nodeRole == "master")
		case "master":
			isApplicable = (nodeRole == "master" || nodeRole == "control-plane")
		case "worker":
			isApplicable = (nodeRole == "worker")
		default:
			// Custom node scope - check if it matches node role
			isApplicable = (ruleNodeScope == nodeRole)
		}

		if isApplicable {
			applicableRules = append(applicableRules, rule)
		}
	}

	return applicableRules
}

// createAggregatedNodeJob creates a single job that executes multiple rules on one node
func (r *ScanReconciler) createAggregatedNodeJob(scan *complianceapi.Scan, rules []complianceapi.Rule, node corev1.Node, scanID string) (*batchv1.Job, error) {
	nodeRole := r.getNodeRole(node)
	jobName := generateJobName("aggregated-node", scan.Name, node.Name)

	// Build rules list for the scanner
	var ruleNames []string
	var ruleScripts []string
	for _, rule := range rules {
		ruleNames = append(ruleNames, rule.Name)
		ruleScripts = append(ruleScripts, rule.Spec.CheckScript)
	}

	// Enhanced job labels with aggregation information
	jobLabels := map[string]string{
		"compliance-operator.alauda.io/scan":       scan.Name,
		"compliance-operator.alauda.io/scan-type":  "node",
		"compliance-operator.alauda.io/scan-id":    scanID,
		"compliance-operator.alauda.io/scanner":    "unified",
		"compliance-operator.alauda.io/node":       node.Name,
		"compliance-operator.alauda.io/node-role":  nodeRole,
		"compliance-operator.alauda.io/job-type":   "aggregated",
		"compliance-operator.alauda.io/rule-count": fmt.Sprintf("%d", len(rules)),
		"compliance-operator.alauda.io/temporary":  "true",
	}

	// Add node scope strategy label if specified
	if scan.Spec.NodeScopeStrategy != "" {
		jobLabels["compliance-operator.alauda.io/node-scope-strategy"] = scan.Spec.NodeScopeStrategy
	}

	job := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      jobName,
			Namespace: scan.Namespace,
			Labels:    jobLabels,
		},
		Spec: batchv1.JobSpec{
			TTLSecondsAfterFinished: pointer.Int32Ptr(3600), // Auto cleanup after 1 hour
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: jobLabels,
				},
				Spec: corev1.PodSpec{
					RestartPolicy:      corev1.RestartPolicyNever,
					ServiceAccountName: "compliance-scanner",
					NodeName:           node.Name,
					HostNetwork:        true,
					HostPID:            true,
					HostIPC:            true,
					// Add tolerations for control-plane nodes if needed
					Tolerations: r.getNodeTolerationsForRole(nodeRole),
					SecurityContext: &corev1.PodSecurityContext{
						RunAsUser:  pointer.Int64Ptr(0),
						RunAsGroup: pointer.Int64Ptr(0),
					},
					Containers: []corev1.Container{
						{
							Name:            "aggregated-node-scanner",
							Image:           r.getUnifiedScannerImage(),
							ImagePullPolicy: corev1.PullAlways,
							Command:         []string{"/usr/local/bin/unified-scanner.sh"},
							Args:            []string{"--mode=aggregated"},
							SecurityContext: &corev1.SecurityContext{
								Privileged:   pointer.BoolPtr(true),
								RunAsUser:    pointer.Int64Ptr(0),
								RunAsGroup:   pointer.Int64Ptr(0),
								RunAsNonRoot: pointer.BoolPtr(false),
							},
							Env: []corev1.EnvVar{
								{Name: "SCAN_MODE", Value: "aggregated"},
								{Name: "RULE_COUNT", Value: fmt.Sprintf("%d", len(rules))},
								{Name: "RULE_NAMES", Value: strings.Join(ruleNames, ",")},
								{Name: "CHECK_TYPE", Value: "node"},
								{Name: "SCAN_NAME", Value: scan.Name},
								{Name: "NAMESPACE", Value: scan.Namespace},
								{Name: "JOB_NAME", Value: jobName},
								{Name: "SCAN_ID", Value: scanID},
								{Name: "NODE_NAME", Value: node.Name},
								{Name: "NODE_ROLE", Value: nodeRole},
							},
							VolumeMounts: []corev1.VolumeMount{
								{
									Name:      "host-root",
									MountPath: "/host",
									ReadOnly:  true,
								},
								{
									Name:      "results",
									MountPath: "/tmp/results",
								},
								{
									Name:      "rule-scripts",
									MountPath: "/tmp/rule-scripts",
									ReadOnly:  true,
								},
							},
						},
					},
					Volumes: []corev1.Volume{
						{
							Name: "host-root",
							VolumeSource: corev1.VolumeSource{
								HostPath: &corev1.HostPathVolumeSource{
									Path: "/",
								},
							},
						},
						{
							Name: "results",
							VolumeSource: corev1.VolumeSource{
								EmptyDir: &corev1.EmptyDirVolumeSource{},
							},
						},
						{
							Name: "rule-scripts",
							VolumeSource: corev1.VolumeSource{
								ConfigMap: &corev1.ConfigMapVolumeSource{
									LocalObjectReference: corev1.LocalObjectReference{
										Name: r.createRuleScriptsConfigMap(scan, rules, scanID),
									},
								},
							},
						},
					},
				},
			},
		},
	}

	// Set owner reference
	if err := controllerutil.SetControllerReference(scan, job, r.Scheme); err != nil {
		return nil, err
	}

	return job, nil
}

// createRuleScriptsConfigMap creates a ConfigMap containing all rule scripts for aggregated execution
func (r *ScanReconciler) createRuleScriptsConfigMap(scan *complianceapi.Scan, rules []complianceapi.Rule, scanID string) string {
	// Use consistent naming for the ConfigMap
	configMapName := fmt.Sprintf("rule-scripts-%s", scanID)

	// This function returns the name that should be used
	// The actual ConfigMap creation will be handled separately to avoid circular dependencies
	return configMapName
}

// createRuleScriptsConfigMapObject creates the actual ConfigMap object with rule scripts
func (r *ScanReconciler) createRuleScriptsConfigMapObject(ctx context.Context, scan *complianceapi.Scan, rules []complianceapi.Rule, scanID string, configMapName string) error {
	log := r.Log.WithValues("scan", scan.Name, "configMap", configMapName)

	// Prepare script data
	scriptData := make(map[string]string)

	// Add individual rule scripts
	for i, rule := range rules {
		scriptKey := fmt.Sprintf("rule_%d_%s.sh", i, cleanKubernetesName(rule.Name))
		scriptData[scriptKey] = rule.Spec.CheckScript
	}

	// Add rule metadata
	var ruleMetadata []string
	for i, rule := range rules {
		metadata := fmt.Sprintf("%d:%s:%s:%s", i, rule.Name, rule.Spec.Severity, rule.Spec.NodeScope)
		ruleMetadata = append(ruleMetadata, metadata)
	}
	scriptData["rule_metadata.txt"] = strings.Join(ruleMetadata, "\n")

	// Add aggregated execution script
	scriptData["execute_aggregated.sh"] = r.generateAggregatedExecutionScript()

	configMap := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      configMapName,
			Namespace: scan.Namespace,
			Labels: map[string]string{
				"compliance-operator.alauda.io/scan":          scan.Name,
				"compliance-operator.alauda.io/scan-id":       scanID,
				"compliance-operator.alauda.io/resource-type": "rule-scripts",
				"compliance-operator.alauda.io/temporary":     "true",
			},
		},
		Data: scriptData,
	}

	// Set owner reference
	if err := controllerutil.SetControllerReference(scan, configMap, r.Scheme); err != nil {
		return fmt.Errorf("failed to set owner reference: %v", err)
	}

	// Create the ConfigMap
	if err := r.Create(ctx, configMap); err != nil {
		return fmt.Errorf("failed to create rule scripts ConfigMap: %v", err)
	}

	log.Info("Created rule scripts ConfigMap", "ruleCount", len(rules))
	return nil
}

// generateAggregatedExecutionScript generates the main script that executes all rules
func (r *ScanReconciler) generateAggregatedExecutionScript() string {
	return `#!/bin/bash
set -euo pipefail

# Aggregated Rule Execution Script
echo "=== Starting Aggregated Compliance Scan ==="
echo "Node: $(hostname)"
echo "Scan ID: ${SCAN_ID}"
echo "Job: ${JOB_NAME}"
echo "Rule Count: ${RULE_COUNT}"

# Initialize result tracking
RESULTS_DIR="/tmp/results"
mkdir -p "${RESULTS_DIR}"
OVERALL_EXIT_CODE=0
EXECUTED_RULES=0
PASSED_RULES=0
FAILED_RULES=0
ERROR_RULES=0

# Read rule metadata
RULE_METADATA_FILE="/tmp/rule-scripts/rule_metadata.txt"
if [ ! -f "${RULE_METADATA_FILE}" ]; then
    echo "ERROR: Rule metadata file not found"
    exit 1
fi

# Execute each rule
while IFS=':' read -r rule_index rule_name rule_severity rule_scope; do
    echo ""
    echo "=== Executing Rule ${rule_index}: ${rule_name} ==="
    echo "Severity: ${rule_severity}"
    echo "Node Scope: ${rule_scope}"

    RULE_SCRIPT="/tmp/rule-scripts/rule_${rule_index}_$(echo ${rule_name} | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]/-/g').sh"
    RULE_RESULT_FILE="${RESULTS_DIR}/rule_${rule_index}_${rule_name}.result"

    if [ ! -f "${RULE_SCRIPT}" ]; then
        echo "ERROR: Rule script not found: ${RULE_SCRIPT}"
        echo "1" > "${RULE_RESULT_FILE}.exit_code"
        echo "Rule script not found" > "${RULE_RESULT_FILE}.output"
        ERROR_RULES=$((ERROR_RULES + 1))
        OVERALL_EXIT_CODE=1
        continue
    fi

    # Make script executable
    chmod +x "${RULE_SCRIPT}"

    # Execute rule with timeout
    RULE_EXIT_CODE=0
    RULE_OUTPUT=""

    if timeout 300 bash "${RULE_SCRIPT}" > "${RULE_RESULT_FILE}.output" 2>&1; then
        RULE_EXIT_CODE=0
        PASSED_RULES=$((PASSED_RULES + 1))
        echo "✓ Rule ${rule_name} PASSED"
    else
        RULE_EXIT_CODE=$?
        if [ ${RULE_EXIT_CODE} -eq 124 ]; then
            echo "⚠ Rule ${rule_name} TIMEOUT"
            echo "Rule execution timed out after 300 seconds" >> "${RULE_RESULT_FILE}.output"
            ERROR_RULES=$((ERROR_RULES + 1))
        elif [ ${RULE_EXIT_CODE} -eq 1 ]; then
            echo "✗ Rule ${rule_name} FAILED"
            FAILED_RULES=$((FAILED_RULES + 1))
        else
            echo "⚠ Rule ${rule_name} ERROR (exit code: ${RULE_EXIT_CODE})"
            ERROR_RULES=$((ERROR_RULES + 1))
        fi
        OVERALL_EXIT_CODE=1
    fi

    # Save rule result metadata
    echo "${RULE_EXIT_CODE}" > "${RULE_RESULT_FILE}.exit_code"
    echo "$(date -u +%Y-%m-%dT%H:%M:%SZ)" > "${RULE_RESULT_FILE}.timestamp"

    # Create individual result ConfigMap for this rule
    create_rule_result_configmap "${rule_name}" "${RULE_EXIT_CODE}" "${RULE_RESULT_FILE}.output"

    EXECUTED_RULES=$((EXECUTED_RULES + 1))

done < "${RULE_METADATA_FILE}"

echo ""
echo "=== Aggregated Scan Summary ==="
echo "Total Rules: ${RULE_COUNT}"
echo "Executed: ${EXECUTED_RULES}"
echo "Passed: ${PASSED_RULES}"
echo "Failed: ${FAILED_RULES}"
echo "Errors: ${ERROR_RULES}"
echo "Overall Result: $([ ${OVERALL_EXIT_CODE} -eq 0 ] && echo "PASS" || echo "FAIL")"

# Create summary result
cat > "${RESULTS_DIR}/scan_summary.json" << EOF
{
  "scan_id": "${SCAN_ID}",
  "job_name": "${JOB_NAME}",
  "node_name": "${NODE_NAME}",
  "total_rules": ${RULE_COUNT},
  "executed_rules": ${EXECUTED_RULES},
  "passed_rules": ${PASSED_RULES},
  "failed_rules": ${FAILED_RULES},
  "error_rules": ${ERROR_RULES},
  "overall_exit_code": ${OVERALL_EXIT_CODE},
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
}
EOF

echo "=== Aggregated Compliance Scan Completed ==="
exit ${OVERALL_EXIT_CODE}

# Function to create individual rule result ConfigMap
create_rule_result_configmap() {
    local rule_name="$1"
    local exit_code="$2"
    local output_file="$3"

    local output_content=""
    if [ -f "${output_file}" ]; then
        output_content=$(cat "${output_file}")
    fi

    # Create ConfigMap using kubectl (assuming it's available in the scanner image)
    local cm_name="${JOB_NAME}-${rule_name}"

    kubectl create configmap "${cm_name}" \
        --namespace="${NAMESPACE}" \
        --from-literal="scan_name=${SCAN_NAME}" \
        --from-literal="rule_id=${rule_name}" \
        --from-literal="job_name=${JOB_NAME}" \
        --from-literal="scan_id=${SCAN_ID}" \
        --from-literal="node_name=${NODE_NAME}" \
        --from-literal="check_type=node" \
        --from-literal="exit_code=${exit_code}" \
        --from-literal="output=${output_content}" \
        --from-literal="timestamp=$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
        --dry-run=client -o yaml | kubectl apply -f - || true

    # Add labels to the ConfigMap
    kubectl label configmap "${cm_name}" \
        --namespace="${NAMESPACE}" \
        "compliance-operator.alauda.io/scan=${SCAN_NAME}" \
        "compliance-operator.alauda.io/rule=${rule_name}" \
        "compliance-operator.alauda.io/job=${JOB_NAME}" \
        "compliance-operator.alauda.io/scan-id=${SCAN_ID}" \
        "compliance-operator.alauda.io/node=${NODE_NAME}" \
        "compliance-operator.alauda.io/temporary=true" || true
}
`
}

// preCreateRuleScriptsConfigMaps creates ConfigMaps with rule scripts before job creation
func (r *ScanReconciler) preCreateRuleScriptsConfigMaps(ctx context.Context, scan *complianceapi.Scan, rules []complianceapi.Rule, scanID string) error {
	log := r.Log.WithValues("scan", scan.Name, "scanID", scanID)
	log.Info("Pre-creating rule scripts ConfigMaps for aggregated jobs")

	// Group rules by node to create separate ConfigMaps if needed
	// For now, create one ConfigMap per scan that contains all node rules
	configMapName := fmt.Sprintf("rule-scripts-%s", scanID)

	if err := r.createRuleScriptsConfigMapObject(ctx, scan, rules, scanID, configMapName); err != nil {
		return fmt.Errorf("failed to create rule scripts ConfigMap: %v", err)
	}

	log.Info("Successfully pre-created rule scripts ConfigMap", "configMap", configMapName, "ruleCount", len(rules))
	return nil
}

// getUnifiedScannerImage returns the unified scanner image for aggregated jobs
func (r *ScanReconciler) getUnifiedScannerImage() string {
	// Use the same image as node scanner for now
	return r.getNodeScannerImage()
}

// getNodeTolerationsForRole returns appropriate tolerations based on node role
func (r *ScanReconciler) getNodeTolerationsForRole(nodeRole string) []corev1.Toleration {
	tolerations := []corev1.Toleration{
		{
			Operator: corev1.TolerationOpExists,
		},
	}

	// Add specific tolerations for control-plane nodes
	if nodeRole == "control-plane" || nodeRole == "master" {
		tolerations = append(tolerations, corev1.Toleration{
			Key:      "node-role.kubernetes.io/control-plane",
			Operator: corev1.TolerationOpExists,
			Effect:   corev1.TaintEffectNoSchedule,
		})
		tolerations = append(tolerations, corev1.Toleration{
			Key:      "node-role.kubernetes.io/master",
			Operator: corev1.TolerationOpExists,
			Effect:   corev1.TaintEffectNoSchedule,
		})
	}

	return tolerations
}

// cleanKubernetesName cleans a name to be valid for Kubernetes resources
func cleanKubernetesName(name string) string {
	// Convert to lowercase
	cleaned := strings.ToLower(name)

	// Replace invalid characters with hyphens
	cleaned = regexp.MustCompile(`[^a-z0-9-]`).ReplaceAllString(cleaned, "-")

	// Remove consecutive hyphens
	cleaned = regexp.MustCompile(`-+`).ReplaceAllString(cleaned, "-")

	// Remove leading and trailing hyphens
	cleaned = strings.Trim(cleaned, "-")

	// Ensure it's not empty
	if cleaned == "" {
		cleaned = "default"
	}

	// Ensure it doesn't exceed Kubernetes name length limits
	if len(cleaned) > 63 {
		cleaned = cleaned[:63]
		cleaned = strings.TrimSuffix(cleaned, "-")
	}

	return cleaned
}
