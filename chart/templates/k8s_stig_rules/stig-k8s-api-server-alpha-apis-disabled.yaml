apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-alpha-apis-disabled
  namespace: compliance-system
spec:
  id: "V-242400"
  title: "The Kubernetes API server must have Alpha APIs disabled."
  description: "Alpha APIs are experimental APIs that may be removed at any time. These APIs are not enabled by default and should not be used in production environments. Alpha APIs may have security vulnerabilities or instabilities that could lead to system compromise."
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i feature-gates *\n\nIf any line is returned that includes enable-admission-plugins and AllAlpha=true, this is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Remove the AllAlpha=true setting from the feature-gates parameter."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Checking API Server Alpha APIs configuration..."
    
    # Initialize variables
    result="PASSED"
    details=""
    manifests_dir="/host/etc/kubernetes/manifests"
    
    # Check if manifests directory exists
    if [[ ! -d "$manifests_dir" ]]; then
        result="FAILED"
        details="Kubernetes manifests directory not found: $manifests_dir"
        echo "FAIL: $details"
        exit 1
    fi
    
    echo "Searching for API Server manifest files in $manifests_dir"
    
    # Find API server manifest files
    api_files=$(find "$manifests_dir" -name "*.yaml" -exec grep -l 'kube-apiserver' {} \; 2>/dev/null)
    
    if [[ -z "$api_files" ]]; then
      result="FAILED"
      details="No API Server manifest found"
        echo "FAIL: $details"
      exit 1
    fi
    
    echo "Found API Server manifest files: $api_files"
    
    # Check each API server manifest file
    for file in $api_files; do
        echo "Checking file: $(basename "$file")"
        feature_gates=$(grep -i -- "--feature-gates" "$file" 2>/dev/null)
        
        if [[ -n "$feature_gates" ]]; then
            echo "Found feature-gates configuration: $feature_gates"
            if [[ "$feature_gates" =~ AllAlpha=true ]]; then
        result="FAILED"
        details+="${details:+<br>}Alpha APIs enabled via feature-gates in $file"
                echo "VIOLATION: AllAlpha=true found in $(basename "$file")"
            else
                echo "OK: No AllAlpha=true found in feature-gates"
            fi
        else
            echo "OK: No feature-gates configuration found in $(basename "$file")"
      fi
    done
    
    if [[ "$result" == "PASSED" ]]; then
    details="Alpha APIs properly disabled"
        echo "PASS: $details"
    exit 0 
    else
        echo "FAIL: $details"
        exit 1
    fi