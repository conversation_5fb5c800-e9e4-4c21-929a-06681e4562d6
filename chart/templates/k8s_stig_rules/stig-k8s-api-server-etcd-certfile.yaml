apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-etcd-certfile
  namespace: compliance-system
spec:
  id: "V-242430"
  title: "Kubernetes etcd must have a certificate for communication."
  description: "Kubernetes stores configuration and state information in a distributed key-value store called etcd. Anyone who can write to etcd can effectively control the Kubernetes cluster. Even just reading the contents of etcd could easily provide helpful hints to a would-be attacker. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server and etcd with a means to be able to authenticate sessions and encrypt traffic. \n\nTo enable encrypted communication for etcd, the parameter \"--etcd-certfile\" must be set. This parameter gives the location of the SSL certification file used to secure etcd communication."
  checkText: "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\ngrep -i etcd-certfile * \n\nIf the setting \"--etcd-certfile\" is not set in the Kubernetes API Server manifest file, this is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--etcd-certfile\" to the certificate to be used for communication with etcd."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Checking API Server etcd certificate file configuration..."
    manifest="/host/etc/kubernetes/manifests/kube-apiserver.yaml"
    required_param="etcd-certfile"
    
    echo "Checking API Server manifest: $manifest"
    
    # Check if manifest file exists
    if [[ ! -f "$manifest" ]]; then
        echo "ERROR: API Server manifest file not found: $manifest"
        exit 1
    fi
    
    echo "Searching for $required_param parameter in manifest..."
    
    # Check if etcd-certfile parameter is configured
    if ! grep -q -- "--$required_param" "$manifest" 2>/dev/null; then
        echo "FAIL: Missing --$required_param configuration in API Server manifest"
        exit 1
    fi
    
    # Extract certificate file path
    cert_path=$(grep -oP -- "--$required_param=\K[^ ]+" "$manifest" 2>/dev/null)
    
    if [[ -z "$cert_path" ]]; then
        echo "FAIL: Unable to extract certificate file path from --$required_param parameter"
        exit 1
    fi
    
    echo "Found etcd certificate file path: $cert_path"
    
    # Check if certificate file exists (add /host prefix for container environment)
    if [[ "$cert_path" =~ ^/host ]]; then
        host_cert_path="$cert_path"
    else
        host_cert_path="/host$cert_path"
    fi
    
    echo "Checking if certificate file exists: $host_cert_path"
    
    if [[ ! -f "$host_cert_path" ]]; then
        echo "FAIL: etcd certificate file not found: $cert_path"
        exit 1
    fi
    
    echo "PASS: API Server etcd certificate file is properly configured and exists"
    echo "Certificate file: $cert_path"
    exit 0 