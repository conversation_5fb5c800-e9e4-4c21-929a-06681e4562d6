apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-etcd-key-file
  namespace: compliance-system
spec:
  id: "V-242427"
  title: "Etcd must configure key-file"
  description: "The Kubernetes etcd key-value store provides a way to store data to the Control Plane. If these files can be changed, data to API object and Control Plane would be compromised."
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i key-file etcd.yaml \n\nIf the setting \"key-file\" is not set in the Kubernetes etcd manifest file, this is a finding."
  fixText: "Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument \"--key-file\" to the appropriate key file."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting etcd key-file configuration check (V-242427)"
    
    # Use /host prefix for host filesystem access
    manifest="/host/etc/kubernetes/manifests/etcd.yaml"
    required_param="\-\-key-file"

    echo "Checking etcd manifest file: $manifest"

    if [[ -f "$manifest" ]]; then
        echo "Found etcd manifest file"
        echo "Checking for --key-file parameter..."
        
        if ! grep -q "$required_param" "$manifest"; then
            echo "FAIL: Missing --key-file configuration"
            echo "etcd must have --key-file parameter configured"
            exit 1
        else
            echo "Found --key-file parameter in manifest"
            key_path=$(grep -oP "$required_param=\K[^ ]+" "$manifest")
            
            if [[ -n "$key_path" ]]; then
                echo "Key file path: $key_path"
                # Add /host prefix for host file validation
                if [[ ! -f "/host$key_path" ]]; then
                    echo "FAIL: Key file not found: $key_path"
                    echo "The specified key file does not exist on the filesystem"
                    exit 1
                else
                    echo "Found key file at: $key_path"
                fi
            fi
            
            echo "PASS: Etcd key-file properly configured"
            echo "etcd key-file parameter is configured and key file exists"
            exit 0
        fi
    else
        echo "FAIL: Etcd manifest missing"
        echo "Expected to find etcd.yaml manifest file"
        exit 1
    fi
