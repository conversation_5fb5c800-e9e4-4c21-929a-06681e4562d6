apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-kubelet-config-ownership
  namespace: compliance-system
spec:
  id: "V-242406"
  title: "The Kubernetes KubeletConfiguration file must be owned by root."
  description: "Kubernetes KubeletConfiguration file must be owned by root to prevent unauthorized modifications."
  fixText: "On the Control Plane and Worker nodes, change to the --config directory. Run the command:\nchown root:root kubelet\n\nTo verify the change took place, run the command:\nls -l kubelet\n\nThe kubelet file should now be owned by root:root."
  checkText: "On the Kubernetes Control Plane and Worker nodes, run the command:\nps -ef | grep kubelet\n\nCheck the config file (path identified by: --config):\n\nChange to the directory identified by --config (example /etc/sysconfig/) run the command:\nls -l kubelet\n\nEach kubelet configuration file must be owned by root:root.\n\nIf any manifest file is not owned by root:root, this is a finding."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting kubelet configuration file ownership check (V-242406)"
    
    # Check kubelet process arguments (preserving original logic)
    echo "Checking kubelet process arguments..."
    kubelet_cmd=$(chroot /host ps -ef | grep kubelet | grep -v grep)
    
    if [[ -z "$kubelet_cmd" ]]; then
        echo "FAIL: Kubelet process not found"
        echo "Unable to locate running kubelet process"
        exit 1
    fi
    
    echo "Found kubelet process"
    
    # Dynamically get kubelet config file path (preserving original logic)
    extracted_config=$(echo "$kubelet_cmd" | grep -o -- "--config=[^ ]*" | cut -d= -f2)
    
    if [[ -z "$extracted_config" ]]; then
        config_file="/host/var/lib/kubelet/config.yaml"
        echo "Using default config file: $config_file"
    else
        # Ensure config file has /host prefix if extracted from process
        if [[ ! "$extracted_config" =~ ^/host ]]; then
            config_file="/host$extracted_config"
        else
            config_file="$extracted_config"
        fi
        echo "Found config file from --config argument: $config_file"
    fi
    
    echo "Checking kubelet configuration file: $config_file"
    
    # Check if config file exists (preserving original logic)
    if [[ ! -f "$config_file" ]]; then
        echo "FAIL: Kubelet config file not found"
        echo "Configuration file does not exist at: $config_file"
        echo "Expected kubelet configuration file must exist for ownership verification"
        exit 1
    fi
    
    echo "Found kubelet configuration file"
    echo "Checking file ownership..."
    
    # Check file ownership (preserving original logic)
    owner=$(stat -c %U:%G "$config_file" 2>/dev/null)
    
    if [[ -z "$owner" ]]; then
        echo "FAIL: Unable to read file ownership"
        echo "Cannot determine ownership for: $config_file"
        exit 1
    fi
    
    echo "Current file ownership: $owner"
    echo "Required ownership: root:root"
    
    # Validate ownership (preserving original logic)
    if [[ "$owner" != "root:root" ]]; then
        echo "FAIL: Invalid ownership"
        echo "File: $config_file"
        echo "Current ownership: $owner"
        echo "Required ownership: root:root"
        echo "Kubelet config file must be owned by root:root for security"
        exit 1
    else
        echo "PASS: Kubelet config has correct ownership"
        echo "File: $config_file"
        echo "Ownership: $owner (compliant)"
        exit 0
    fi 