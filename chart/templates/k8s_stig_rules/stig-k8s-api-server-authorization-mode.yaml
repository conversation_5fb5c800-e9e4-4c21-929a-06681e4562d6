apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-authorization-mode
  namespace: compliance-system
spec:
  id: "V-242382"
  title: "The Kubernetes API Server must enable Node,RBAC as the authorization mode."
  description: "To mitigate the risk of unauthorized access to sensitive information by entities that have been issued certificates by DOD-approved PKIs, all DOD systems (e.g., networks, web servers, and web portals) must be properly configured to incorporate access control methods that do not rely solely on the possession of a certificate for access. Successful authentication must not automatically give an entity access to an asset or security boundary. Authorization procedures and controls must be implemented to ensure each authenticated entity also has a validated and current authorization. Authorization is the process of determining whether an entity, once authenticated, is permitted to access a specific asset.\n\nNode,RBAC is the method within Kubernetes to control access of users and applications. Kubernetes uses roles to grant authorization API requests made by kubelets.\n\nSatisfies: SRG-APP-000340-CTR-000770, SRG-APP-000033-CTR-000095, SRG-APP-000378-CTR-000880, SRG-APP-000033-CTR-000090"
  checkText: "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\ngrep -i authorization-mode * \n\nIf the setting authorization-mode is set to \"AlwaysAllow\" in the Kubernetes API Server manifest file or is not configured, this is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--authorization-mode\" to \"Node,RBAC\"."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Checking API Server authorization mode configuration..."
    manifests_dir="/host/etc/kubernetes/manifests"
    echo "Searching for API Server manifest files in: $manifests_dir"
    
    # Check if manifests directory exists
    if [[ ! -d "$manifests_dir" ]]; then
        echo "ERROR: Kubernetes manifests directory not found: $manifests_dir"
        echo "FAIL: Manifests directory missing"
        exit 1
    fi
    
    echo "Found manifests directory"
    
    # Find API Server manifest files
    api_files=$(grep -lis 'kube-apiserver' "$manifests_dir"/* 2>/dev/null)
    
    if [[ -z "$api_files" ]]; then
        echo "ERROR: No API Server manifest files found in $manifests_dir"
        echo "FAIL: No API Server manifest found"
        exit 1
    fi
    
    echo "Found API Server manifest files: $api_files"
    
    # Check each API Server manifest file for authorization mode
    all_configured=true
    violation_files=""
    
    for file in $api_files; do
        echo "Checking file: $(basename "$file")"
        
        # Get authorization mode configuration
        auth_mode=$(grep -i -- "--authorization-mode" "$file" 2>/dev/null)
        
        if [[ -z "$auth_mode" ]]; then
            echo "VIOLATION: authorization-mode not configured in $(basename "$file")"
            all_configured=false
            if [[ -n "$violation_files" ]]; then
                violation_files="${violation_files}, $(basename "$file")"
            else
                violation_files="$(basename "$file")"
            fi
        elif [[ "$auth_mode" =~ "AlwaysAllow" ]]; then
            echo "VIOLATION: AlwaysAllow authorization mode found in $(basename "$file")"
            echo "  Configuration: $auth_mode"
            all_configured=false
            if [[ -n "$violation_files" ]]; then
                violation_files="${violation_files}, $(basename "$file")"
            else
                violation_files="$(basename "$file")"
            fi
        elif ! [[ "$auth_mode" =~ "Node,RBAC" ]]; then
            echo "VIOLATION: Missing Node,RBAC authorization modes in $(basename "$file")"
            echo "  Current configuration: $auth_mode"
            all_configured=false
            if [[ -n "$violation_files" ]]; then
                violation_files="${violation_files}, $(basename "$file")"
            else
                violation_files="$(basename "$file")"
            fi
        else
            echo "OK: Valid authorization mode found in $(basename "$file")"
            echo "  Configuration: $auth_mode"
        fi
    done
    
    # Final result
    if [[ "$all_configured" == false ]]; then
        echo "FAIL: Invalid authorization mode configuration in: $violation_files"
        exit 1
    fi
    
    echo "OK: All API Server manifests have proper authorization modes configured"
    echo "PASS: Authorization modes properly configured"
    exit 0 