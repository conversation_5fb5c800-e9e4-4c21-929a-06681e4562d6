apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-api-server-tls-cert-file
  namespace: compliance-system
spec:
  id: "V-242422"
  title: "Kubernetes API Server must have a certificate for communication."
  description: "Kubernetes control plane and external communication is managed by API Server. The main implementation of the API Server is to manage hardware resources for pods and container using horizontal or vertical scaling. Anyone who can access the API Server can effectively control the Kubernetes architecture. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server with a means to be able to authenticate sessions and encrypt traffic. \n\nTo enable encrypted communication for API Server, the parameter etcd-cafile must be set. This parameter gives the location of the SSL Certificate Authority file used to secure API Server communication."
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i tls-cert-file *\ngrep -i tls-private-key-file *\n\nIf the setting tls-cert-file and private-key-file is not set in the Kubernetes API server manifest file or contains no value, this is a finding."
  fixText: "Edit the Kubernetes API Server manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the value of tls-cert-file and tls-private-key-file to path containing Approved Organizational Certificate."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting API Server TLS certificate configuration check (V-242422)"
    
    manifests_dir="/host/etc/kubernetes/manifests"
    
    echo "Checking for API Server manifest files in: $manifests_dir"
    
    # Check if manifests directory exists
    if [[ ! -d "$manifests_dir" ]]; then
        echo "FAIL: Kubernetes manifests directory not found: $manifests_dir"
        exit 1
    fi
    
    # Find API Server manifest files
    api_files=$(grep -lis 'kube-apiserver' "$manifests_dir"/* 2>/dev/null)
    
    if [[ -z "$api_files" ]]; then
        echo "FAIL: No API Server manifest found in $manifests_dir"
        echo "Expected to find files containing 'kube-apiserver'"
        exit 1
    fi
    
    echo "Found API Server manifest files:"
    echo "$api_files"
    
    # Check each API Server manifest file
    for file in $api_files; do
        echo "Analyzing manifest file: $file"
        
        # Check TLS cert file configuration (preserving original logic)
        cert_line=$(grep -i -- "--tls-cert-file" "$file" 2>/dev/null)
        key_line=$(grep -i -- "--tls-private-key-file" "$file" 2>/dev/null)
        
        echo "Checking for --tls-cert-file configuration..."
        if [[ -z "$cert_line" ]]; then
            echo "FAIL: tls-cert-file missing in $file"
            echo "TLS certificate file must be configured for secure communication"
            exit 1
        fi
        echo "Found tls-cert-file configuration: $cert_line"
        
        echo "Checking for --tls-private-key-file configuration..."
        if [[ -z "$key_line" ]]; then
            echo "FAIL: tls-private-key-file missing in $file"
            echo "TLS private key file must be configured for secure communication"
            exit 1
        fi
        echo "Found tls-private-key-file configuration: $key_line"
        
        echo "Both TLS certificate and private key are properly configured in $file"
    done
    
    echo "PASS: API Server certificates properly configured"
    exit 0 