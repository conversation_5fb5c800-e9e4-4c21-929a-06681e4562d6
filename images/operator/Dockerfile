ARG OPS_DISTROLESS_TAG=12-alauda-202503180545

FROM build-harbor.alauda.cn/ait/go-builder:1.23-alpine AS builder

WORKDIR /workspace
COPY . /workspace

ENV GO111MODULE=on \
    GOMAXPROCS=3 \
    GOPROXY="https://build-nexus.alauda.cn/repository/golang/,direct" \
    GONOSUMDB="gitlab-ce.alauda.cn/*,gomod.alauda.cn/*,bitbucket.org/mathildetech/*"

RUN go mod tidy
RUN CGO_ENABLED=0 go build -ldflags '-s -w' -v -o /bin/manager cmd/manager/main.go
RUN strip /bin/manager

FROM build-harbor.alauda.cn/ops/distroless-static-nonroot:${OPS_DISTROLESS_TAG}

COPY --from=builder /bin/manager /manager

USER 65532:65532

ENTRYPOINT ["/manager"] 