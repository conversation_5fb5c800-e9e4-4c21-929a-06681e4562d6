global:
  chartVersion: v0.0.0-beta.6.gd6558326
  registry:
    address: build-harbor.alauda.cn
    imagePullSecrets:
    # - harbor-secret
    # - docker-registry-secret
  images:
    content:
      code: gitlab-ce.alauda.cn/ait/compliance-operator
      repository: ait/compliance-content
      support_arm: true
      tag: v0.0.0-beta.6.gd6558326
      thirdparty: false
    scanner:
      code: gitlab-ce.alauda.cn/ait/compliance-operator
      repository: ait/compliance-unified-scanner
      support_arm: true
      tag: v0.0.0-beta.6.gd6558326
      thirdparty: false
    report:
      code: gitlab-ce.alauda.cn/ait/compliance-operator
      repository: ait/compliance-report-service
      support_arm: true
      tag: v0.0.0-beta.6.gd6558326
      thirdparty: false
    openscapScanner:
      code: gitlab-ce.alauda.cn/ait/compliance-operator
      repository: ait/compliance-openscap-scanner
      support_arm: true
      tag: v0.0.0-beta.6.gd6558326
      thirdparty: false
    operator:
      code: gitlab-ce.alauda.cn/ait/compliance-operator
      repository: ait/compliance-operator
      support_arm: true
      tag: v0.0.0-beta.6.gd6558326
      thirdparty: false
  complianceNamespace: compliance-system

# Report storage configuration
reportStorage:
  # StorageClass name for OpenSCAP report persistent storage
  # If not set, reports will use emptyDir (temporary storage)
  storageClass: ""
  size: 1Gi
