apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-etcd-file-permissions
  namespace: compliance-system
spec:
  id: "V-242459"
  title: "The Kubernetes etcd must have file permissions set to 644 or more restrictive."
  description: "The Kubernetes etcd key-value store provides a way to store data to the Control Plane. If these files can be changed, data to API object and Control Plane would be compromised."
  checkText: "Review the permissions of the Kubernetes etcd by using the command:\n\nls -AR /var/lib/etcd/*\n\nIf any of the files have permissions more permissive than \"644\", this is a finding."
  fixText: "Change the permissions of the manifest files to \"644\" by executing the command:\n\nchmod -R 644 /var/lib/etcd/*"
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting etcd file permissions check (V-242459)"
    
    # Define etcd data directory (preserving original logic)
    etcd_dir="/host/var/lib/etcd"

    echo "Checking etcd data directory: $etcd_dir"

    # Check if etcd directory exists (preserving original logic)
    if [[ -d "$etcd_dir" ]]; then
        echo "Found etcd data directory"
        echo "Checking file permissions for all files in etcd directory..."
        echo "Required: permissions must be 644 or more restrictive"
        
        # Check permissions of all files (preserving original logic)
        while IFS= read -r file; do
            perm=$(stat -c '%a' "$file" 2>/dev/null)
            
            # Check if permissions are more permissive than 644 (preserving original logic)
            if [[ $perm -gt 644 ]]; then
                echo "FAIL: Excessive etcd file permissions found"
                echo "File: $file"
                echo "Current permissions: $perm"
                echo "Maximum allowed: 644"
                echo "etcd files must have restrictive permissions for security"
                exit 1
            fi
        done < <(find "$etcd_dir" -type f 2>/dev/null)
        
        echo "PASS: All etcd files have compliant permissions"
        exit 0
    else
        echo "FAIL: Etcd directory missing"
        echo "Expected to find etcd data directory at $etcd_dir"
        exit 1
    fi 