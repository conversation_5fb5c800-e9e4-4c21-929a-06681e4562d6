apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-scheduler-secure-binding
  namespace: compliance-system
spec:
  id: "V-242384"
  title: "The Kubernetes Scheduler must have secure binding."
  description: "The Scheduler is a background process that determines which nodes will run pods. The Scheduler must be configured to use a secure binding to ensure that communication between the Scheduler and the API server is encrypted. By using a secure binding, the confidentiality and integrity of the communication is protected."
  checkText: "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i bind-address kube-scheduler.yaml\n\nIf the setting \"--bind-address\" is not set to \"127.0.0.1\" in the Kubernetes Scheduler manifest file, this is a finding."
  fixText: "Edit the Kubernetes Scheduler manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--bind-address\" to \"127.0.0.1\"."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting V-242384: Kubernetes Scheduler secure binding check"
    
    scheduler_file="/host/etc/kubernetes/manifests/kube-scheduler.yaml"
    
    echo "Checking Scheduler manifest file: $scheduler_file"
    
    if [[ -f "$scheduler_file" ]]; then
        echo "Scheduler manifest file found"
        
        echo "Searching for --bind-address parameter..."
        bind_addr=$(grep -i -- "--bind-address" "$scheduler_file" 2>/dev/null | chroot /host awk -F= '{print $2}' 2>/dev/null | tr -d ' ')
        
        if [[ -n "$bind_addr" ]]; then
            echo "Found bind-address setting: $bind_addr"
            
            if [[ "$bind_addr" != "127.0.0.1" ]]; then
                echo "FAIL: Invalid bind-address $bind_addr in Scheduler (should be 127.0.0.1)"
                exit 1
            else
                echo "PASS: Scheduler bind-address is correctly set to 127.0.0.1"
                exit 0
            fi
        else
            echo "FAIL: No --bind-address parameter found in Scheduler manifest"
            exit 1
        fi
    else
        echo "FAIL: Scheduler manifest file not found: $scheduler_file"
        exit 1
    fi 