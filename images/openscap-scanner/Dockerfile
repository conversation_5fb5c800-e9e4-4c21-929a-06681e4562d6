# Use Red Hat UBI8 minimal as base image for better OpenSCAP compatibility
FROM registry.access.redhat.com/ubi8/ubi-minimal

# Install microdnf and basic tools first
RUN microdnf install -y dnf && microdnf clean all

# Install EPEL repository for additional packages
RUN dnf install -y epel-release || \
    dnf install -y https://dl.fedoraproject.org/pub/epel/epel-release-latest-8.noarch.rpm

# Install OpenSCAP and dependencies using dnf
RUN dnf update -y && dnf install -y \
    curl \
    bzip2 \
    ca-certificates \
    vim \
    python3 \
    && dnf clean all

# Try to install OpenSCAP packages from different sources
RUN dnf install -y openscap-scanner || echo "openscap-scanner not available in base repos"
RUN dnf install -y openscap-utils || echo "openscap-utils not available, continuing..."
RUN dnf install -y python3-openscap || echo "python3-openscap not available, continuing..."
RUN dnf install -y scap-security-guide || echo "scap-security-guide not available, continuing..."


# Install kubectl
# Add TARGETARCH argument for multi-arch support
ARG TARGETARCH

# Install kubectl based on target architecture
RUN curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/${TARGETARCH}/kubectl" && \
    chmod +x kubectl && \
    mv kubectl /usr/local/bin/


# 创建必要的目录
RUN mkdir -p /usr/share/openscap/cpe \
    && mkdir -p /opt/openscap/oval \
    && mkdir -p /opt/scripts \
    && mkdir -p /reports \
    && mkdir -p /app \
    && mkdir -p /content


# Create CPE dictionary to fix missing file error
RUN cat > /usr/share/openscap/cpe/openscap-cpe-dict.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<cpe-list xmlns="http://cpe.mitre.org/dictionary/2.0" 
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://cpe.mitre.org/dictionary/2.0 http://cpe.mitre.org/files/cpe-dictionary_2.1.xsd">
  <generator>
    <product_name>OpenSCAP CPE Dictionary</product_name>
    <product_version>1.0</product_version>
    <schema_version>2.0</schema_version>
    <timestamp>2025-07-04T00:00:00</timestamp>
  </generator>
</cpe-list>
EOF

# Pre-download OVAL dependencies during build
RUN echo "=== Pre-downloading OVAL dependencies ===" && \
    curl -L --connect-timeout 30 --max-time 300 \
         "https://ftp.suse.com/pub/projects/security/oval/suse.linux.enterprise.micro.5-patch.xml.bz2" \
         -o "/opt/openscap/oval/suse.linux.enterprise.micro.5-patch.xml.bz2" || echo "SUSE OVAL download failed, continuing..." && \
    curl -L --connect-timeout 30 --max-time 300 \
         "https://www.redhat.com/security/data/oval/v2/RHEL9/rhel-9.oval.xml.bz2" \
         -o "/opt/openscap/oval/com.redhat.rhsa-RHEL9.xml.bz2" || echo "RHEL OVAL download failed, continuing..." && \
    curl -L --connect-timeout 30 --max-time 300 \
         "https://security-metadata.canonical.com/oval/com.ubuntu.jammy.usn.oval.xml.bz2" \
         -o "/opt/openscap/oval/com.ubuntu.jammy.usn.oval.xml.bz2" || echo "Ubuntu OVAL download failed, continuing..." && \
    echo "=== OVAL dependencies download completed ===" && \
    ls -la /opt/openscap/oval/

# Copy scripts
COPY scripts/* /app/

# Make scripts executable
RUN chmod +x /app/*

# Set environment variables for crash prevention and UBI compatibility
ENV OSCAP_CPE_DICT="/usr/share/openscap/cpe/openscap-cpe-dict.xml"
ENV OSCAP_PROBE_OFFLINE="1"
ENV OSCAP_OVAL_OFFLINE="1"
ENV OSCAP_PROBE_RPM_VERIFY_IGNORE_ERRORS="1"
# UBI specific environment variables
ENV OSCAP_PROBE_ROOT="/host"
ENV OSCAP_FULL_VALIDATION="0"

# Verify OpenSCAP installation
RUN echo "=== Verifying OpenSCAP installation ===" && \
    oscap --version && \
    echo "=== Supported specifications ===" && \
    oscap --help | head -20


# Save OpenSCAP version info
RUN oscap --version > /app/scap_version

# Set working directory
WORKDIR /app

# Default command
CMD ["/app/openscap-scanner.sh"]


LABEL \
    name="openscap-ubi9" \
    version="ubi9-latest" \
    description="OpenSCAP scanner based on Red Hat UBI9 for better enterprise compatibility" \
    run="podman run --privileged -v /:/host -e HOSTROOT=/host -e PROFILE=xccdf_org.ssgproject.content_profile_stig -e CONTENT=ssg-slmicro5-ds.xml -e REPORT_DIR=/reports -e CONTENT_IMAGE=registry.alauda.cn:60070/test/compliance/os-content:latest" \
    io.k8s.display-name="OpenSCAP UBI9 container for enterprise node scans" \
    io.k8s.description="OpenSCAP security scanner based on Red Hat UBI9, optimized for SUSE MicroOS and enterprise environments" \
    io.openshift.tags="compliance openscap scan ubi9 enterprise suse-compatible" \
    io.openshift.wants="scap-content" 
