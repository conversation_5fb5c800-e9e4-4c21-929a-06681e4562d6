apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-etcd-cert-file
  namespace: compliance-system
spec:
  id: "V-242428"
  title: "Etcd must configure cert-file"
  description: "The Kubernetes etcd key-value store provides a way to store data to the Control Plane. If these files can be changed, data to API object and Control Plane would be compromised."
  checkText: "Change to the /etc/kubernetes/manifests/ directory on the Kubernetes Control Plane. Run the command:\ngrep -i cert-file etcd.yaml \n\nIf the setting \"cert-file\" is not set in the Kubernetes etcd manifest file, this is a finding."
  fixText: "Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Set the argument \"--cert-file\" to the appropriate certificate file."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    check_id="V-242428"
    title="Etcd must configure cert-file"
    severity="MEDIUM"
    result="PASSED"
    details=""
    manifest="/host/etc/kubernetes/manifests/etcd.yaml"
    required_param="\-\-cert-file"

    if [[ -f "$manifest" ]]; then
        if ! grep -q "$required_param" "$manifest"; then
            result="FAILED"
            details="Missing $required_param configuration"
            ((FAILED++))
        else
            cert_path=$(grep -oP "$required_param=\K[^ ]+" "$manifest")
            # Add /host prefix to certificate path for verification
            host_cert_path="/host$cert_path"
            if [[ ! -f "$host_cert_path" ]]; then
                result="FAILED"
                details="Certificate file not found: $cert_path"
                ((FAILED++))
            fi
        fi
    else
        result="FAILED"
        details="Etcd manifest missing"
        ((FAILED++))
    fi

    # Set success details if passed
    [[ "$result" == "PASSED" ]] && details="Etcd cert-file properly configured" && ((PASSED++))

    # Output results
    echo "Check ID: $check_id"
    echo "Title: $title"
    echo "Severity: $severity"
    echo "Result: $result"
    echo "Details: $details"

    # Exit with appropriate code and message
    if [[ "$result" == "PASSED" ]]; then
        message="SUCCESS: $details"
        echo "$message"
        exit 0
    else
        message="FAILURE: $details"
        echo "$message"
        exit 1
    fi