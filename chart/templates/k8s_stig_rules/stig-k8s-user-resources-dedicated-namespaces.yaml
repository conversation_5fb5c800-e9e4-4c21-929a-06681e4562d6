apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-user-resources-dedicated-namespaces
  namespace: compliance-system
spec:
  id: "V-242383"
  title: "User-managed resources must be created in dedicated namespaces."
  description: "Creating namespaces for user-managed resources is important when implementing Role-Based Access Controls (RBAC). RBAC allows for the authorization of users and helps support proper API server permissions separation and network micro segmentation. If user-managed resources are placed within the default namespaces, it becomes impossible to implement policies for RBAC permission, service account usage, network policies, and more."
  checkText: "To view the available namespaces, run the command:\n\nkubectl get namespaces\n\nThe default namespaces to be validated are default, kube-public, and kube-node-lease if it is created.\n\nFor the default namespace, execute the commands:\n\nkubectl config set-context --current --namespace=default\nkubectl get all\n\nFor the kube-public namespace, execute the commands:\n\nkubectl config set-context --current --namespace=kube-public\nkubectl get all\n\nFor the kube-node-lease namespace, execute the commands:\n\nkubectl config set-context --current --namespace=kube-node-lease\nkubectl get all\n\nThe only valid return values are the kubernetes service (i.e., service/kubernetes) and nothing at all.\n\nIf a return value is returned from the \"kubectl get all\" command and it is not the kubernetes service (i.e., service/kubernetes), this is a finding."
  fixText: "Move any user-managed resources from the default, kube-public, and kube-node-lease namespaces to user namespaces."
  severity: "high"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Checking for user-managed resources in default namespaces..."
    
    # Set context to default namespace
    echo "Setting context to default namespace..."
    kubectl config set-context --current --namespace=default 2>/dev/null
    
    # Check for non-system resources in default namespace
    echo "Checking default namespace for user resources..."
    sys_resources=$(kubectl get all -n default 2>&1 | grep -vE 'service/kubernetes|No resources found|NAME')
    
    if [[ -n "$sys_resources" ]]; then
        echo "FAILED: Non-system resources found in default namespace"
        echo "Found resources:"
        echo "$sys_resources"
        echo "User-managed resources must be moved to dedicated namespaces"
        exit 1
    else
        echo "PASSED: Default namespace isolation maintained"
        echo "No user-managed resources found in default namespace"
        echo "Default namespace contains only system resources (service/kubernetes)"
        exit 0
    fi