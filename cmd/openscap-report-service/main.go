package main

import (
	"archive/zip"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"
)

type ReportInfo struct {
	ScanID    string    `json:"scanId"`
	NodeName  string    `json:"nodeName"`
	Timestamp time.Time `json:"timestamp"`
	Size      int64     `json:"size"`
}

type ReportService struct {
	reportsDir string
}

func NewReportService() *ReportService {
	reportsDir := "/reports"
	if dir := os.Getenv("REPORTS_DIR"); dir != "" {
		reportsDir = dir
	}
	if err := os.MkdirAll(reportsDir, 0755); err != nil {
		log.Fatalf("Failed to create reports directory: %v", err)
	}
	return &ReportService{reportsDir: reportsDir}
}

func (rs *ReportService) uploadHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Parse multipart form
	err := r.ParseMultipartForm(50 << 20) // 50MB max
	if err != nil {
		log.Printf("Failed to parse multipart form: %v", err)
		http.Error(w, "Failed to parse form", http.StatusBadRequest)
		return
	}

	// Get form values
	scanID := r.FormValue("scanId")
	scanName := r.FormValue("scanName")
	nodeName := r.FormValue("nodeName")
	profile := r.FormValue("profile")
	scanner := r.FormValue("scanner")

	if scanID == "" {
		http.Error(w, "scanId is required", http.StatusBadRequest)
		return
	}

	// Get uploaded file
	file, header, err := r.FormFile("report")
	if err != nil {
		log.Printf("Failed to get uploaded file: %v", err)
		http.Error(w, "No file uploaded", http.StatusBadRequest)
		return
	}
	defer file.Close()

	// Create scan directory
	scanDir := filepath.Join(rs.reportsDir, scanID)
	if err := os.MkdirAll(scanDir, 0755); err != nil {
		log.Printf("Failed to create scan directory: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Generate filename
	filename := fmt.Sprintf("report-%s-%d.html", nodeName, time.Now().Unix())
	if nodeName == "" {
		filename = fmt.Sprintf("report-%d.html", time.Now().Unix())
	}
	// Keep original filename if provided
	if header.Filename != "" {
		filename = header.Filename
	}

	filePath := filepath.Join(scanDir, filename)

	// Save uploaded file
	dst, err := os.Create(filePath)
	if err != nil {
		log.Printf("Failed to create file: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
	defer dst.Close()

	size, err := io.Copy(dst, file)
	if err != nil {
		log.Printf("Failed to save file: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	log.Printf("Report uploaded successfully: scanID=%s, scanName=%s, nodeName=%s, profile=%s, scanner=%s, file=%s, size=%d bytes",
		scanID, scanName, nodeName, profile, scanner, filename, size)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":   "success",
		"message":  "Report uploaded successfully",
		"scanId":   scanID,
		"scanName": scanName,
		"nodeName": nodeName,
		"filename": filename,
		"size":     size,
	})
}

func (rs *ReportService) listHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	reports := make(map[string][]ReportInfo)

	// Read all scan directories
	entries, err := os.ReadDir(rs.reportsDir)
	if err != nil {
		log.Printf("Failed to read reports directory: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	for _, entry := range entries {
		if !entry.IsDir() {
			continue
		}

		scanID := entry.Name()
		scanDir := filepath.Join(rs.reportsDir, scanID)

		// Read reports in scan directory
		reportFiles, err := os.ReadDir(scanDir)
		if err != nil {
			log.Printf("Failed to read scan directory %s: %v", scanID, err)
			continue
		}

		var scanReports []ReportInfo
		for _, reportFile := range reportFiles {
			if reportFile.IsDir() || !strings.HasSuffix(reportFile.Name(), ".html") {
				continue
			}

			info, err := reportFile.Info()
			if err != nil {
				continue
			}

			// Extract node name from filename (format: report-nodename-timestamp.html)
			nodeName := ""
			parts := strings.Split(reportFile.Name(), "-")
			if len(parts) >= 3 {
				nodeName = strings.Join(parts[1:len(parts)-1], "-")
			}

			scanReports = append(scanReports, ReportInfo{
				ScanID:    scanID,
				NodeName:  nodeName,
				Timestamp: info.ModTime(),
				Size:      info.Size(),
			})
		}

		if len(scanReports) > 0 {
			reports[scanID] = scanReports
		}
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(reports)
}

func (rs *ReportService) healthHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{
		"status": "healthy",
		"time":   time.Now().Format(time.RFC3339),
	})
}

func (rs *ReportService) downloadHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Extract scanID from URL path /download/{scanId}
	path := strings.TrimPrefix(r.URL.Path, "/download/")
	scanID := strings.TrimSuffix(path, "/")

	if scanID == "" {
		http.Error(w, "Scan ID is required", http.StatusBadRequest)
		return
	}

	scanDir := filepath.Join(rs.reportsDir, scanID)
	if _, err := os.Stat(scanDir); os.IsNotExist(err) {
		http.Error(w, "Scan not found", http.StatusNotFound)
		return
	}

	// Create zip file
	zipFile := filepath.Join("/tmp", scanID+".zip")
	if err := rs.createZipFile(scanDir, zipFile); err != nil {
		log.Printf("Failed to create zip file: %v", err)
		http.Error(w, "Failed to create zip file", http.StatusInternalServerError)
		return
	}
	defer os.Remove(zipFile) // Clean up

	// Serve zip file
	w.Header().Set("Content-Type", "application/zip")
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s.zip", scanID))

	http.ServeFile(w, r, zipFile)
}

func (rs *ReportService) createZipFile(sourceDir, zipPath string) error {
	zipFile, err := os.Create(zipPath)
	if err != nil {
		return err
	}
	defer zipFile.Close()

	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()

	// Walk through source directory
	return filepath.Walk(sourceDir, func(filePath string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip directories and non-HTML files
		if info.IsDir() || !strings.HasSuffix(info.Name(), ".html") {
			return nil
		}

		// Create zip entry
		relPath, err := filepath.Rel(sourceDir, filePath)
		if err != nil {
			return err
		}

		zipEntry, err := zipWriter.Create(relPath)
		if err != nil {
			return err
		}

		// Copy file content
		srcFile, err := os.Open(filePath)
		if err != nil {
			return err
		}
		defer srcFile.Close()

		_, err = io.Copy(zipEntry, srcFile)
		return err
	})
}

func (rs *ReportService) deleteHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodDelete {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Extract scanID from URL path /delete/{scanId}
	path := strings.TrimPrefix(r.URL.Path, "/delete/")
	scanID := strings.TrimSuffix(path, "/")

	if scanID == "" {
		http.Error(w, "Scan ID is required", http.StatusBadRequest)
		return
	}

	scanDir := filepath.Join(rs.reportsDir, scanID)
	if _, err := os.Stat(scanDir); os.IsNotExist(err) {
		http.Error(w, "Scan not found", http.StatusNotFound)
		return
	}

	// Remove the entire scan directory
	if err := os.RemoveAll(scanDir); err != nil {
		log.Printf("Failed to delete scan directory %s: %v", scanID, err)
		http.Error(w, "Failed to delete scan reports", http.StatusInternalServerError)
		return
	}

	log.Printf("Successfully deleted reports for scanID: %s", scanID)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":  "success",
		"message": "Scan reports deleted successfully",
		"scanId":  scanID,
	})
}

func main() {
	rs := NewReportService()

	http.HandleFunc("/upload", rs.uploadHandler)
	http.HandleFunc("/list", rs.listHandler)
	http.HandleFunc("/download/", rs.downloadHandler)
	http.HandleFunc("/delete/", rs.deleteHandler)
	http.HandleFunc("/health", rs.healthHandler)

	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("OpenSCAP Report Service starting on port %s", port)
	log.Printf("Reports directory: %s", rs.reportsDir)
	log.Printf("Available endpoints:")
	log.Printf("  POST   /upload - Upload report (multipart form with scanId, nodeName, etc.)")
	log.Printf("  GET    /list   - List all reports")
	log.Printf("  GET    /download/{scanId} - Download zip file for scan")
	log.Printf("  DELETE /delete/{scanId} - Delete all reports for scan")
	log.Printf("  GET    /health - Health check")

	if err := http.ListenAndServe(":"+port, nil); err != nil {
		log.Fatalf("Server failed to start: %v", err)
	}
}
