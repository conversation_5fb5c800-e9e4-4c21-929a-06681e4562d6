apiVersion: compliance-operator.alauda.io/v1alpha1
kind: Rule
metadata:
  name: stig-k8s-etcd-client-cert-auth
  namespace: compliance-system
spec:
  id: "V-242423"
  title: "Kubernetes etcd must enable client authentication to secure service."
  description: "Kubernetes container and pod configuration are maintained by Kubelet. Kubelet agents register nodes with the API Server, mount volume storage, and perform health checks for containers and pods. Anyone who gains access to Kubelet agents can effectively control applications within the pods and containers. Using authenticity protection, the communication can be protected against man-in-the-middle attacks/session hijacking and the insertion of false information into sessions.\n\nThe communication session is protected by utilizing transport encryption protocols, such as TLS. TLS provides the Kubernetes API Server with a means to be able to authenticate sessions and encrypt traffic.\n\nTo enable encrypted communication for Kubelet, the parameter client-cert-auth must be set. This parameter gives the location of the SSL Certificate Authority file used to secure Kubelet communication."
  checkText: "Change to the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. Run the command:\n\ngrep -i client-cert-auth * \n\nIf the setting client-cert-auth is not configured in the Kubernetes etcd manifest file or set to \"false\", this is a finding."
  fixText: "Edit the Kubernetes etcd manifest file in the /etc/kubernetes/manifests directory on the Kubernetes Control Plane. \n\nSet the value of \"--client-cert-auth\" to \"true\" for the etcd."
  severity: "medium"
  checkType: "node"
  nodeScope: "control-plane"
  checkScript: |
    #!/bin/bash
    
    echo "Starting etcd client certificate authentication check (V-242423)"
    
    # Define manifests directory (preserving original logic)
    manifests_dir="/host/etc/kubernetes/manifests"
    
    echo "Checking for etcd manifest files in: $manifests_dir"
    
    # Find etcd manifest files (preserving original logic)
    etcd_files=$(grep -lis 'name: etcd' "$manifests_dir"/* 2>/dev/null)

    # Check if etcd manifest files exist (preserving original logic)
    if [[ -z "$etcd_files" ]]; then
        echo "FAIL: No etcd manifest found"
        echo "Expected to find etcd manifest files in manifests directory"
        exit 1
    fi

    echo "Found etcd manifest files: $etcd_files"
    echo "Checking --client-cert-auth configuration..."

    # Check each etcd manifest file (preserving original logic)
    for file in $etcd_files; do
        echo "Checking file: $file"
        
        # Check client-cert-auth setting (preserving original logic)
        if ! grep -iq -- "\-\-client-cert-auth=true" "$file" 2>/dev/null; then
            echo "FAIL: client-cert-auth validation failed in $file"
            
            # Check if explicitly disabled or missing (preserving original logic)
            if grep -iq -- "\-\-client-cert-auth=false" "$file" 2>/dev/null; then
                echo "client-cert-auth is explicitly disabled in $file"
                echo "Client certificate authentication must be enabled for security"
            else
                echo "client-cert-auth is not configured in $file"
                echo "Missing --client-cert-auth=true parameter"
            fi
            exit 1
        else
            echo "Found --client-cert-auth=true in $file"
        fi
    done

    echo "PASS: client-cert-auth properly enabled"
    exit 0 